{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "baseUrl": "./",
    "paths": {
      "@tessa-portal/*": ["src/*"]
    },

    /* Bundler mode */
    "noImplicitAny": false,
    "moduleResolution": "Node",
    "esModuleInterop": true,
    "isolatedModules": true,
    "resolveJsonModule": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "types": [
      "./node_modules/@types/react",
      "./node_modules/@telekom/scale-components-react"
    ]
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
