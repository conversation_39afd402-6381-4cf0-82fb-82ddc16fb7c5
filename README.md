# TESSA Portal

A modern web application for managing and interacting with TESSA probes and reservations.

## 🚀 Getting Started

### Prerequisites

- Node.js (v21 or higher)
- npm (comes with Node.js)
- Docker and Docker Compose

### Tech Stack

- **React** with TypeScript for the UI
- **Tailwind CSS** for styling (with `tw-` prefix)
- **Scale Components** (Telekom Design System) for UI components
- **Vite** for build tooling

### Setup and Installation

1. Clone the repository:
```bash
git clone https://gitlab01.its-telekom.eu/firefly/tessa/tessa-portal.git
cd tessa-portal
```

2. Install dependencies:
```bash
npm ci
```

### Running the Application

The application requires both the development server and Docker services to run properly:

1. Start the development server:
```bash
npm run dev
```

2. In a new terminal, start the Docker services:
```bash
cd deploy/local
docker compose up -d
```

The application will be available at `http://localhost:5005`.

This setup includes:
- Nginx reverse proxy routing requests to the development server and other services
- STF WebSocket proxy for device communication
- Hot Module Replacement (HMR) for development
- Dedicated Docker network for services

To stop the Docker services:
```bash
docker compose down
```

### Docker Services Overview

| Service | Description | Purpose |
|---------|-------------|---------|
| tessa-portal | Nginx reverse proxy | Routes requests between development server and other services |
| tessa-portal-stfwsproxy | WebSocket proxy | Handles device communication (ports 8002, 8003) |

#### Proxy Routes

The Nginx proxy handles several routes:
- Main application (proxied to development server)
- STF API and WebSocket connections
- Device screen view
- iOS proxy
- WebSocket connections for Vite HMR


## 💅 Styling

### Tailwind CSS

The project uses Tailwind CSS for styling with a custom prefix `tw-` to avoid conflicts with other CSS frameworks. All Tailwind classes in your HTML/JSX should be prefixed with `tw-`:

```jsx
// Example
<div className="tw-flex tw-items-center tw-p-4">
  <h1 className="tw-text-2xl tw-font-bold">Hello World</h1>
</div>
```

### Scale Components

We use [Scale](https://telekom.github.io/scale/), the Telekom Design System, for our core UI components. Scale provides a comprehensive set of accessible, customizable components that follow Telekom's design guidelines.

```jsx
// Example using Scale components
import { ScaleButton, ScaleCard } from "@telekom/scale-components-react";

function MyComponent() {
  return (
    <ScaleCard>
      <ScaleButton variant="primary">Click me</ScaleButton>
    </ScaleCard>
  );
}
```

For more information on available components and their usage, refer to:
- [Scale Components Documentation](https://telekom.github.io/scale/)
- [Scale GitHub Repository](https://github.com/telekom/scale)

## 🤝 Contributing

We use conventional commits to maintain a clear and automated versioning system. Please follow these guidelines when contributing:

### Commit Message Format

Each commit message consists of a **header**, a **body** and a **footer**. The header has a special format that includes a **type**, a **scope** and a **subject**:

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

#### Types
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

#### Examples

```
feat(auth): add login functionality
fix(probes): resolve connection timeout issue
docs(readme): update setup instructions
```

### Development Workflow

1. Create a new branch for your feature/fix:
```bash
git checkout -b feat/your-feature
# or
git checkout -b fix/your-bug-fix
```

2. Make your changes and commit using conventional commits

3. Push your changes and create a merge request

4. Ensure CI passes and request a review

### Version Control

The project uses automatic versioning based on conventional commits:
- `feat`: Minor version bump
- `fix`: Patch version bump
- Breaking changes (indicated by `!` or `BREAKING CHANGE` in commit message): Major version bump


## 🛠 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint


## 📚 Additional Resources

- [Vite Documentation](https://vitejs.dev/)
- [React Documentation](https://react.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [Scale Components](https://telekom.github.io/scale/)
- [Conventional Commits](https://www.conventionalcommits.org/)
