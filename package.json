{"name": "tessa-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.67.2", "@tanstack/react-table": "^8.21.2", "@telekom-ods/react-ui-kit": "^1.10.2", "@telekom-ods/react-ui-kit-base": "^1.10.2", "@telekom/scale-components": "^3.0.0-beta.155", "@telekom/scale-components-react": "^3.0.0-beta.155", "axios": "^1.8.2", "clsx": "^2.1.1", "file-saver": "^2.0.5", "iframe-resizer": "^4.4.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "oidc-client-ts": "^3.1.0", "papaparse": "^5.5.2", "prettier-eslint": "^16.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-markdown": "^9.1.0", "react-oidc-context": "^3.2.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.30.0", "styled-components": "^6.1.15", "ts-essentials": "^10.0.4", "use-debounce": "^10.0.4", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/iframe-resizer": "^3.5.13", "@types/lodash": "^4.17.16", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.1.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "typescript-eslint": "^8.26.0", "vite": "^5.4.14", "vite-plugin-svgr": "^4.3.0"}, "overrides": {"@types/react": "^18.3.18"}}