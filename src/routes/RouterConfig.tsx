import {
  createBrowser<PERSON>outer,
  Outlet,
  Params,
  Navigate,
} from "react-router-dom";
import ErrorBoundary from "@tessa-portal/pages/ErrorBoundary/ErrorBoundary";
import Authenticated from "../components/auth/Authenticated";
import { AutoLogout, Logout, RedirectAfterLogin } from "./RouterUtils";
import Homepage from "@tessa-portal/pages/Homepage/Homepage";
import ManualTestingProbes from "@tessa-portal/pages/ManualTesting/Probes";
import ManualTestingMulticontrol from "@tessa-portal/pages/ManualTesting/Multicontrol";
import SubscribersPage from "@tessa-portal/pages/Subscribers/SubscribersPage";
import Locations from "@tessa-portal/pages/Administration/Locations/Locations";
import Probes from "@tessa-portal/pages/Administration/Probes/Probes";
import AdministrationPage from "@tessa-portal/pages/Administration/AdministrationPage";
import AutomationJobs from "@tessa-portal/pages/AutomationJobs/AutomationJobs";
import DefaultLayout from "@tessa-portal/layout/DefaultLayout/DefaultLayout";
import NotFound from "@tessa-portal/pages/NotFound/NotFound";
import DeviceView from "@tessa-portal/pages/AutomationJobs/DeviceView";
import Imprint from "@tessa-portal/pages/Footer/Imprint";
import Disclaimer from "@tessa-portal/pages/Footer/Disclaimer";
import DataPrivacyInformation from "@tessa-portal/pages/Footer/DataPrivacyInformation";
import { ServerStatus } from "@tessa-portal/pages/ServerStatus";
import LoginRedirect from "@tessa-portal/pages/Redirect/LoginRedirect";
import CloseLoginRedirect from "@tessa-portal/pages/Redirect/CloseLoginRedirect";
import IframeLogin from "@tessa-portal/pages/Redirect/IframeLogin";

export const Router = createBrowserRouter([
  {
    errorElement: <ErrorBoundary />,
    children: [
      {
        path: "/",
        element: (
          <RedirectAfterLogin>
            <DefaultLayout sidebar={false} breadcrumbs={false}>
              <Homepage />
            </DefaultLayout>
          </RedirectAfterLogin>
        ),
      },
      {
        path: "/iframe-login",
        element: <IframeLogin />,
      },
      {
        path: "/login-redirect",
        element: <LoginRedirect />,
      },
      {
        path: "/login-redirect-close",
        element: <CloseLoginRedirect />,
      },
      {
        path: "/logout",
        element: <Logout />,
      },
      {
        path: "/imprint",
        element: (
          <DefaultLayout sidebar={false} breadcrumbs={false}>
            <Imprint />
          </DefaultLayout>
        ),
      },
      {
        path: "/disclaimer",
        element: (
          <DefaultLayout sidebar={false} breadcrumbs={false}>
            <Disclaimer />
          </DefaultLayout>
        ),
      },
      {
        path: "/data-privacy-information",
        element: (
          <DefaultLayout sidebar={false} breadcrumbs={false}>
            <DataPrivacyInformation />
          </DefaultLayout>
        ),
      },
      {
        path: "",
        element: (
          <Authenticated
            children={
              <>
                <AutoLogout />
                <DefaultLayout>
                  <Outlet />
                </DefaultLayout>
              </>
            }
          />
        ),
        handle: {
          crumb: () => ({
            name: "TESSA",
          }),
        },
        children: [
          {
            path: "manual-testing",
            element: (
              <Authenticated roles={["admin", "manual"]}>
                <Outlet />
              </Authenticated>
            ),
            handle: {
              crumb: () => ({
                name: "Manual Testing",
              }),
            },
            children: [
              {
                path: "",
                element: <Navigate to="probes" />,
              },
              {
                path: "probes",
                element: <Outlet />,
                handle: {
                  crumb: () => ({
                    name: "Probes",
                    to: `/manual-testing/probes`,
                  }),
                },
                children: [
                  {
                    path: "",
                    element: <ManualTestingProbes />,
                  },
                  {
                    path: ":probeName",
                    element: <ManualTestingProbes />,
                    handle: {
                      crumb: ({ probeName }: Readonly<Params>) => ({
                        name: probeName,
                        to: `/manual-testing/probes/${probeName}`,
                      }),
                    },
                  },
                ],
              },
              {
                path: "multicontrol",
                element: <ManualTestingMulticontrol />,
                handle: {
                  crumb: () => ({
                    name: "Multicontrol",
                    to: `/manual-testing/multicontrol`,
                  }),
                },
              },
            ],
          },
          {
            path: "automation",
            element: (
              <Authenticated roles={["automation", "admin", "expert-testing"]}>
                <Outlet />
              </Authenticated>
            ),
            handle: {
              crumb: () => ({
                name: "Automation",
                to: `/automation`,
              }),
            },
            children: [
              {
                index: true,
                element: <AutomationJobs />,
              },
              {
                path: ":uuid",
                element: <Outlet />,
                handle: {
                  crumb: ({ uuid }: Readonly<Params>) => ({
                    name: `Job ID ${uuid}`,
                    to: `/automation/${uuid}`,
                  }),
                },
                children: [
                  {
                    index: true,
                    element: <AutomationJobs />,
                  },
                  {
                    path: "devices",
                    element: <DeviceView />,
                    handle: {
                      crumb: ({ uuid }: Readonly<Params>) => ({
                        name: "Devices",
                        to: `/automation/${uuid}/devices`,
                      }),
                    },
                  },
                ],
              },
            ],
          },
          {
            path: "subscribers",
            element: (
              <Authenticated roles={["manual", "automation", "admin"]}>
                <SubscribersPage />
              </Authenticated>
            ),
            handle: {
              crumb: () => ({
                name: "Subscribers",
                to: `/subscribers`,
              }),
            },
          },
          {
            path: "server-status",
            element: <ServerStatus />,
            handle: {
              crumb: () => ({
                name: "Server Status",
              }),
            },
          },
          {
            path: "administration",
            element: (
              <Authenticated roles={["admin"]}>
                <Outlet />
              </Authenticated>
            ),
            handle: {
              crumb: () => ({
                name: "Administration",
              }),
            },
            children: [
              {
                path: "",
                element: <Navigate to="locations" />,
              },
              {
                path: "locations",
                element: <Locations />,
                handle: {
                  crumb: () => ({
                    name: "Locations",
                    to: `/administration/locations`,
                  }),
                },
              },
              {
                path: "locations",
                element: <Locations />,
                handle: {
                  crumb: () => ({
                    name: "Locations",
                    to: `/administration/locations`,
                  }),
                },
              },
              {
                path: "probes",
                element: <Probes />,
                handle: {
                  crumb: () => ({
                    name: "Probes",
                    to: `/administration/probes`,
                  }),
                },
              },
              {
                path: "subscribers",
                element: <AdministrationPage />,
                handle: {
                  crumb: () => ({
                    name: "Subscribers",
                    to: `/administration/subscribers`,
                  }),
                },
              },
            ],
          },
          {
            path: "*",
            element: <NotFound />,
            handle: {
              crumb: () => ({
                name: "404 Not Found",
              }),
            },
          },
        ],
      },
    ],
  },
]);
