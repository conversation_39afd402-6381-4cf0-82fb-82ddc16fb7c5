import { PropsWithChildren, useEffect } from "react";
import { Navigate, useNavigate } from "react-router-dom";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import { useHandleLogout } from "@tessa-portal/layout/DefaultLayout/hooks";

export const AutoLogout = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const toast = useToastNotification();

  useEffect(() => {
    const handleTokenExpiration = () => {
      toast.closeAll();
      auth.removeUser().then(() => navigate("/"));
    };
    auth.events.addAccessTokenExpired(handleTokenExpiration);

    return () => {
      auth.events.removeAccessTokenExpired(handleTokenExpiration);
    };
  }, [auth, navigate, toast]);

  return null;
};

export const RedirectAfterLogin = ({ children }: PropsWithChildren) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return children;
  }

  return <Navigate to={`/manual-testing/probes`} />;
};

export const Logout = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const handleLogout = useHandleLogout();

  useEffect(() => {
    if (!auth.isAuthenticated) {
      return;
    }

    handleLogout();
  }, [auth, navigate, handleLogout]);

  return <div>Logging out...</div>;
};
