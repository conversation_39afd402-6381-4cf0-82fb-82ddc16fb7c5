/* eslint-disable @typescript-eslint/no-explicit-any */
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import <PERSON> from "papaparse";

// Export as JSON
export const exportToJSON = (data: any[], fileName: string) => {
  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: "application/json",
  });
  saveAs(blob, `${fileName}.json`);
};

// Export as CSV
export const exportToCSV = (data: any[], fileName: string) => {
  const csv = Papa.unparse(data);
  const blob = new Blob([csv], { type: "text/csv" });
  saveAs(blob, `${fileName}.csv`);
};

// Export as Excel
export const exportToExcel = (data: any[], fileName: string) => {
  // Preprocess data to stringify arrays and objects
  const processedData = data.map((row) =>
    Object.fromEntries(
      Object.entries(row).map(([key, value]) => [
        key,
        Array.isArray(value) ? value.join(", ") : value, // Join array values as a comma-separated string
      ]),
    ),
  );

  const worksheet = XLSX.utils.json_to_sheet(processedData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
  const xlsxBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([xlsxBuffer], { type: "application/octet-stream" });
  saveAs(blob, `${fileName}.xlsx`);
};
