import { ScaleTextField } from "@telekom/scale-components-react";
import { useC<PERSON>roller, UseControllerProps } from "react-hook-form";
import { ComponentProps, forwardRef } from "react";
import StyledScaleTextField from "@tessa-portal/helpers/scale/StyledScaleTextField";

type TextFieldProps = ComponentProps<typeof ScaleTextField> &
  UseControllerProps;

const TextField = forwardRef<HTMLScaleTextFieldElement, TextFieldProps>(
  (
    {
      control,
      defaultValue,
      disabled,
      name,
      rules,
      shouldUnregister,
      helperText: helperTextProp,
      ...props
    },
    ref,
  ) => {
    const {
      field,
      fieldState: { error, invalid },
    } = useController({
      control,
      defaultValue,
      disabled,
      name,
      rules,
      shouldUnregister,
    });

    let helperText: string | undefined;

    if (error) {
      helperText = error.message;
    } else if (helperTextProp) {
      helperText = helperTextProp;
    }

    return (
      <StyledScaleTextField
        disabled={field.disabled}
        helperText={helperText}
        invalid={invalid}
        name={field.name}
        onScale-change={field.onChange}
        onBlur={field.onBlur}
        ref={(instance) => {
          field.ref(instance);
          if (typeof ref === "function") {
            ref(instance);
          } else if (ref) {
            (
              ref as React.MutableRefObject<HTMLScaleTextFieldElement | null>
            ).current = instance;
          }
        }}
        value={field.value}
        {...props}
      />
    );
  },
);

export default TextField;
