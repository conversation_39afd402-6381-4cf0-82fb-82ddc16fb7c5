import { useCallback, useMemo, useState } from "react";
import moment from "moment";
import {
  useGetProbes,
  useGetProbeLocations,
} from "@tessa-portal/hooks/services/probe";
import { useGetReservations } from "@tessa-portal/hooks/services/reservation";
import { Device, Probe } from "@tessa-portal/types/services/probe";
import { Reservation } from "@tessa-portal/types/services/reservations";
import {
  getReservationStatus,
  ReservationStatus,
} from "@tessa-portal/features/manualTesting/helpers/getReservationStatus";
import { User } from "@tessa-portal/types/services/users";
import { useGetUsers } from "../services/user";

export interface ProbeData extends Probe {
  location: string;
  device?: Device;
  reservation?: Reservation;
  reservationStatus: ReservationStatus;
  combinedStatus: "online" | "offline";
  user?: User;
}

export const useGetProbeData = () => {
  const { data: probes, isLoading: isLoadingProbes } = useGetProbes();
  const { data: locations, isLoading: isLoadingProbeLocations } =
    useGetProbeLocations();
  const {
    data: reservationData,
    isLoading: isLoadingReservations,
    refetch: refetchReservations,
  } = useGetReservations();
  const { data: users } = useGetUsers();
  const [forceRenderCount, setForceRenderCount] = useState(0);

  const handleRefetch = useCallback(async () => {
    await refetchReservations();
    setForceRenderCount((val) => val + 1);
  }, [refetchReservations]);

  const reservations = useMemo(() => {
    return reservationData?.filter((res) =>
      moment(res.until).isAfter(moment()),
    );
  }, [reservationData]);

  const tableData = useMemo(() => {
    if (!probes || !locations || !reservationData) {
      return [];
    }

    return probes.reduce<ProbeData[]>((acc, probe) => {
      if (probe.devices?.length === 0) return acc;

      const location =
        locations.find((loc) => loc.country_iso === probe.country_iso)
          ?.country_name || "No location";

      const foundReservation = reservations?.find(
        (res) => probe.probe_name === res.probe_name,
      );

      const reservationStatus = getReservationStatus(
        foundReservation?.from,
        foundReservation?.until,
      );

      const user = users?.find(
        (user) => user.uid === foundReservation?.user_id,
      );

      const isOnline =
        probe.status === "online" &&
        probe.devices?.[0]?.status === "online" &&
        (probe.devices?.[0]?.stf_status === "online" ||
          probe.devices?.[0]?.stf_status === "irrelevant");

      acc.push({
        ...probe,
        location,
        device: probe.devices?.[0],
        reservation: foundReservation,
        combinedStatus: isOnline ? "online" : "offline",
        reservationStatus,
        user: user,
      });

      return acc;
    }, []) as ProbeData[];
  }, [
    probes,
    locations,
    reservationData,
    reservations,
    users,
    forceRenderCount,
  ]);

  return {
    data: tableData,
    isLoading:
      isLoadingProbes || isLoadingProbeLocations || isLoadingReservations,
    refetch: handleRefetch,
  };
};
