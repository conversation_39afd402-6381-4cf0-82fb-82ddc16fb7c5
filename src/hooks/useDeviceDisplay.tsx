import { useContext } from "react";
import { DeviceDisplayContext } from "@tessa-portal/providers/DeviceDisplayProvider/DeviceDisplayProvider";

const useDeviceDisplay = () => {
  const context = useContext(DeviceDisplayContext);
  if (!context) {
    throw new Error(
      "useDeviceDisplay must be used within a DeviceDisplayProvider",
    );
  }
  return context;
};

export default useDeviceDisplay;
