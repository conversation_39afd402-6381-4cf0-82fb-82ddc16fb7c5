import { useMemo } from "react";
import axios, { AxiosRequestConfig } from "axios";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";

const { VITE_BACKEND_BASE_URL } = import.meta.env;

const useAxios = (options?: AxiosRequestConfig) => {
  const baseURL = `${VITE_BACKEND_BASE_URL}`;
  const auth = useAuth();

  const instance = useMemo(
    () =>
      axios.create({
        baseURL,
        ...options,
      }),
    [options, baseURL],
  );

  instance.interceptors.request.use(
    (config) => {
      const token = auth.user?.access_token;
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error),
  );

  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error?.response?.data?.message) {
        return Promise.reject(new Error(error?.response?.data?.message));
      }
      if (error?.response?.data?.error) {
        console.log(error?.response?.data?.error);
        return Promise.reject(new Error(error?.response?.data?.error));
      }

      if (error?.response?.data) {
        return Promise.reject(new Error(error?.response?.data));
      }

      return Promise.reject(error);
    },
  );

  return instance;
};

export default useAxios;
