/* eslint-disable @typescript-eslint/no-explicit-any */
import _ from "lodash";
import { useRef } from "react";
import {
  useGetUserSettings,
  useUpdateUserSettings,
} from "@tessa-portal/hooks/services/user";
import { TableState, Updater } from "@tanstack/react-table";

const addTableSettings = (userSettings: any, tableSettings: any) => {
  if (!userSettings) {
    return {
      tableSettings: [tableSettings],
    };
  }

  const newUserSettings = _.cloneDeep(userSettings);

  const oldTableSettings = _.find(userSettings.tableSettings, [
    "id",
    tableSettings?.id,
  ]);

  if (!_.isEqual(tableSettings, oldTableSettings)) {
    newUserSettings.tableSettings = _.chain(userSettings.tableSettings)
      .reject(["id", tableSettings?.id])
      .concat(tableSettings)
      .value();
  }

  return newUserSettings;
};

export default function useUserTableSettings(
  tableId: any,
  defaultInitialState?: any,
) {
  const latestUserSettingsRef = useRef();

  const getUserSettings = useGetUserSettings();
  const { data: userSettings } = getUserSettings;
  const { mutateAsync: updateUserSettings } = useUpdateUserSettings();

  const tableSettings = _.find(userSettings?.tableSettings, ["id", tableId]);
  const tableInitialState = tableSettings?.initialState ?? defaultInitialState;

  const updateTableSettings = (updater: Updater<TableState>) => {
    const state =
      typeof updater === "function" ? updater(tableInitialState) : updater;

    const newUserSettings = addTableSettings(userSettings, {
      id: tableId,
      version: 1,
      initialState: state,
    });

    if (
      !_.isEqual(userSettings, newUserSettings) &&
      !_.isEqual(latestUserSettingsRef.current, newUserSettings)
    ) {
      updateUserSettings(newUserSettings);

      latestUserSettingsRef.current = newUserSettings;
    }
  };

  return {
    initialState: tableInitialState,
    isFetched: getUserSettings?.isFetched,
    updateTableSettings,
  };
}
