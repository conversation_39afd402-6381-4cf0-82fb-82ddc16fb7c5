import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type UpdateABoardStatusRequest = {
  boardPath: string;
  status: boolean;
};

export const useUpdateBoardStatus = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ boardPath, status }: UpdateABoardStatusRequest) => {
      const response = await axios.put(
        `/sim-manager/api/boards/${boardPath}/status?enabled=${status}`,
      );
      return response.data;
    },
  });
};
