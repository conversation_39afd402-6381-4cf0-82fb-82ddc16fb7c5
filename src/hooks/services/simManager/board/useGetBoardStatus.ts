import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetBoardStatus = (boardPath: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["boardStatus", boardPath],
    queryFn: async () => {
      const response = await axios.get(
        `/sim-manager/api/boards/${boardPath}/status`,
      );
      return response.data;
    },
  });
};
