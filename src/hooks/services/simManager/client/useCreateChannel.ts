import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type CreateChannelRequest = {
  clientName: string;
  deviceName: string;
  channelName: string;
};

export const useCreateChannel = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
      channelName,
    }: CreateChannelRequest) => {
      const response = await axios.post(
        `/sim-manager/api/clients/${clientName}/${deviceName}/${channelName}`,
      );
      return response.data;
    },
  });
};
