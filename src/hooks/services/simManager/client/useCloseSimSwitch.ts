import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type CloseSimSwitchRequest = {
  clientName: string;
  deviceName: string;
  channelName: string;
};

export const useCloseSimSwitch = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
      channelName,
    }: CloseSimSwitchRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/clients/${clientName}/${deviceName}/${channelName}/close`,
      );
      return response.data;
    },
  });
};
