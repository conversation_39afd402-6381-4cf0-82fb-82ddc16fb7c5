import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetClientDevices = (clientName: string) => {
  const axios = useAxios();
  return useQuery({
    queryKey: ["clientDevices", clientName],
    queryFn: async () => {
      const response = await axios.get(
        `/cms/api/clients/${clientName}/devices`,
      );
      return response.data.slice();
    },
  });
};
