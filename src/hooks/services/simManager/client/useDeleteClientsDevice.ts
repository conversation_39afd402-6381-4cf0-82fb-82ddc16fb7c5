import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteClientsDeviceRequest = {
  clientName: string;
  deviceName: string;
};

export const useDeleteClientsDevice = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
    }: DeleteClientsDeviceRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/clients/${clientName}/${deviceName}`,
      );
      return response.data;
    },
  });
};
