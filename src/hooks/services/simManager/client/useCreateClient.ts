import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type CreateClientRequest = {
  clientName: string;
  locationName: string;
};

export const useCreateClient = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ clientName, locationName }: CreateClientRequest) => {
      const response = await axios.post(
        `/sim-manager/api/clients/${clientName}/?location_name=${locationName}`,
      );
      return response.data;
    },
  });
};
