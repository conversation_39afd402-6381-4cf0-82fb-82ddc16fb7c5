import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type ResetClientRequest = {
  clientName: string;
};

export const useResetClient = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ clientName }: ResetClientRequest) => {
      const response = await axios.put(
        `/sim-manager/api/clients/${clientName}/reset`,
      );
      return response.data;
    },
  });
};
