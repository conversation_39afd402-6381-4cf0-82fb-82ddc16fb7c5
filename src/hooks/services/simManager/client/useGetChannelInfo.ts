import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetChannelInfo = (
  clientName: string,
  deviceName: string,
  channelName: string,
) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["channelInfo", clientName, deviceName, channelName],
    queryFn: async () => {
      const response = await axios.get(
        `/sim-manager/api/clients/${clientName}/${deviceName}/${channelName}`,
      );
      return response.data;
    },
  });
};
