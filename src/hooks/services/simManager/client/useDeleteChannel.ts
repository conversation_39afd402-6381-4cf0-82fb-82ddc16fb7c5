import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteChannelRequest = {
  clientName: string;
  deviceName: string;
  channelName: string;
};

export const useDeleteChannel = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
      channelName,
    }: DeleteChannelRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/clients/${clientName}/${deviceName}/${channelName}`,
      );
      return response.data;
    },
  });
};
