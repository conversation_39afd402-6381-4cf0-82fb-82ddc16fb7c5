import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type CreateClientsDeviceRequest = {
  clientName: string;
  deviceName: string;
};

export const useCreateClientsDevice = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
    }: CreateClientsDeviceRequest) => {
      const response = await axios.post(
        `/sim-manager/api/clients/${clientName}/${deviceName}`,
      );
      return response.data;
    },
  });
};
