import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type OpenSimSwitchRequest = {
  clientName: string;
  deviceName: string;
  channelName: string;
};

export const useOpenSimSwitch = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      deviceName,
      channelName,
    }: OpenSimSwitchRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/clients/${clientName}/${deviceName}/${channelName}/open`,
      );
      return response.data;
    },
  });
};
