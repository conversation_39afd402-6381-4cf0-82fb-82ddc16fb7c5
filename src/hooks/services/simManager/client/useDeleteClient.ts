import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteClientRequest = {
  clientName: string;
};

export const useDeleteClient = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ clientName }: DeleteClientRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/clients/${clientName}`,
      );
      return response.data;
    },
  });
};
