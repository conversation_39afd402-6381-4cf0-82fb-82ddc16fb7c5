import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type ChangeClientsLocationRequest = {
  clientName: string;
  locationName: string;
};

export const useChangeClientsLocation = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      clientName,
      locationName,
    }: ChangeClientsLocationRequest) => {
      const response = await axios.patch(
        `/sim-manager/api/clients/${clientName}?location_name=<string>${locationName}`,
      );
      return response.data;
    },
  });
};
