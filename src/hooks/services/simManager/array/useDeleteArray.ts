import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteArrayRequest = {
  arrayName: string;
};

export const useDeleteArray = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ arrayName }: DeleteArrayRequest) => {
      const result = await axios.delete(`/sim-manager/api/arrays/${arrayName}`);
      return result.data;
    },
  });
};
