import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useReloadArrays = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const result = await axios.post("/sim-manager/api/refresh");
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["mappings"] });
      queryClient.invalidateQueries({ queryKey: ["subscribers"] });
    },
  });
};
