import { useQuery } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetArrayStatus = (arrayName: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["arrayStatus", arrayName],
    queryFn: async () => {
      const result = await axios.get(
        `/sim-manager/api/arrays/${arrayName}/status`,
      );
      return result.data;
    },
  });
};
