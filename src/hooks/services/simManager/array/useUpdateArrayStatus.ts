import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type StatusData = {
  name: string;
  enabled: boolean;
};

type UpdateArrayStatusRequest = {
  status: StatusData;
};

export const useUpdateArrayStatus = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ status }: UpdateArrayStatusRequest) => {
      const result = await axios.put(
        `/sim-manager/api/arrays/${status.name}/status`,
        status,
      );
      return result.data;
    },
  });
};
