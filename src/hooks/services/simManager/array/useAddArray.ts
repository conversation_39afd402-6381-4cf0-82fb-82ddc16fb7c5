import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { SimArray } from "@tessa-portal/types/services/simManager";

type AddPoolRequest = {
  array: SimArray;
};

export const useAddArray = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async (data: AddPoolRequest) => {
      const result = await axios.post("/sim-manager/api/arrays", data);
      return result.data;
    },
  });
};
