import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetMappingStatus = (channelPath: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["mappings", channelPath],
    queryFn: async () => {
      const response = await axios.get(
        `/sim-manager/api/mappings/chanel/${channelPath}`,
      );

      return response.data;
    },
  });
};
