import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { SimMapping } from "@tessa-portal/types/services/simManager";
const { VITE_QUERY_REFETCH_INTERVAL } = import.meta.env;

export const useGetMappings = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["mappings"],
    queryFn: async () => {
      const response = await axios.get("/sim-manager/api/mappings");

      return response.data.slice() as SimMapping[];
    },
    refetchInterval: parseInt(VITE_QUERY_REFETCH_INTERVAL),
  });
};
