import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteMappingRequest = {
  probeName: string;
};

export const useDeleteMapping = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName }: DeleteMappingRequest) => {
      const response = await axios.delete(
        `/sim-manager/api/mappings/probe/${probeName}`,
      );

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["mappings"] });
    },
  });
};
