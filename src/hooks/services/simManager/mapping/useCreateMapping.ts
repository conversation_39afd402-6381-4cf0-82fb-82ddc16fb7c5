import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type CreateMappingRequest = {
  probeName: string;
  iccid: string;
};

export const useCreateMapping = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, iccid }: CreateMappingRequest) => {
      const response = await axios.put(
        `/sim-manager/api/mappings/probe/${probeName}`,
        {
          probe_name: probeName,
          iccid,
        },
      );

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["mappings"] });
    },
  });
};
