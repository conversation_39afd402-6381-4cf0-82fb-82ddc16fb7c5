import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type UpdatePoolRequest = {
  poolId: string;
  updatedPool: {
    name: string;
  };
};

export const useUpdatePool = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ poolId, updatedPool }: UpdatePoolRequest) => {
      const response = await axios.patch(
        `/sim-manager/api/pools/${poolId}`,
        updatedPool,
      );

      return response.data;
    },
  });
};
