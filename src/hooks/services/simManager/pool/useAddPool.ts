import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type Pool = {
  name: string;
};

type AddPoolRequest = {
  pool: Pool;
};

export const useAddPool = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ pool }: AddPoolRequest) => {
      const response = await axios.post("/sim-manager/api/pools", pool);

      return response.data;
    },
  });
};
