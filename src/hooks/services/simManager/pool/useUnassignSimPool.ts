import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AssignSimPool = {
  name: string;
  iccid: number;
};

export const useUnassignSimPool = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ name, iccid }: AssignSimPool) => {
      const response = await axios.put(
        `/sim-manager/api/pools/${name}/unassign/${iccid}`,
      );

      return response.data;
    },
  });
};
