import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type DeletePoolRequest = {
  poolName: string;
};

export const useDeletePool = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ poolName }: DeletePoolRequest) => {
      const response = await axios.delete(`/sim-manager/api/pools/${poolName}`);

      return response.data;
    },
  });
};
