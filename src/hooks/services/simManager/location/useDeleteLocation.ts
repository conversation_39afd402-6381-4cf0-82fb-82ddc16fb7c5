import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteLocationRequest = {
  locationName: string;
};

export const useDeleteReservation = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ locationName }: DeleteLocationRequest) => {
      const resposne = await axios.delete(
        `/sim-manager/api/locations/${locationName}`,
      );

      return resposne.data;
    },
  });
};
