import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AddLocationRequest = {
  locationName: string;
};

export const useAddLocation = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ locationName }: AddLocationRequest) => {
      const resposne = await axios.post(
        `/sim-manager/api/locations/${locationName}`,
      );

      return resposne.data;
    },
  });
};
