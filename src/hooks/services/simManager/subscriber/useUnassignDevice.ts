import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type UnassignDeviceRequest = {
  id: number;
  deviceId: number;
};

export const useUnassignDevice = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ id, deviceId }: UnassignDeviceRequest) => {
      const response = await axios.put(
        `/sim-manager/api/subscribers/${id}/unassign/${deviceId}`,
      );
      return response.data;
    },
  });
};
