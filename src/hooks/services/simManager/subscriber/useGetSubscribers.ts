import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Subscriber } from "@tessa-portal/types/services/simManager";

export const useGetSubscribers = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["subscribers"],
    queryFn: async () => {
      const response = await axios.get("/sim-manager/api/subscribers");
      return response.data.slice().reverse() as Subscriber[];
    },
  });
};
