import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Subscriber } from "@tessa-portal/types/services/simManager";

type EditSubscriberRequest = {
  id: number;
  subscriber: Subscriber;
};

export const useEditSubscriber = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, subscriber }: EditSubscriberRequest) => {
      const response = await axios.put(
        `/sim-manager/api/subscribers/${id}`,
        subscriber,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscriber"] });
      queryClient.invalidateQueries({ queryKey: ["subscribers"] });
    },
  });
};
