import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Subscriber } from "@tessa-portal/types/services/simManager";

export const useAddSubscriber = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (subscriber: Subscriber) => {
      const response = await axios.post(
        "/sim-manager/api/subscribers",
        subscriber,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscribers"] });
    },
  });
};
