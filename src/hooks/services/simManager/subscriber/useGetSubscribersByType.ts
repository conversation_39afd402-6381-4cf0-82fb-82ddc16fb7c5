import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetSubscribersByType = (
  sub_type: "mappable" | "static" | "sip" | "fixed_line" | "sims",
) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["subscribers_by_type", sub_type],
    queryFn: async () => {
      const response = await axios.get(
        `/sim-manager/api/subscribers/type/${sub_type}`,
      );
      return response.data.reverse();
    },
  });
};
