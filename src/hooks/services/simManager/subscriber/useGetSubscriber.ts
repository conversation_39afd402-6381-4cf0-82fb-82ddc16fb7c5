import _ from "lodash";
import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Subscriber } from "@tessa-portal/types/services/simManager";

export const useGetSubscriber = (id: number | null) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["subscriber", id],
    queryFn: async () => {
      const response = await axios.get(`/sim-manager/api/subscribers/${id}`);
      return response.data as Subscriber;
    },
    enabled: _.isInteger(id),
  });
};
