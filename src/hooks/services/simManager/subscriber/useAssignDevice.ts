import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AssignDeviceRequest = {
  id: number;
  deviceId: number;
};

export const useAssignDevice = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ id, deviceId }: AssignDeviceRequest) => {
      const response = await axios.put(
        `/sim-manager/api/subscribers/${id}/assign/${deviceId}`,
      );
      return response.data;
    },
  });
};
