import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteSubscriberRequest = {
  id: number;
};

export const useDeleteSubscriber = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ id }: DeleteSubscriberRequest) => {
      const response = await axios.delete(`/sim-manager/api/subscribers/${id}`);
      return response.data;
    },
  });
};
