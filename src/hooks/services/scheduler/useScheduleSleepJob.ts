import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ScheduleSleepJob } from "@tessa-portal/types/services/scheduler";

export const useScheduleSleepJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async (data: ScheduleSleepJob) => {
      const response = await axios.post(
        "/scheduler/api/schedule/sleep_job",
        data,
      );
      return response.data;
    },
  });
};
