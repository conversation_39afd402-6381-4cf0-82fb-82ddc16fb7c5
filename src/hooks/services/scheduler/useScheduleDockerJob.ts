import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ScheduleDockerJob } from "@tessa-portal/types/services/scheduler";

export const useScheduleDockerJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async (data: ScheduleDockerJob) => {
      const response = await axios.post(
        "/scheduler/api/schedule/docker_job",
        data,
      );
      return response.data;
    },
  });
};
