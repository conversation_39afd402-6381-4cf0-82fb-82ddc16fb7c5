import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteQueuedStatus = {
  id: string;
};

export const useDeleteQueuedStatus = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ id }: DeleteQueuedStatus) => {
      const response = await axios.delete(`/scheduler/api/queue/${id}`);
      return response.data;
    },
  });
};
