import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Statuses } from "@tessa-portal/types/services/scheduler";

const { VITE_QUERY_REFETCH_INTERVAL } = import.meta.env;

export const useGetStatuses = () => {
  const axios = useAxios();

  // TODO: fix pagination
  return useQuery({
    queryKey: ["statuses"],
    queryFn: async () => {
      const response = await axios.get(
        "/scheduler/api/statuses?reverse=true&start=0&per_page=200",
      );
      return response.data as Statuses;
    },
    refetchInterval: parseInt(VITE_QUERY_REFETCH_INTERVAL),
  });
};
