import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type UnpauseScheduledJob = {
  scheduleId: number;
};

export const useUnpauseScheduledJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ scheduleId }: UnpauseScheduledJob) => {
      const response = await axios.put(
        `/scheduler/api/schedule/${scheduleId}/unpause`,
      );
      return response.data;
    },
  });
};
