import { useMutation } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type PauseScheduledJob = {
  scheduleId: number;
};

export const usePauseScheduledJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ scheduleId }: PauseScheduledJob) => {
      const response = await axios.put(
        `/scheduler/api/schedule/${scheduleId}/pause`,
      );
      return response.data;
    },
  });
};
