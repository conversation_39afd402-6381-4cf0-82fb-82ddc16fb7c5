import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetFinishedStatuses = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["finishedStatuses"],
    queryFn: async () => {
      const response = await axios.get(
        "/scheduler/api/statuses/finished?reverse=true&start=0&per_page=100",
      );
      return response.data;
    },
  });
};
