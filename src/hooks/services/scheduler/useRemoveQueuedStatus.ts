import { useMutation, useQueryClient } from "@tanstack/react-query";

import useAxios from "@tessa-portal/hooks/useAxios";

type RemoveQueuedStatus = {
  id: string;
};

export const useRemoveQueuedStatus = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id }: RemoveQueuedStatus) => {
      const response = await axios.put(`/scheduler/api/queue/${id}/release`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["queue"] });
    },
  });
};
