import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetOpenStatuses = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["openStatuses"],
    queryFn: async () => {
      const response = await axios.get(
        "/scheduler/api/statuses/open?reverse=true&start=0&per_page=100",
      );
      return response.data;
    },
  });
};
