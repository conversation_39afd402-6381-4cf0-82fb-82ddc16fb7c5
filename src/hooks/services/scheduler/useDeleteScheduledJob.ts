import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteScheduledJob = {
  scheduleId: number;
};

export const useDeleteScheduledJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ scheduleId }: DeleteScheduledJob) => {
      const response = await axios.delete(
        `/scheduler/api/schedule/${scheduleId}`,
      );
      return response.data;
    },
  });
};
