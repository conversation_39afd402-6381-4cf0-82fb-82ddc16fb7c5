import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { QueuedStatuses } from "@tessa-portal/types/services/scheduler";

const { VITE_QUERY_REFETCH_INTERVAL } = import.meta.env;

export const useGetQueuedStatuses = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["queue"],
    queryFn: async () => {
      const response = await axios.get(
        "/scheduler/api/queue?start=0&per_page=3000",
      );
      return response.data as QueuedStatuses;
    },
    refetchInterval: parseInt(VITE_QUERY_REFETCH_INTERVAL),
  });
};
