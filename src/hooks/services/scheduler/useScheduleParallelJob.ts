import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ScheduleParallelJob } from "@tessa-portal/types/services/scheduler";

export const useScheduleParallelJob = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async (data: ScheduleParallelJob) => {
      const response = await axios.post(
        "/scheduler/api/schedule/parallel_job",
        data,
      );
      return response.data;
    },
  });
};
