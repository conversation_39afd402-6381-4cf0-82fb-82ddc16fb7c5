import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { StfUser } from "@tessa-portal/types/services/stf";

const { VITE_BFF_API } = import.meta.env;

export const useGetStfUser = () => {
  const axios = useAxios({ baseURL: VITE_BFF_API });

  // TODO: Fix the response in stf proxy
  return useQuery({
    queryKey: ["stfUser"],
    queryFn: async () => {
      const response = await axios.get("/stf/user", { responseType: "blob" });
      const text = await response.data.text();
      return JSON.parse(text).user as StfUser;
    },
  });
};
