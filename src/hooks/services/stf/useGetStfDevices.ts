import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { StfDevice } from "@tessa-portal/types/services/stf";
const { VITE_BFF_API } = import.meta.env;

export const useGetStfDevices = () => {
  const axios = useAxios({ baseURL: VITE_BFF_API });

  // TODO: Fix the response in stf proxy
  return useQuery({
    queryKey: ["stfDevices"],
    queryFn: async () => {
      const response = await axios.get("/stf/devices", {
        responseType: "blob",
      });
      const text = await response.data.text();
      return JSON.parse(text).devices as StfDevice[];
    },
  });
};
