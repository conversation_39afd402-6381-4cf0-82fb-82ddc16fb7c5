import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetChannelDetails = (probeName: string, channel: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["probeChannelDetails", probeName],
    queryFn: async () => {
      const response = await axios.get(
        `/attenuator/api/probes/${probeName}/channels/${channel}`,
      );
      return response.data;
    },
  });
};
