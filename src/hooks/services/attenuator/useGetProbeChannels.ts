import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetProbeChannels = (probeName: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["attenuatorProbeChannels", probeName],
    queryFn: async () => {
      const response = await axios.get(
        `/attenuator/api/probes/${probeName}/channels`,
      );
      return response.data;
    },
  });
};
