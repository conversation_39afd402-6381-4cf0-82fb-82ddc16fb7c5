import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetAttenuatorDetails = (attenuatorName: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["attenuator", attenuatorName],
    queryFn: async () => {
      const response = await axios.get(
        `/attenuator/api/attenuators/${attenuatorName}`,
      );
      return response.data;
    },
  });
};
