import _ from "lodash";
import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetChannelValues = (probeName?: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["attenuatorProbeOutputs", probeName],
    queryFn: async () => {
      const response = await axios.get(
        `/attenuator/api/probes/${probeName}/outputs`,
      );
      return response.data;
    },
    enabled: !_.isEmpty(probeName),
  });
};
