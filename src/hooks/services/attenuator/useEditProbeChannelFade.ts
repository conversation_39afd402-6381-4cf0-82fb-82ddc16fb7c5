import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type EditChannelValue = {
  probeName: string;
  channel: string;
  value: number | string;
};

export const useEditProbeChannelFade = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ probeName, channel, value }: EditChannelValue) => {
      return axios.patch(
        `/attenuator/api/probes/${probeName}/channels/${channel}/fade/${value}`,
      );
    },
  });
};
