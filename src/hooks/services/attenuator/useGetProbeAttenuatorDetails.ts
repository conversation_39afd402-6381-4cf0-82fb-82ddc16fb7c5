import _ from "lodash";
import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetProbeAttenuatorDetails = (probeName?: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["attenuatorProbeDetails", probeName],
    queryFn: async () => {
      const response = await axios.get(
        `/attenuator/api/probes/${probeName}/attenuator`,
      );
      return response.data;
    },
    enabled: !_.isEmpty(probeName),
  });
};
