import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";

type CreateBulkReservationRequest = {
  filters: [{ probe_name: string; country_iso: string }];
  reservation: ReservationTimeData;
};

export const useCreateBulkReservation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBulkReservationRequest) => {
      return await axios.post(`/reservations/api/bulk`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
  });
};
