import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";

type CreateReservationRequest = {
  probe_name: string;
  reservation: ReservationTimeData;
};

export const useCreateReservation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      probe_name,
      reservation,
    }: CreateReservationRequest) => {
      return await axios.post(
        `/reservations/api/reservations/byProbeName/${probe_name}`,
        {
          ...reservation,
          note: "manual",
        },
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
  });
};
