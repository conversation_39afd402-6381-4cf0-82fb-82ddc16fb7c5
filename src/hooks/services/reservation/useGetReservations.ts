import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Reservation } from "@tessa-portal/types/services/reservations";
import moment from "moment";

const { VITE_QUERY_REFETCH_INTERVAL } = import.meta.env;

export const useGetReservations = () => {
  const axios = useAxios();
  const from = moment().add(-1, "day").toISOString();
  const until = moment().add(7, "day").toISOString();

  // FIXME: This query is not working as expected, useMemo not updated on fresh data
  return useQuery({
    queryKey: ["reservations"],
    queryFn: async () => {
      const response = await axios.get(
        `/reservations/api/reservations?from=${from}&until=${until}&all=true`,
      );
      return response.data.sort(
        (a: Reservation, b: Reservation) =>
          new Date(b.from).getTime() - new Date(a.from).getTime(),
      ) as Reservation[];
    },
    refetchInterval: parseInt(VITE_QUERY_REFETCH_INTERVAL),
  });
};
