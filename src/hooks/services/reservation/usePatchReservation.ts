import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";

type PatchReservationRequest = {
  id: number | string;
  reservation: Partial<ReservationTimeData>;
};

export const usePatchReservation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, reservation }: PatchReservationRequest) => {
      return await axios.patch(
        `/reservations/api/reservation/${id}`,
        reservation,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
  });
};
