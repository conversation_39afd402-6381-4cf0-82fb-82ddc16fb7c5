import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteReservationRequest = {
  id: number;
};

export const useDeleteReservation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id }: DeleteReservationRequest) => {
      return await axios.delete(`/reservations/api/reservation/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
  });
};
