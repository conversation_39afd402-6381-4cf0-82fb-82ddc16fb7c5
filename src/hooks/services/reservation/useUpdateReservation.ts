import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";

type UpdateReservationRequest = {
  id: number | string;
  reservation: ReservationTimeData;
};

export const useUpdateReservation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, reservation }: UpdateReservationRequest) => {
      return await axios.put(
        `/reservations/api/reservation/${id}`,
        reservation,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reservations"] });
    },
  });
};
