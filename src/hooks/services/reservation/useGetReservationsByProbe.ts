import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Reservation } from "@tessa-portal/types/services/reservations";
import moment from "moment";

export const useGetReservationsByProbe = (probeName: string) => {
  const axios = useAxios();
  const from = moment().add(-14, "day").toISOString();
  const until = moment().add(7, "day").toISOString();

  return useQuery({
    queryKey: ["reservations", probeName],
    queryFn: async () => {
      const response = await axios.get(
        `/reservations/api/reservations/byProbeName/${probeName}?from=${from}&until=${until}`,
      );
      return response.data.sort(
        (a: Reservation, b: Reservation) =>
          new Date(b.from).getTime() - new Date(a.from).getTime(),
      ) as Reservation[];
    },
  });
};
