import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteClaimsRequest = {
  groupId: number;
};

export const useDeleteClaims = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ groupId }: DeleteClaimsRequest) => {
      const response = await axios.delete(`/users/api/claims/${groupId}`);
      return response.data;
    },
  });
};
