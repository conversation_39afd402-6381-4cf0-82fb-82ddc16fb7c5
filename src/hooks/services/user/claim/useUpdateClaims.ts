import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Claims } from "@tessa-portal/types/services/user";

type UpdateClaimsRequest = {
  groupId: number;
  claims: Claims;
};

export const useUpdateClaims = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ groupId, claims }: UpdateClaimsRequest) => {
      const response = await axios.put(`/users/api/claims/${groupId}`, claims);
      return response.data;
    },
  });
};
