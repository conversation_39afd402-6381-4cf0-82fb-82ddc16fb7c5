import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { User } from "@tessa-portal/types/services/users";

export const useGetUsers = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await axios.get("users/api/users");
      return response.data.reverse() as User[];
    },
  });
};
