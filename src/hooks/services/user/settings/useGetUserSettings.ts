import _ from "lodash";
import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import useUser from "@tessa-portal/hooks/useUser";

export const useGetUserSettings = () => {
  const axios = useAxios();

  const user = useUser();

  return useQuery({
    queryKey: ["userSettings", user?.uidNumber],
    queryFn: async () => {
      const response = await axios.get(
        `users/api/users/${user?.uidNumber}/settings`,
      );
      return response.data;
    },
    enabled: !_.isNil(user?.uidNumber),
  });
};
