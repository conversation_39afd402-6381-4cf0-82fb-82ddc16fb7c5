/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import useUser from "@tessa-portal/hooks/useUser";

type TableSettings = {
  id: string;
  initialState: any;
  version: string;
};

type UserSettings = {
  tableSettings?: [TableSettings];
  expertTesting?: {
    showAllJobs: boolean;
  };
};

type UpdateUserSettingsRequest = UserSettings;

export const useUpdateUserSettings = () => {
  const axios = useAxios();
  const user = useUser();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateUserSettingsRequest) => {
      const response = await axios.post(
        `/users/api/users/${user?.uidNumber}/settings`,
        data,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userSettings"] });
    },
  });
};
