import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type User = {
  login: string;
  mail: string;
};

type PwdResetRequest = {
  user: User;
};

export const useRequestPwdReset = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ user }: PwdResetRequest) => {
      const response = await axios.post("/users/password/reset", user);
      return response.data;
    },
  });
};
