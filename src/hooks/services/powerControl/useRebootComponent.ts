import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type RebootComponentRequest = {
  uuid: string;
};

export const useRebootComponent = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ uuid }: RebootComponentRequest) => {
      const response = await axios.patch(`/pcs/api/components/${uuid}/reboot`);
      return response.data;
    },
  });
};
