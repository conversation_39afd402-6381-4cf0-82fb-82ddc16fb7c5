import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type HardRestart = {
  probeName: string;
  parameters: any;
};

export const useHardRestart = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ probeName, parameters }: HardRestart) => {
      const response = await axios.post(
        `/cms/api/probes/${probeName}/control/api/arduino/hardrestart`,
        parameters,
      );

      return response.data;
    },
  });
};
