import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type ResetComponentRequest = {
  uuid: string;
};

export const useResetComponent = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ uuid }: ResetComponentRequest) => {
      const response = await axios.patch(`/pcs/api/components/${uuid}/reset`);
      return response.data;
    },
  });
};
