import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetPowerControllers = (component_type: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["powerControllers", component_type],
    queryFn: async () => {
      const response = await axios.get(
        `/pcs/api/component_uuids?type=${component_type}`,
      );
      return response.data;
    },
  });
};
