import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_GIT } = import.meta.env;

export const useGetRepository = (path: string, branch: string, id: number) => {
  const axios = useAxios({ baseURL: VITE_GIT });

  return useQuery({
    queryKey: ["repository", path, branch, id],
    queryFn: async () => {
      const response = await axios.get(
        `/repository?path=${path}&branch=${branch}&id=${id}`,
      );
      return response.data;
    },
  });
};
