import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_GIT } = import.meta.env;

export const useGetRepositoryBranches = (repositoryId: string) => {
  const axios = useAxios({ baseURL: VITE_GIT });

  return useQuery({
    queryKey: ["branches", repositoryId],
    queryFn: async () => {
      const response = await axios.get(`/branches?id=${repositoryId}`);
      return response.data;
    },
  });
};
