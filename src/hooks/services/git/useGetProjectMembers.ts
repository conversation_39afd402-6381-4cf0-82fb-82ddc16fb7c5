import { useQueries } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_GIT } = import.meta.env;

export const useGetProjectMembers = (ids: number[]) => {
  const axios = useAxios({ baseURL: VITE_GIT });

  const queries = ids.map((id) => ({
    queryKey: [`${id}_members`],
    queryFn: () =>
      axios.get(`/members?id=${id}`).then((res) => ({ id, data: res.data[0] })),
  }));

  return useQueries({ queries: queries });
};
