import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_GIT } = import.meta.env;

export const useGetProjects = (topic: string) => {
  const axios = useAxios({ baseURL: VITE_GIT });

  return useQuery({
    queryKey: ["projects", topic],
    queryFn: async () => {
      const response = await axios.get(`/projects/${topic}`);
      return response.data;
    },
  });
};
