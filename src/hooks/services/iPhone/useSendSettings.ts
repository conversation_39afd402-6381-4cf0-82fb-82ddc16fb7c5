import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Settings = {
  probeName: string;
  sessionId: string;
  desiredSettings: {
    settings: {
      mjpegServerScreenshotQuality: number;
      mjpegServerFramerate: number;
      mjpegScalingFactor: number;
    };
  };
};
// desiredSettings example
// { settings:
//   { mjpegServerScreenshotQuality: 5,
//     mjpegServerFramerate: 10,
//     mjpegScalingFactor: 35
//   }
// }

export const useSendSettings = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({ probeName, sessionId, desiredSettings }: Settings) => {
      const response = await axios.post(
        `/c/${probeName}/session/${sessionId}/appium/settings`,
        desiredSettings,
      );

      return response.data;
    },
  });
};
