import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Command = {
  probeName: string;
  command: string;
};

export const useSendCommand = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({ probeName, command }: Command) => {
      const response = await axios.post(`/c/${probeName}/wda/${command}`);
      return response.data;
    },
  });
};
