import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Command = {
  probeName: string;
  sessionId: string;
  command: string;
  coordsData: any;
};
// dragfromtoforduration
// orientation
// tap/null

export const useSendSessionCommand = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({
      probeName,
      command,
      sessionId,
      coordsData,
    }: Command) => {
      const response = await axios.post(
        `/c/${probeName}/session/${sessionId}/wda/${command}`,
        coordsData,
      );
      return response.data;
    },
  });
};
