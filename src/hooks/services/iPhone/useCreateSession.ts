import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { useSendSettings } from "@tessa-portal/hooks/services/iPhone/useSendSettings";

const { VITE_IOS } = import.meta.env;

type Session = {
  probeName: string;
  sessionCaps: { capabilities: any };
};
// capabilities example
// {"capabilities": {}}

export const useCreateSession = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  const { mutateAsync: sendSettings } = useSendSettings();
  const desiredSettings = {
    settings: {
      mjpegServerScreenshotQuality: 5,
      mjpegServerFramerate: 15,
      mjpegScalingFactor: 40,
    },
  };

  return useMutation({
    mutationFn: async ({ probeName, sessionCaps }: Session) => {
      const response = await axios.post(
        `/c/${probeName}/session/`,
        sessionCaps,
      );
      return response.data;
    },
    onSuccess: (data, variables) => {
      sendSettings({
        probeName: variables.probeName,
        sessionId: data?.sessionId,
        desiredSettings,
      });
    },
  });
};
