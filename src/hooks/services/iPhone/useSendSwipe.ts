import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Command = {
  probeName: string;
  sessionId: string;
  coordsData: any;
};
// swipe example
// {"duration": 0.1, "fromX": 200, "fromY": 840,"toX" : 200,"toY" : 410}

export const useSendSwipe = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({ probeName, sessionId, coordsData }: Command) => {
      const response = await axios.post(
        `/c/${probeName}/session/${sessionId}/wda/dragfromtoforduration`,
        coordsData,
      );
      return response.data;
    },
  });
};
