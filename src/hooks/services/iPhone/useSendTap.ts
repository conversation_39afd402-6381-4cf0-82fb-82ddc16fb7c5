import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Command = {
  probeName: string;
  sessionId: string;
  coordsData: any;
};
// tap example
// {"x": 258, "y": 757}

export const useSendTap = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({ probeName, sessionId, coordsData }: Command) => {
      const response = await axios.post(
        `/c/${probeName}/session/${sessionId}/wda/tap`,
        coordsData,
      );
      return response.data;
    },
  });
};
