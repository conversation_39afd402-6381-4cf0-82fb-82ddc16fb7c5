import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
const { VITE_IOS } = import.meta.env;

export const useGetStatus = (probeName: string) => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useQuery({
    queryKey: ["status", probeName],
    queryFn: async () => {
      const response = await axios.get(`/c/${probeName}/status`);
      return response.data;
    },
    staleTime: 0,
    retry: false,
  });
};
