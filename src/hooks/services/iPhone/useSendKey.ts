import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

const { VITE_IOS } = import.meta.env;

type Command = {
  probeName: string;
  sessionId: string;
  keyData: any;
};
// key example
// {"value": ["a"], "frequency": 10}

export const useSendKey = () => {
  const axios = useAxios({ baseURL: VITE_IOS });

  return useMutation({
    mutationFn: async ({ probeName, sessionId, keyData }: Command) => {
      const response = await axios.post(
        `/c/${probeName}/session/${sessionId}/wda/keys`,
        keyData,
      );
      return response.data;
    },
  });
};
