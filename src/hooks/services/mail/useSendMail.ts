import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { MailData } from "@tessa-portal/types/services/mail";

type SendMailRequest = {
  mail: MailData;
};

export const useSendMail = () => {
  const axios = useAxios({ baseURL: "" });

  return useMutation({
    mutationFn: async ({ mail }: SendMailRequest) => {
      const response = await axios.post("/mail/mail", mail);
      return response.data;
    },
  });
};
