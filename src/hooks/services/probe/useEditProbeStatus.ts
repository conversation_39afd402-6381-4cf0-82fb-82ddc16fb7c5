import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ProbeStatus } from "@tessa-portal/types/services/probe";

type EditProbeStatusRequest = {
  probeName: string;
  status: ProbeStatus;
};

export const useEditProbeStatus = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, status }: EditProbeStatusRequest) => {
      return axios.put(`/cms/api/probes/${probeName}/status?value=${status}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
