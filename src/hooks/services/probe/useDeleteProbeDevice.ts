import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Device } from "@tessa-portal/types/services/probe";

type DeleteProbeDevice = {
  probeName: string;
  device: Device;
};

export const useDeleteProbeDevice = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, device }: DeleteProbeDevice) => {
      return axios.delete(
        `/cms/api/probes/${probeName}/devices/${device.device_id}`,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probeDevice"] });
      queryClient.invalidateQueries({ queryKey: ["probeDevices"] });
    },
  });
};
