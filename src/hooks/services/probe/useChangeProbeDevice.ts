import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Device } from "@tessa-portal/types/services/probe";

type ChangeProbeDevice = {
  probe_name: string;
  device: Device;
};

export const useChangeProbeDevice = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probe_name, device }: ChangeProbeDevice) => {
      return axios.patch(
        `/cms/api/probes/${probe_name}/devices/${device.device_id}`,
        device,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probeDevice"] });
      queryClient.invalidateQueries({ queryKey: ["probeDevices"] });
    },
  });
};
