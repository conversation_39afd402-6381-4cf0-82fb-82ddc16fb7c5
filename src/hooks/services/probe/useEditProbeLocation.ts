import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type EditProbeLocationRequest = {
  probeName: string;
  countryIso: string;
};

export const useEditProbeLocation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, countryIso }: EditProbeLocationRequest) => {
      return axios.put(
        `/cms/api/probes/${probeName}/location/assign/${countryIso}`,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
