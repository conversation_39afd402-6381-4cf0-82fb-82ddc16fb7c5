import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type RenameLocationRequestData = {
  country_iso: string;
  country_name: string;
};

export const useRenameProbeLocation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      country_iso,
      country_name,
    }: RenameLocationRequestData) => {
      return axios.patch(`/cms/api/locations/${country_iso}`, { country_name });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
    },
  });
};
