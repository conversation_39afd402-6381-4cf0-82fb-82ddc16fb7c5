import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AddLocationRequest = {
  country_name: string;
  country_iso: string;
};

export const useAddProbeLocation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ country_iso, country_name }: AddLocationRequest) => {
      return axios.post("/cms/api/locations", {
        country_iso,
        country_name,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
    },
  });
};
