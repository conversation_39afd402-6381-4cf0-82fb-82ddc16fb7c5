import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { ProbeLocation } from "@tessa-portal/types/services/probe";

export const useGetProbeLocations = () => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["locations"],
    queryFn: async () => {
      const response = await axios.get("/cms/api/locations");

      return response.data as ProbeLocation[];
    },
  });
};
