import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteLocationRequest = {
  country_iso: string;
};

export const useDeleteProbeLocation = () => {
  const axios = useAxios();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ country_iso }: DeleteLocationRequest) => {
      return axios.delete(`/cms/api/locations/${country_iso}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
    },
  });
};
