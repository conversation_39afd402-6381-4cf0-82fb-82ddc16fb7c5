import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Probe } from "@tessa-portal/types/services/probe";

type CreateProbeAndEditLocationRequest = {
  countryIso: string;
  probe: Probe;
};

export const useCreateProbeAndEditLocation = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      countryIso,
      probe,
    }: CreateProbeAndEditLocationRequest) => {
      const { data: addProbeData } = await axios.post("/cms/api/probes", probe);

      await axios.put(
        `/cms/api/probes/${probe?.probe_name}/location/assign/${countryIso}`,
      );

      return addProbeData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
