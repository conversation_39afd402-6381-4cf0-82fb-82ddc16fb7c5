import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Device } from "@tessa-portal/types/services/probe";

type AddProbeDeviceRequest = {
  probeName: string;
  device: Device;
};

export const useAddProbeDevice = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, device }: AddProbeDeviceRequest) => {
      return axios.post(`/cms/api/probes/${probeName}/devices`, device);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probeDevice"] });
      queryClient.invalidateQueries({ queryKey: ["probeDevices"] });
    },
  });
};
