import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AddMultiplexerRequest = {
  probeName: string;
  deviceId: any;
  multiplexers: any;
};

export const useAddMultiplexer = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      probeName,
      deviceId,
      multiplexers,
    }: AddMultiplexerRequest) => {
      return await axios.post(
        `/cms/api/probes/${probeName}/devices/${deviceId}/multiplexers?multiplexer_serial=${multiplexers.replace("#", "%23")}`,
      );
    },
  });
};
