import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Probe } from "@tessa-portal/types/services/probe";

const { VITE_QUERY_REFETCH_INTERVAL } = import.meta.env;

export const useGetProbes = (format = "long") => {
  const axios = useAxios();

  // get probes with devices, if format !== 'long', then without devices
  return useQuery({
    queryKey: ["probes", format],
    queryFn: async () => {
      const response = await axios.get(`/cms/api/probes?format=${format}`);

      return response.data
        .map((item: Probe) => {
          const status =
            item.status === "defective" ? "maintenance" : item.status;
          return {
            ...item,
            status,
          };
        })
        .sort((a: Probe, b: Probe) =>
          a.probe_name.localeCompare(b.probe_name),
        ) as Probe[];
    },
    refetchInterval: parseInt(VITE_QUERY_REFETCH_INTERVAL),
  });
};
