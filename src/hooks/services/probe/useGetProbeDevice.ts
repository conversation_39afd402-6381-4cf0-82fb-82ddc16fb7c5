import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetProbeDevice = (probeName: string, deviceId: number) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["probeDevice", probeName, deviceId],
    queryFn: async () => {
      const response = await axios.get(
        `/cms/api/probes/${probeName}/devices/${deviceId}`,
      );

      return response.data;
    },
  });
};
