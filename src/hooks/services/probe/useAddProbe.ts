import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Probe } from "@tessa-portal/types/services/probe";

type CreateProbeRequest = {
  probe: Probe;
};

export const useAddProbe = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probe }: CreateProbeRequest) => {
      return axios.post("/cms/api/probes", probe);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
