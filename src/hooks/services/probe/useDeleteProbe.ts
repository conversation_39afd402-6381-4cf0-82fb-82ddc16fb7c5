import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type DeleteProbe = {
  probeName: string;
};

export const useDeleteProbe = () => {
  const axios = useAxios();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName }: DeleteProbe) => {
      return axios.delete(`/cms/api/probes/${probeName}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
