import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useGetProbeDevices = (probeName: string) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["probeDevices", probeName],
    queryFn: async () => {
      const response = await axios.get(`/cms/api/probes/${probeName}/devices`);

      return response.data;
    },
  });
};
