import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Device } from "@tessa-portal/types/services/probe";

type RemoveMultiplexerRequest = {
  probeName: string;
  device: Device;
  multiplexers: any;
};

export const useRemoveMultiplexer = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      probeName,
      device,
      multiplexers,
    }: RemoveMultiplexerRequest) => {
      return axios.delete(
        `/cms/api/probes/${probeName}/devices/${device.device_id}/multiplexers?multiplexer_serial=${multiplexers.replace("#", "%23")}`,
      );
    },
  });
};
