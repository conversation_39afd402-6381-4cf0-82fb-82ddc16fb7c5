import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Device } from "@tessa-portal/types/services/probe";

type EditProbeDeviceRequest = {
  probeName: string;
  device: Device;
};

export const useEditProbeDevice = () => {
  const axios = useAxios();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probeName, device }: EditProbeDeviceRequest) => {
      return axios.put(
        `/cms/api/probes/${probeName}/devices/${device.device_id}`,
        device,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probeDevice"] });
      queryClient.invalidateQueries({ queryKey: ["probeDevices"] });
    },
  });
};
