import { useMutation, useQueryClient } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";
import { Probe } from "@tessa-portal/types/services/probe";

type EditProbeRequest = {
  probeName: string;
  probe: Probe;
};

export const useEditProbe = () => {
  const axios = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ probe, probeName }: EditProbeRequest) => {
      return axios.put(`/cms/api/probes/${probeName}`, probe);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["probes"] });
    },
  });
};
