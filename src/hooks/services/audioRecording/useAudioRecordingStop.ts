import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AudioRecordingStopRequest = {
  probeName: string;
  parameters: object;
};

export const useAudioRecordingStop = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      probeName,
      parameters,
    }: AudioRecordingStopRequest) => {
      return axios.post(
        `/cms/api/probes/${probeName}/control/api/audio/recording/stop`,
        parameters,
      );
    },
  });
};
