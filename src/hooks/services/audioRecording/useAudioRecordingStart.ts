import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type AudioRecordingStartRequest = {
  probeName: string;
  parameters: {
    filename: string;
    audiodevice: string;
    stream: boolean;
    args: {
      [key: string]: number;
    };
  };
};

export const useAudioRecordingStart = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({
      probeName,
      parameters,
    }: AudioRecordingStartRequest) => {
      return axios.post(
        `/cms/api/probes/${probeName}/control/api/audio/recording/start`,
        parameters,
      );
    },
  });
};
