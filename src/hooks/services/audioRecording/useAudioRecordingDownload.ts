import { useQuery } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

export const useAudioRecordingDownload = (
  probeName: string,
  fileName: string,
) => {
  const axios = useAxios();

  return useQuery({
    queryKey: ["download", probeName, fileName],
    queryFn: async () => {
      const response = await axios.get(
        `/cms/api/probes/${probeName}/control/api/audio/recording/${fileName}`,
        {
          responseType: "blob",
        },
      );
      return response.data;
    },
    enabled: fileName !== "",
  });
};
