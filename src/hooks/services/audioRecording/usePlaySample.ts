import { useMutation } from "@tanstack/react-query";
import useAxios from "@tessa-portal/hooks/useAxios";

type PlaySample = {
  probeName: string;
  parameters: any;
};

export const usePlaySample = () => {
  const axios = useAxios();

  return useMutation({
    mutationFn: async ({ probeName, parameters }: PlaySample) => {
      return axios.post(
        `/cms/api/probes/${probeName}/control/api/audio/play`,
        parameters,
      );
    },
  });
};
