import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import MainRouter from "./routes/MainRouter";
import ConfirmProvider from "./providers/ConfirmProvider";
import ToastNotificationProvider from "./providers/ToastNotificationProvider";
import RootErrorBoundaryLayout from "@tessa-portal/layout/RootErrorBoundaryLayout";
import ErrorBoundary from "@tessa-portal/components/common/ErrorBoundary";
import { WindowsManagerProvider } from "./providers/WindowManagerProvider";
import { DeviceDisplayProvider } from "./providers/DeviceDisplayProvider/DeviceDisplayProvider";
import "iframe-resizer/js/iframeResizer.contentWindow";
import queryConfig from "../query.config";
import GlobalStyles from "./styles/GlobalStyles";
import { HelmetProvider } from "react-helmet-async";
import { UniversalAuthProvider } from "./components/auth/UniversalAuthProvider/UniversalAuthProvider";

const queryClient = new QueryClient(queryConfig);

function App() {
  return (
    <ErrorBoundary fallback={<RootErrorBoundaryLayout />}>
      <QueryClientProvider client={queryClient}>
        <UniversalAuthProvider>
          <ToastNotificationProvider>
            <ConfirmProvider>
              <WindowsManagerProvider>
                <DeviceDisplayProvider>
                  <HelmetProvider>
                    <GlobalStyles />
                    <MainRouter />
                  </HelmetProvider>
                </DeviceDisplayProvider>
              </WindowsManagerProvider>
            </ConfirmProvider>
          </ToastNotificationProvider>
        </UniversalAuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
