import { WebStorageStateStore } from "oidc-client-ts";

const { VITE_KEYCLOAK_REALM, VITE_KEYCLOAK_URL } = import.meta.env;

export const oidcConfig = {
  authority: `${VITE_KEYCLOAK_URL}/realms/${VITE_KEYCLOAK_REALM}`,
  client_id: [
    "tessa.its",
    "tessa.its-telekom.eu",
    "tessa.automation.telekom.net",
    "tessa-new.automation.telekom.net",
  ].includes(window.location.hostname)
    ? "tessaprod"
    : "tessadev",
  automaticSilentRenew: false,
  monitorSession: true,
  onSigninCallback: () => {
    window.history.replaceState({}, document.title, window.location.pathname);
  },
  post_logout_redirect_uri: window.location.origin,
  querySessionStatus: console.log,
  redirect_uri: window.location.origin,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
};

export default oidcConfig;
