import { createGlobalStyle } from "styled-components";

const GlobalStyles = createGlobalStyle`
  :root {
    /* --telekom-color-background-canvas: #FFFFFF;
    --telekom-color-background-surface: #F5F5F5; */
    --telekom-color-navigation-surface-subtle: #FaFaFa;
  }

  @media (prefers-color-scheme: dark) {
    :root {
      /* --telekom-color-background-canvas: #1A1A1A;
      --telekom-color-background-surface: #252525; */
      --telekom-color-background-canvas: #18171e;
      --telekom-color-background-surface: #28272d;
      /* --telekom-color-background-surface-subtle: #28272d; */
      --telekom-color-navigation-surface-subtle: #28272d;
      --telekom-color-text-and-icon-inverted-standard: #28272d;
    }
  }
`;

export default GlobalStyles;
