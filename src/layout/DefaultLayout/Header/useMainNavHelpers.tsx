import { ReactNode, useMemo } from "react";

export type NavItemType = {
  hideOnDesktop?: boolean;
  name: ReactNode;
  path: string;
  activePathPattern?: string;
  protected?: boolean;
  roles: string[];
};

export const isNavItemVisible = (
  item: NavItemType,
  isAuthenticated: boolean,
  userRoles?: string[],
): boolean => {
  if (!item.protected) {
    return true;
  }

  if (item.protected && !isAuthenticated) {
    return false;
  }

  if (item.protected && (!item.roles || item.roles.length === 0)) {
    return true;
  }

  if (
    item.protected &&
    item.roles &&
    item.roles.length > 0 &&
    userRoles?.some((role) => item.roles.includes(role))
  ) {
    return true;
  }

  return false;
};

const useMainNavItems = (): NavItemType[] => {
  return useMemo(
    () => [
      {
        name: "Remote Control",
        path: "/remote-control",
        activePathPattern: `/remote-control/*`,
        protected: true,
        roles: ["admin", "manual"],
        featureFlag: "remoteControl",
      },
      {
        name: "Reservations",
        path: "/reservations",
        activePathPattern: `/reservations/*`,
        protected: true,
        roles: ["admin", "manual"],
      },
      {
        name: "Manual Testing",
        path: "/manual-testing",
        activePathPattern: `/manual-testing/*`,
        protected: true,
        roles: ["admin", "manual"],
      },
      {
        name: "Automation Jobs",
        path: "/automation-jobs",
        activePathPattern: `/automation-jobs/*`,
        protected: true,
        roles: ["admin", "automated", "expert"],
        featureFlag: "automationJobs",
      },
      {
        name: "Subscribers",
        path: "/subscribers",
        activePathPattern: `/subscribers/*`,
        protected: true,
        roles: [],
      },
    ],
    [],
  );
};

export default useMainNavItems;
