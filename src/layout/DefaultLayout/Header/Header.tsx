import { ScaleTelekomHeader } from "@telekom/scale-components-react";

import Functions from "../Functions";
import MainNav from "./MainNav";

const Header = () => {
  return (
    <>
      <ScaleTelekomHeader
        app-name="TESSA"
        app-name-link="/"
        meta-nav-aria-label="Meta navigation section"
        meta-nav-external-aria-label="External navigation section"
        lang-switcher-aria-label="Language switcher section"
        main-nav-aria-label="Main navigation"
        logoHref="/"
        slot="header"
        type="slim"
      >
        <MainNav />
        <Functions />
      </ScaleTelekomHeader>
    </>
  );
};

export default Header;
