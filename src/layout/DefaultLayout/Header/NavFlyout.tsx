import { ScaleTelekomNavFlyout } from "@telekom/scale-components-react";
import { ReactNode } from "react";
import styled from "styled-components";

const NavList = styled.ul`
  & li:hover {
    color: var(--scl-color-primary-active);
  }
`;

export const NavFlyout = ({
  children,
  onChange,
  expanded,
}: {
  children: ReactNode;
  onChange?: (expanded: boolean) => void;
  expanded?: boolean;
}) => (
  <ScaleTelekomNavFlyout
    hover
    expanded={expanded}
    onScale-expanded={onChange && ((ev) => onChange(ev.detail.expanded))}
  >
    <div className={"tw-flex tw-justify-center"}>
      <div className={"tw-flex tw-max-w-screen-md tw-grow"}>
        <NavList
          className={
            "tw-max-h-generic-size-24 tw-my-8 tw-flex tw-flex-col tw-flex-wrap tw-gap-8"
          }
        >
          {children}
        </NavList>
      </div>
    </div>
  </ScaleTelekomNavFlyout>
);
