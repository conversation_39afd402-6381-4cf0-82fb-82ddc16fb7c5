import {
  ScaleTelekomNavItem,
  ScaleTelekomNavList,
} from "@telekom/scale-components-react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import useUser from "@tessa-portal/hooks/useUser";
import { Link, matchPath, useLocation } from "react-router-dom";
import useMainNavItems, { isNavItemVisible } from "./useMainNavHelpers";

const MainNav = () => {
  return (
    <ScaleTelekomNavList slot="main-nav" variant="main-nav">
      <MainNavItems />
    </ScaleTelekomNavList>
  );
};

export const MainNavItems = () => {
  const auth = useAuth();
  const { roles } = useUser();
  const { pathname } = useLocation();

  const items = useMainNavItems();

  return items
    ?.filter((item) => isNavItemVisible(item, auth.isAuthenticated, roles))
    ?.map((item, index) => (
      <ScaleTelekomNavItem
        key={item.path + index}
        active={
          matchPath(item.activePathPattern || item.path, pathname)
            ? true
            : undefined
        }
        hideOnDesktop={item.hideOnDesktop}
      >
        <Link to={item.path}>{item.name}</Link>
      </ScaleTelekomNavItem>
    ));
};

export default MainNav;
