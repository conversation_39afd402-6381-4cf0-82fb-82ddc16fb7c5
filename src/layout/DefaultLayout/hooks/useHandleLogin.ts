import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";

export const useHandleLogin = () => {
  const auth = useAuth();

  return async () => {
    try {
      if (auth.signinRedirect) {
        await auth.signinRedirect();
      } else {
        // For iframe context, open login in a new window
        window.open("/login-redirect", "_blank");
      }
    } catch (error) {
      console.error("Error during sign-in redirect:", error);
    }
  };
};
