import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import { useQueryClient } from "@tanstack/react-query";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";

export const useHandleLogout = () => {
  const auth = useAuth();
  const toast = useToastNotification();
  const queryClient = useQueryClient();

  return async () => {
    try {
      toast.closeAll();
      queryClient.clear();

      if (auth.signoutRedirect) {
        auth.signoutRedirect({
          id_token_hint: auth.user?.id_token,
          post_logout_redirect_uri: window.location.origin,
        });
      } else {
        // For iframe context, just clear the state
        await auth.removeUser();
      }
    } catch (error) {
      console.error("Error during sign-out redirect:", error);
    }
  };
};
