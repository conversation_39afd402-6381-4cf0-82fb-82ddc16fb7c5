import { ScaleTelekomNavList } from "@telekom/scale-components-react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import ProfileMenu from "./ProfileMenu";
import { useHandleLogin, useHandleLogout } from "./hooks";

const Functions = () => {
  const auth = useAuth();

  if (auth.isAuthenticated) {
    return (
      <ScaleTelekomNavList
        variant="functions"
        slot="functions"
        alignment="right"
      >
        <FunctionsUserProfile />
      </ScaleTelekomNavList>
    );
  }

  return (
    <ScaleTelekomNavList variant="functions" slot="functions" alignment="right">
      <FunctionsUserProfile />
    </ScaleTelekomNavList>
  );
};

const FunctionsUserProfile = () => {
  const auth = useAuth();
  const userProfile = auth.user?.profile;

  const serviceLinks = [
    {
      type: "serviceLink",
      name: "Edit Account",
      href: "https://auth.its-telekom.eu/realms/wwtc/account/",
      icon: "action-edit",
      target: "_blank",
    },
  ];
  const handleLogin = useHandleLogin();
  const handleLogout = useHandleLogout();

  return (
    <ProfileMenu
      loggedIn={!!auth.user?.profile}
      label={userProfile?.name}
      registerHelperLabel="Need an account?"
      registerLabel="Register now"
      registerUrl="https://jira.telekom.de/plugins/servlet/desk/portal/2242/create/8172"
      loginLabel="Log in"
      loginHandler={handleLogin}
      logoutLabel="Log out"
      logoutHandler={handleLogout}
      serviceName="TESSA"
      serviceDescription="Telekom Evolved System for Service Automation"
      serviceLinks={serviceLinks}
      userInfo={{
        name: userProfile?.name,
        email: userProfile?.email,
      }}
    />
  );
};

export default Functions;
