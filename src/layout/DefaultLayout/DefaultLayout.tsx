import { ReactNode } from "react";
import Footer from "./Footer";
import Sidebar from "./Sidebar/Sidebar";
import TopBar from "./TopBar";

export type DefaultLayoutProps = {
  children: ReactNode;
  breadcrumbs?: boolean;
  sidebar?: boolean;
  heading?: string;
};

export const DefaultLayout = ({
  children,
  breadcrumbs = true,
  sidebar = true,
}: DefaultLayoutProps) => {
  const isInIframe = window.top !== window.self;

  return (
    <div className="tw-flex tw-h-screen tw-w-screen tw-overflow-hidden">
      {!isInIframe && sidebar && <Sidebar />}
      <div className="tw-flex tw-flex-1 tw-flex-col tw-overflow-auto">
        {!isInIframe && <TopBar breadcrumbs={breadcrumbs} />}
        <main className="tw-flex-1">{children}</main>
        {!isInIframe && <Footer />}
      </div>
    </div>
  );
};

export default DefaultLayout;
