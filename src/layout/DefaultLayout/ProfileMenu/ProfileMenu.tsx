import {
  AppNavigationUserMenu,
  ScaleButton,
  ScaleIconUserFileUser,
  ScaleLink,
  ScaleMenuFlyout,
  ScaleMenuFlyoutList,
  //   ScaleTelekomMobileFlyoutCanvas,
  //   ScaleTelekomNavFlyout,
  ScaleTelekomNavItem,
} from "@telekom/scale-components-react";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import { useEffect, useState, useCallback } from "react";

interface NavigationItem {
  type: string;
  [key: string]: unknown;
}

interface UserInfo {
  name?: string;
  email?: string;
}

interface ServiceLink extends NavigationItem {
  name: string;
  href: string;
  icon?: string;
}

interface ProfileMenuProps {
  userMenuDesktopTrigger?: HTMLSpanElement;
  label?: string;
  accessibilityLabel?: string;
  closeMenuAccessibilityLabel?: string;
  serviceName?: string;
  serviceDescription?: string;
  loggedIn?: boolean;
  loginUrl?: string;
  loginLabel?: string;
  userInfo?: UserInfo;
  serviceLinks?: ServiceLink[];
  logoutLabel?: string;
  logoutUrl?: string;
  registerHelperLabel?: string;
  registerLabel?: string;
  registerUrl?: string;
  logoutHandler?: () => void;
  loginHandler?: () => void;
  menuOpen?: boolean;
  onMenuOpenChange?: (isOpen: boolean) => void;
}

export const ProfileMenu = ({
  userMenuDesktopTrigger,
  label,
  accessibilityLabel,
  //   closeMenuAccessibilityLabel,
  serviceName,
  serviceDescription,
  loggedIn,
  loginLabel,
  registerHelperLabel,
  registerLabel,
  registerUrl,
  userInfo,
  serviceLinks = [],
  logoutLabel,
  logoutHandler,
  loginHandler,
  menuOpen: controlledMenuOpen,
  onMenuOpenChange,
}: ProfileMenuProps) => {
  const [internalMenuOpen, setInternalMenuOpen] = useState(false);
  const isMenuOpen = controlledMenuOpen ?? internalMenuOpen;

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (isMenuOpen && event.key === "Escape") {
        userMenuDesktopTrigger?.click();
      }
    },
    [isMenuOpen, userMenuDesktopTrigger],
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  const handleMenuStateChange = (isOpen: boolean) => {
    setInternalMenuOpen(isOpen);
    onMenuOpenChange?.(isOpen);
  };

  const printSignInMenu = () => {
    return (
      <div className="profile-menu-login">
        <strong>{serviceName}</strong>
        <p>{serviceDescription}</p>

        <div className="tw-m-4">
          <StyledScaleButton
            variant="primary"
            size="small"
            onClick={loginHandler}
            className="tw-w-full"
          >
            {loginLabel}
          </StyledScaleButton>
        </div>
        <div className="footer">
          <p>{registerHelperLabel}</p>
          <p>
            <ScaleLink target="_blank" href={registerUrl}>
              {registerLabel}
            </ScaleLink>
          </p>
        </div>
      </div>
    );
  };

  const printProfileTrigger = () => {
    if (!loggedIn) {
      return (
        <ScaleIconUserFileUser selected={isMenuOpen}></ScaleIconUserFileUser>
      );
    }

    return (
      <ScaleIconUserFileUser selected={isMenuOpen}></ScaleIconUserFileUser>
    );
  };

  const buildUserNavigation = (): NavigationItem[] => {
    const divider: NavigationItem[] = [{ type: "divider" }];

    for (const el of serviceLinks) {
      el.type = "item";
    }

    let menu: NavigationItem[] = [];

    if (userInfo) {
      menu = menu.concat({
        type: "userInfo",
        name: userInfo.name,
        email: userInfo.email,
      });
    }

    menu = menu.concat(divider);
    menu = menu.concat(serviceLinks);
    menu = menu.concat(divider);
    // menu = menu.concat(buildLogoutButton());
    return menu;
  };

  const buildDesktopMenuStyles = () => {
    let style =
      ".app-navigation-user-menu { padding: 0 12px; box-sizing: border-box; width: 600px; }";
    style +=
      ".scale-icon { width: 20px; height: 20px; display: flex; align-self: center; }";
    style +=
      ".app-navigation-user-menu__user-info { margin: 1rem !important; }";
    style +=
      ".app-navigation-user-menu__item { margin: 1rem !important; padding: 0 !important; }";

    return style;
  };

  //   const buildMobileMenuStyles = () => {
  //     let style =
  //       ".app-navigation-user-menu__user-info--name { margin-bottom: 0 !important; }";
  //     style += ".scale-icon { width: 20px; height: 20px; }";

  //     return style;
  //   };

  const printLabel = () => {
    if (!loggedIn) {
      return <span className="flyout-label">Log in</span>;
    }
    if (!accessibilityLabel) {
      return <span className="flyout-label">{label}</span>;
    }

    return (
      <div className="flyout-label">
        <span aria-hidden="true">{label}</span>
        <span className="visually-hidden">{accessibilityLabel}</span>
      </div>
    );
  };

  // TODO: extend with mobile menu
  return (
    <div className="scale-telekom-profile-menu">
      <ScaleTelekomNavItem class="user-menu-desktop">
        <ScaleMenuFlyout
          direction="bottom-left"
          onChange={(e: React.FormEvent<HTMLScaleMenuFlyoutElement>) => {
            const target = e.currentTarget;
            if (target.id === "user-menu-desktop") {
              handleMenuStateChange(target.hasAttribute("open"));
            }
          }}
          triggerHasPopup={true}
          aria-expanded={isMenuOpen}
        >
          <a
            href="#"
            slot="trigger"
            role="button"
            aria-controls="user-menu-desktop"
            onClick={(e) => e.preventDefault()}
            className="tw-flex tw-items-center tw-gap-2"
          >
            {printProfileTrigger()}
            {printLabel()}
          </a>
          <ScaleMenuFlyoutList
            id="user-menu-desktop"
            preventFlipVertical={true}
            role="menu"
          >
            {loggedIn ? (
              <span className="tw-flex tw-w-[350px] tw-flex-col tw-justify-center">
                <AppNavigationUserMenu
                  hide={() => {
                    userMenuDesktopTrigger?.click();
                    handleMenuStateChange(false);
                  }}
                  navigation={buildUserNavigation()}
                  styles={buildDesktopMenuStyles()}
                />
                <div className="tw-ml-6 tw-flex tw-justify-start">
                  <ScaleButton
                    variant="secondary"
                    size="small"
                    onClick={logoutHandler}
                  >
                    {logoutLabel}
                  </ScaleButton>
                </div>
              </span>
            ) : (
              <AppNavigationUserMenu
                navigation={[]}
                styles={buildDesktopMenuStyles()}
              >
                {printSignInMenu()}
              </AppNavigationUserMenu>
            )}
          </ScaleMenuFlyoutList>

          <div
            slot="trigger"
            className="user-menu-trigger"
            ref={(el: HTMLElement | null) =>
              (userMenuDesktopTrigger = el || undefined)
            }
          />
        </ScaleMenuFlyout>
      </ScaleTelekomNavItem>
      {/* <ScaleTelekomNavItem className="user-menu-mobile">
        <button>
          {printProfileTrigger()}
          {printLabel()}
        </button>

        <ScaleTelekomNavFlyout variant="mobile">
          <ScaleTelekomMobileFlyoutCanvas
            closeButtonLabel={closeMenuAccessibilityLabel}
          >
            {loggedIn && [
              <AppNavigationUserMenu
                slot="mobile-main-nav"
                navigation={buildUserNavigation()}
                styles={buildMobileMenuStyles()}
              ></AppNavigationUserMenu>,
            ]}

            {!loggedIn && [
              <AppNavigationUserMenu slot="mobile-main-nav" navigation={[]}>
                {printSignInMenu()}
              </AppNavigationUserMenu>,
            ]}
          </ScaleTelekomMobileFlyoutCanvas>
        </ScaleTelekomNavFlyout>
      </ScaleTelekomNavItem> */}
    </div>
  );
};

export default ProfileMenu;
