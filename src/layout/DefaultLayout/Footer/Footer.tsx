import {
  ScaleTelekomFooter,
  ScaleTelekomFooterContent,
} from "@telekom/scale-components-react";

const Footer = () => {
  return (
    <ScaleTelekomFooter slot="footer" type="minimal">
      <ScaleTelekomFooterContent>
        <span slot="notice">© Deutsche Telekom AG</span>
        <ul slot="navigation">
          <li>
            <a href="imprint"> Imprint </a>
          </li>
          <li>
            <a href="data-privacy-information"> Data privacy </a>
          </li>
          <li>
            <a href="disclaimer"> Disclaimer </a>
          </li>
        </ul>
      </ScaleTelekomFooterContent>
    </ScaleTelekomFooter>
  );
};

export default Footer;
