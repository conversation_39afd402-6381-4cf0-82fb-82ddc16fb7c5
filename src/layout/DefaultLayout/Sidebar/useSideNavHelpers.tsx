import styled from "styled-components";
import {
  ScaleIconAlertError,
  ScaleIconContentApplications,
  ScaleIconContentLock,
  ScaleIconDeviceComputer,
  ScaleIconServiceServices,
  ScaleIconUserFileFileCollection,
  ScaleSidebarNavItem,
} from "@telekom/scale-components-react";
import {
  ScaleIconActionLaunch,
  ScaleIconContentSignal,
  ScaleIconContentSimCard,
  ScaleIconDeviceAddDevice,
  ScaleIconDeviceDevicePhone,
  ScaleIconDeviceServer,
  ScaleIconNavigationLocation,
  ScaleIconServiceMaintanance,
  ScaleIconUserFileAdmin,
  ScaleIconUserFileFamilies,
} from "@telekom/scale-components-react";
import { ReactNode, useMemo } from "react";

export const StyledCollapsibleDiv = styled.div`
  .sidebar-nav-collapsible__button {
    width: 100%;
    box-sizing: border-box;
    text-decoration: none;
  }

  .sidebar-nav-collapsible_wrapper {
    border-bottom: 1px solid var(--telekom-color-ui-faint);
  }

  .sidebar-nav-collapsible__button:hover {
    color: var(--color-hover);
  }

  ul {
    & a {
      padding-left: 42px;
    }
  }
`;

export const StyledSideNavItem = styled(ScaleSidebarNavItem)`
  a:hover {
    color: var(--color-hover);
  }
`;

export type NavItemType = {
  hideOnDesktop?: boolean;
  name: string;
  path: string;
  activePathPattern?: string;
  protected?: boolean;
  roles: string[];
  featureFlag?: string;
  icon?: ReactNode;
  children?: NavItemType[];
};

export const isNavItemVisible = (
  item: NavItemType,
  isAuthenticated: boolean,
  userRoles?: string[],
): boolean => {
  if (!item.protected) {
    return true;
  }

  if (item.protected && !isAuthenticated) {
    return false;
  }

  if (item.protected && (!item.roles || item.roles.length === 0)) {
    return true;
  }

  if (
    item.protected &&
    item.roles &&
    item.roles.length > 0 &&
    userRoles?.some((role) => item.roles.includes(role))
  ) {
    return true;
  }

  return false;
};

const useSideNavItems = (): NavItemType[] => {
  return useMemo(
    () => [
      {
        name: "Manual testing",
        path: "/manual-testing",
        activePathPattern: `/manual-testing/*`,
        protected: true,
        roles: ["admin", "manual"],
        icon: <ScaleIconDeviceComputer />,
        children: [
          {
            name: "Probes",
            path: "/manual-testing/probes",
            activePathPattern: `/manual-testing/probes/*`,
            protected: true,
            roles: ["admin", "manual"],
            icon: <ScaleIconDeviceDevicePhone />,
          },
          {
            name: "Multicontrol",
            path: "/manual-testing/multicontrol",
            protected: true,
            roles: ["admin", "manual"],
            icon: <ScaleIconContentApplications />,
          },
        ],
      },
      {
        name: "Automation",
        path: "/automation",
        activePathPattern: `/automation/*`,
        protected: true,
        roles: ["admin", "automation", "expert-testing"],
        icon: <ScaleIconActionLaunch />,
      },
      {
        name: "Subscribers",
        path: "/subscribers",
        activePathPattern: `/subscribers/*`,
        protected: true,
        roles: [],
        icon: <ScaleIconContentSimCard />,
      },
      {
        name: "Help & Support",
        path: "/help",
        activePathPattern: `/help/*`,
        protected: false,
        roles: [],
        icon: <ScaleIconServiceServices />,
        children: [
          {
            name: "Report incident",
            path: "https://jira.telekom.de/plugins/servlet/desk/portal/2242/create/8181",
            protected: false,
            roles: [],
            icon: <ScaleIconAlertError />,
          },
          {
            name: "Request access",
            path: "https://jira.telekom.de/plugins/servlet/desk/portal/2242/create/8172",
            protected: false,
            roles: [],
            icon: <ScaleIconContentLock />,
          },
          {
            name: "Documentation",
            path: "https://wiki.telekom.de/display/GTAFKB/TESSA",
            protected: false,
            roles: [],
            icon: <ScaleIconUserFileFileCollection />,
          },
          {
            name: "Server status",
            path: "/server-status",
            protected: true,
            roles: [],
            icon: <ScaleIconDeviceServer />,
          },
        ],
      },
      {
        name: "Administration",
        path: "/administration",
        activePathPattern: `/administration/*`,
        protected: true,
        roles: ["admin"],
        icon: <ScaleIconUserFileAdmin />,
        children: [
          {
            name: "Locations",
            path: "/administration/locations",
            protected: true,
            roles: ["admin"],
            icon: <ScaleIconNavigationLocation />,
          },
          {
            name: "Probes",
            path: "/administration/probes",
            protected: true,
            roles: ["admin"],
            icon: <ScaleIconDeviceAddDevice />,
          },
          {
            name: "SIM Arrays",
            path: "/administration/sim-arrays",
            protected: true,
            roles: ["admin"],
            icon: (
              <ScaleIconDeviceServer style={{ transform: "rotate(270deg)" }} />
            ),
          },
          {
            name: "Attenuators",
            path: "/administration/attenuators",
            protected: true,
            roles: ["admin"],
            icon: <ScaleIconContentSignal />,
          },
          {
            name: "Users & Groups",
            path: "/administration/users-and-groups",
            protected: true,
            roles: ["admin"],
            icon: <ScaleIconUserFileFamilies />,
          },
          {
            name: "Maintenance",
            path: "/administration/maintenance",
            protected: true,
            roles: ["admin"],
            icon: <ScaleIconServiceMaintanance />,
          },
        ],
      },
    ],
    [],
  );
};

export default useSideNavItems;
