import { ScaleSidebarNav } from "@telekom/scale-components-react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import useUser from "@tessa-portal/hooks/useUser";
import { useLocation } from "react-router-dom";
import useSideNavItems, { isNavItemVisible } from "./useSideNavHelpers";
import SideNavItems from "./SideNavItems";

const SideNav = () => {
  const auth = useAuth();
  const { roles } = useUser();
  const { pathname } = useLocation();

  const items = useSideNavItems();
  const visibleItems = items?.filter((item) =>
    isNavItemVisible(item, auth.isAuthenticated, roles),
  );

  return (
    <ScaleSidebarNav>
      <SideNavItems items={visibleItems} pathname={pathname} />
    </ScaleSidebarNav>
  );
};

export default SideNav;
