import { SidebarNavCollapsible } from "./SidebarNavCollapsible";
import { NavLink } from "react-router-dom";
import { matchPath } from "react-router-dom";
import { NavItemType, StyledSideNavItem } from "./useSideNavHelpers";

interface SideNavItemsProps {
  items: NavItemType[];
  pathname: string;
}

const navLinkStyle = "tw-flex tw-items-center tw-gap-2";

const SideNavItems = ({ items, pathname }: SideNavItemsProps) => {
  return (
    <>
      {items?.map((item, index) => {
        if (item.children && item.children.length > 0) {
          const shouldExpand = item.children.some((child) =>
            matchPath(child.activePathPattern || child.path, pathname),
          );

          return (
            <SidebarNavCollapsible
              key={item.path + index}
              label={item.name}
              icon={item.icon}
              bold
              expanded={shouldExpand}
              active={
                item.activePathPattern &&
                matchPath(item.activePathPattern, pathname)
                  ? true
                  : undefined
              }
            >
              {item.children.map((child, childIndex) => (
                <StyledSideNavItem
                  key={child.path + childIndex}
                  bold
                  active={
                    matchPath(child.activePathPattern || child.path, pathname)
                      ? true
                      : undefined
                  }
                >
                  {child.path.startsWith("http") ? (
                    <a
                      href={child.path}
                      className={navLinkStyle}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {child.icon}
                      <span>{child.name}</span>
                    </a>
                  ) : (
                    <NavLink to={child.path} className={navLinkStyle}>
                      {child.icon}
                      {child.name}
                    </NavLink>
                  )}
                </StyledSideNavItem>
              ))}
            </SidebarNavCollapsible>
          );
        }

        // Regular item with no children
        return (
          <StyledSideNavItem
            key={item.path + index}
            bold
            active={
              matchPath(item.activePathPattern || item.path, pathname)
                ? true
                : undefined
            }
          >
            {item.path.startsWith("http") ? (
              <a
                href={item.path}
                className={navLinkStyle}
                target="_blank"
                rel="noopener noreferrer"
              >
                {item.icon}
                <span>{item.name}</span>
              </a>
            ) : (
              <NavLink to={item.path} className={navLinkStyle}>
                {item.icon}
                <span>{item.name}</span>
              </NavLink>
            )}
          </StyledSideNavItem>
        );
      })}
    </>
  );
};

export default SideNavItems;
