import { ScaleLogoSvg } from "@telekom/scale-components-react";
import SideNav from "./SideNav";

const Sidebar = () => {
  return (
    <div
      className="tw-h-screen tw-w-56 tw-flex-shrink-0 tw-overflow-y-auto"
      style={{
        backgroundColor: "var(--telekom-color-navigation-surface-subtle)",
      }}
    >
      <div className="tw-flex tw-flex-col tw-gap-4">
        <div
          className="tw-justify-left tw-mt-4 tw-flex tw-cursor-pointer tw-items-center tw-gap-4 tw-px-4"
          onClick={() => (window.location.href = "/")}
        >
          <ScaleLogoSvg className="tw-mx-2 tw-w-12" />
          <span className="tw-text-2xl tw-font-bold tw-text-[--telekom-color-primary-standard]">
            TESSA
          </span>
        </div>
        <SideNav />
      </div>
    </div>
  );
};

export default Sidebar;
