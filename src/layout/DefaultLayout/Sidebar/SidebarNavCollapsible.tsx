import {
  useState,
  ReactElement,
  ReactNode,
  MouseEvent,
  KeyboardEvent,
} from "react";
import {
  ScaleIconNavigationCollapseDown,
  ScaleIconNavigationRight,
} from "@telekom/scale-components-react";
import { StyledCollapsibleDiv } from "./useSideNavHelpers";

interface SidebarNavCollapsibleProps {
  label: string;
  icon?: ReactNode;
  href?: string;
  expanded?: boolean;
  active?: boolean;
  bold?: boolean;
  children?: ReactElement[];
}

export const SidebarNavCollapsible = ({
  label,
  icon,
  href = "#",
  expanded,
  bold = true,
  children,
}: SidebarNavCollapsibleProps): ReactElement => {
  const [isExpanded, setIsExpanded] = useState(expanded);

  const handleClick = (event: MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault();
    setIsExpanded(!isExpanded);
  };

  const handleKeydown = (event: KeyboardEvent<HTMLAnchorElement>) => {
    if (event.metaKey || event.ctrlKey || event.shiftKey) {
      return;
    }
    if (event.defaultPrevented) {
      return;
    }
    if (event.code === "Space") {
      event.preventDefault();
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <StyledCollapsibleDiv>
      <div className="sidebar-nav-collapsible_wrapper tw-p-[16px] tw-pl-[24px] tw-pr-[12px]">
        <a
          href={href}
          className="sidebar-nav-collapsible__button tw-font-bold"
          onClick={handleClick}
          onKeyDown={handleKeydown}
          role="button"
          aria-expanded={isExpanded ? "true" : "false"}
        >
          <span className="tw-flex tw-items-center tw-justify-between">
            <span className="tw-flex tw-items-center tw-gap-2">
              {icon}
              {label}
            </span>
            {isExpanded ? (
              <ScaleIconNavigationCollapseDown
                className="tw-text-text-&-icon"
                selected={bold}
                size={20}
              />
            ) : (
              <ScaleIconNavigationRight
                className="tw-text-text-&-icon"
                selected={bold}
                size={20}
              />
            )}
          </span>
        </a>
      </div>
      <ul hidden={!isExpanded}>{children}</ul>
    </StyledCollapsibleDiv>
  );
};
