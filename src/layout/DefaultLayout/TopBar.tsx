import Breadcrumbs from "@tessa-portal/components/common/Breadcrumbs";
import Functions from "./Functions";
import { ScaleLogoSvg } from "@telekom/scale-components-react";

type TopBarProps = {
  breadcrumbs?: boolean;
};

const TopBar = ({ breadcrumbs = true }: TopBarProps) => {
  return (
    <div className="tw-flex tw-items-center tw-justify-between tw-px-4 tw-pt-4">
      <div>
        {location.pathname === "/" && (
          <div
            className="tw-justify-left tw-flex tw-cursor-pointer tw-items-center tw-gap-4 tw-px-4"
            onClick={() => (window.location.href = "/")}
          >
            <ScaleLogoSvg className="tw-mx-2 tw-w-12" />
          </div>
        )}
        <div className="tw-flex tw-flex-col tw-gap-2">
          {breadcrumbs && <Breadcrumbs />}
        </div>
      </div>
      <div>
        <Functions />
      </div>
    </div>
  );
};

export default TopBar;
