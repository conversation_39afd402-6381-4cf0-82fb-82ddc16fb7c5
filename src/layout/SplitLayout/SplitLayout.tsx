import { ReactNode } from "react";
import { ScaleDivider } from "@telekom/scale-components-react";
import clsx from "clsx";

type SplitLayoutProps = {
  left: ReactNode;
  right?: ReactNode;
  leftWidth?: string;
  rightWidth?: string;
};

const SplitLayout = ({
  left,
  right,
  leftWidth,
  rightWidth,
}: SplitLayoutProps) => {
  return (
    <div className="tw-flex tw-w-full">
      {/* Left Section */}
      <div
        className={clsx(
          "tw-p-1",
          right ? "tw-flex-grow-0" : "tw-flex-grow",
          "tw-overflow-auto",
        )}
        style={
          right
            ? {
                minWidth: leftWidth,
                maxWidth: leftWidth,
              }
            : undefined
        }
      >
        {left}
      </div>

      {/* Divider */}
      {left && right && (
        <div className="tw-w-fit">
          <ScaleDivider vertical={true} className="tw-mx-3 tw-h-full" />
        </div>
      )}

      {/* Right Section */}
      {right && (
        <div
          className={clsx(
            "tw-overflow-auto tw-p-2",
            rightWidth ? "tw-flex-grow-0" : "tw-flex-grow",
          )}
          style={{
            minWidth: rightWidth,
            maxWidth: rightWidth,
          }}
        >
          {right}
        </div>
      )}
    </div>
  );
};

export default SplitLayout;
