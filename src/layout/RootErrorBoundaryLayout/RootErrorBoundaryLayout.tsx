import {
  ScaleAppShell,
  ScaleTelekomFooter,
  ScaleTelekomHeader,
} from "@telekom/scale-components-react";
import ErrorBoundaryHero from "@tessa-portal/components/common/ErrorBoundaryHero";

const RootErrorBoundaryLayout = () => {
  return (
    <ScaleAppShell>
      <ScaleTelekomHeader
        app-name="TESSA"
        app-name-link="/"
        logoHref="/"
        slot="header"
      />
      <div className="-tw-ml-8 -tw-mr-8 tw-mb-8">
        <ErrorBoundaryHero />
      </div>
      <ScaleTelekomFooter slot="footer" />
    </ScaleAppShell>
  );
};

export default RootErrorBoundaryLayout;
