import { IdTokenClaims } from "oidc-client-ts";

export interface RawUserProfile extends Partial<IdTokenClaims> {
  email?: string;
  name?: string;
  groups?: string[];
  gidNumbers?: number[];
  family_name?: string;
  given_name?: string;
  preferred_username?: string;
  uidNumber?: string;
  userId?: string;
}

export interface UserProfile extends RawUserProfile {
  isAdmin: boolean;
  roles: string[];
  token?: string;
}
