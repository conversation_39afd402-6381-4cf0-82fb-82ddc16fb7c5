export enum DeviceType {
  ipPhone = "IP_Phone",
  android = "Android",
  analogueModem = "Analogue_Modem",
  ios = "iOS",
  lteModem = "LTE_Modem",
  iot = "IoT",
}

export type ProbeLocation = {
  country_name: string;
  country_iso: string;
};

export type Multiplexer = {
  multiplexer_serial: string;
};

export type ProbeStatus = "defective" | "online" | "offline";

// FIXME: Probe actually doesn't have location field
// it comes from ReservationData type
export type Probe = {
  probe_alias: string;
  probe_name: string;
  platform: string;
  OS: string;
  IP: string;
  VPN: boolean;
  poe: boolean;
  location_latitude?: string;
  location_longitude?: string;
  status?: ProbeStatus;
  country_iso?: string;
  location?: string;
  // created_at?: string;
  // updated_at?: string;
  ras_password?: string;
  ras_port?: string;
  ras_protocol?: string;
  ras_username?: string;
  devices?: Device[];
  uuid: string;
};

export type Device = {
  device_id: number;
  name: string;
  serial: string;
  type: DeviceType;
  adboe: string;
  version: string;
  multiplexers: string[];
  status: "online" | "offline";
  stf_status?: "online" | "offline" | "irrelevant";
  exclusive: boolean;
  rooted: boolean;
  IP: string;
  subscribers?: {
    type: "static" | "fixed_line" | "sip";
    msisdn: string;
  };
  uuid: string;
};
