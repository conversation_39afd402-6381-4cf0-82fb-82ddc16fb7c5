import { DeviceType } from "./probe";

export type DeviceFilters = {
  desired_mapping?: {
    [iccid: string]: string;
  };
  probe_name: string;
};

export type Schedule = {
  type: string;
  value: string | null;
  name: string;
};

export type ScheduleDockerJob = {
  image_tag: string;
  cucumber_args: string;
  reservation_duration: number;
  test_identifier: string;
  schedule?: Schedule;
  filters: DeviceFilters[];
};

export type ScheduleSleepJob = {
  reservation_duration: number;
  user_id: string;
  filters: ProbeFilters[];
};

export type Traces = {
  osix: boolean;
  tcpdump: boolean;
  screencap: boolean;
};

export type ScheduleParallelJob = {
  branch: string;
  testcases: string[];
  project_id: number;
  network: string;
  subscriber_tags: string[];
  schedule?: Schedule;
  traces: Traces;
};

// Resque statuses
export type ProbeFilters = {
  probe_alias: string;
  desired_mapping?: {
    iccid: string;
  };
};

export type OptionsProbes = {
  booking_id: number;
  device: string;
  device_type: DeviceType;
  filter_used: ProbeFilters;
  identifier: string;
  msisdn: string;
  probe_alias: string;
  probe_name: string;
};

export type Options = {
  cucumber_args: string;
  env_id: string;
  env_vars: string;
  filters: ProbeFilters[];
  image_tag: string;
  job_type: string;
  probes: OptionsProbes[];
  reservation_duration: number;
  selenium_grid_url: string;
  test_type: string;
  user_id: string;
};

export type Status = {
  message: string;
  name: "DockerJob" | "SleepJob";
  num: number;
  options: Options;
  status: "working" | "completed" | "killed" | "failed";
  processed: boolean;
  time: number;
  total: number;
  uuid: string;
};

export type Statuses = {
  all: number;
  per_page: number;
  start: number;
  statuses: Status[];
};

export type QueuedStatuses = {
  all: number;
  per_page: number;
  start: number;
  jobs: QueuedStatusJob[];
};

export type QueuedStatusJob = {
  approval_id: string;
  klass: "DockerJob" | "SleepJob";
  arguments: Array<{
    filters: Array<{
      probe_alias: string;
      desired_mapping: {
        iccid: string;
      };
    }>;
    user_id: string;
    reservation_duration: {
      source: string;
      parsedValue: number;
    };
    test_type: string;
    verify_sim_mapping: boolean;
    requires_approval: boolean;
  }>;
  jail_status: {
    reason: string[][];
    details: Array<{
      probe_alias: string;
      desired_mapping: {
        iccid: string;
      };
    }>;
    times_jailed: number;
    jailed_at_first: string;
    jail_time_left: number;
  };
};
