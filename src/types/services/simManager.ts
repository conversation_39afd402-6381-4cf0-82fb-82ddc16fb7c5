import { Device, Probe } from "@tessa-portal/types/services/probe";

export interface Location {
  id: string;
  name: string;
}

export interface SimArray {
  name: string;
  internalIPAddress: string;
  internalIPPort: number;
  maxNumberOfBoards: number;
  externalIPAddress: string;
  externalIPPort: number;
}

export enum SimInsertStatus {
  free,
  inserting,
  inserted,
  removing,
  fail,
}

export type AssignedTo = Pick<
  Device,
  "device_id" | "serial" | "type" | "status" | "name"
> &
  Pick<Probe, "probe_name">;

export interface Subscriber
  extends Record<
    string,
    number | string | undefined | boolean | string[] | AssignedTo
  > {
  iccid?: string;
  id: number;
  msisdn?: string;
  imsi?: string;
  tariff?: string;
  wnw?: string;
  op_wnw?: string;
  psp?: string;
  wholesale_id?: string;
  itg_id?: string;
  secret?: string;
  name?: string;
  origin?: string;
  prepaid?: string;
  type?: "mappable" | "static" | "sip" | "fixed_line";
  sim_type?: string;
  position?: string;
  modified?: string;
  lab: boolean;
  live: boolean;
  tags: string[];
  assigned_to?: AssignedTo;
}

export type SimMapping = {
  channel: {
    path: string;
    ready: boolean;
    active: boolean;
  };
  mapped?: {
    iccid: string;
    path: string;
  };
  using?: {
    iccid: string;
    path: string;
  };
};

export interface CreateSubscriberRequest extends Omit<Subscriber, "id"> {
  iccid?: string; // required on mappable and static
  msisdn?: string; // required on sip and fixed line
  imsi?: string;
  tariff?: string;
  wnw?: string;
  op_wnw?: string;
  psp?: string;
  wholesale_id?: string;
  itg_id?: string;
  secret?: string;
  name?: string;
  origin?: string;
  prepaid?: string;
  type: "mappable" | "static" | "sip" | "fixed_line";
  sim_type?: string;
  position?: string;
  modified?: string;
  lab: boolean;
  live: boolean;
  tags: string[];
}
