export type BrowserApp = {
  id: string;
  name: string;
  selected: boolean;
  system: boolean;
  type: string;
  developer: string;
};

export type Browser = {
  apps: BrowserApp[];
  selected: boolean;
};

export type Battery = {
  health: string;
  level: number;
  scale: number;
  source: string;
  status: string;
  temp: number;
  voltage: number;
};

export type Display = {
  density: number;
  fps: number;
  height: number;
  id: number;
  rotation: number;
  secure: boolean;
  size: number;
  url: string;
  width: number;
  xdpi: number;
  ydpi: number;
  inches?: number;
};

export type Group = {
  class: string;
  id: string;
  lifeTime: {
    start: string;
    stop: string;
  };
  lock: boolean;
  name: string;
  origin: string;
  originName: string;
  owner: {
    email: string;
    name: string;
  };
  repetitions: number;
};

export type Network = {
  connected: boolean;
  failover: boolean;
  roaming: boolean;
  subtype: string | null;
  type: string | null;
};

export type Provider = {
  channel: string;
  name: string;
};

export type Phone = {
  iccid: string | null;
  imei: string | null;
  imsi: string | null;
  network: string | null;
  phoneNumber: string | null;
};

export type CPU = {
  cores: number;
  freq: number;
  name: string;
};

export type Memory = {
  ram: number;
  rom: number;
};

export type StfDevice = {
  abi: string;
  airplaneMode: boolean;
  battery: Battery;
  browser: Browser;
  channel: string;
  cpuPlatform: string;
  createdAt: string;
  display: Display;
  group: Group;
  logs_enabled: boolean;
  manufacturer: string;
  marketName: string;
  model: string;
  network: Network;
  openGLESVersion: string;
  operator: string | null;
  owner: null | { email: string; name: string };
  phone: Phone;
  platform: string;
  presenceChangedAt: string;
  present: boolean;
  product: string;
  provider: Provider;
  ready: boolean;
  remoteConnect: boolean;
  remoteConnectUrl: string | null;
  sdk: string;
  serial: string;
  status: number;
  statusChangedAt: string;
  usage: string | null;
  usageChangedAt?: string;
  version: string;
  name: string;
  releasedAt?: string;
  image?: string;
  cpu: CPU;
  memory: Memory;
  using: boolean;
};

export type UserGroups = {
  lock: boolean;
  quotas: {
    allocated: {
      duration: number;
      number: number;
    };
    consumed: {
      duration: number;
      number: number;
    };
    defaultGroupsDuration: number;
    defaultGroupsNumber: number;
    defaultGroupsRepetitions: number;
    repetitions: number;
  };
  subscribed: string[];
};

export type UserSettings = {
  dateFormat: string;
  deviceListActiveTabs: {
    details: boolean;
    icons: boolean;
  };
  emailAddressSeparator: string;
  lastUsedDevice: string;
};

export type StfUser = {
  createdAt: string;
  email: string;
  group: string;
  groups: UserGroups;
  ip: string;
  lastLoggedInAt: string;
  name: string;
  privilege: string;
  settings: UserSettings;
};
