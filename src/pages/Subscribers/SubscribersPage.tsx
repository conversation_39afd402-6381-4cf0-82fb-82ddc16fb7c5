import { Page } from "../Page";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { ScaleDivider } from "@telekom/scale-components-react";
import { SubscribersContext } from "@tessa-portal/features/subscribers/context/SubscribersContext";
import {
  SubscriberEditor,
  SubscribersTable,
} from "@tessa-portal/features/subscribers";
import { useState } from "react";
import { Subscriber } from "@tessa-portal/types/services/simManager";

const SubscribersPage = () => {
  const [selectedSubscriber, setSelectedSubscriber] = useState<Subscriber>();

  return (
    <Page title="Subscribers" heading="Subscribers">
      <SubscribersContext.Provider
        value={{
          showModal: false,
          newSubscriber: () => null,
          selectedSubscriber,
          setSelectedSubscriber,
        }}
      >
        <PanelGroup direction="horizontal">
          <Panel defaultSize={selectedSubscriber ? 40 : 100}>
            <SubscribersTable />
          </Panel>
          {selectedSubscriber && (
            <>
              <PanelResizeHandle>
                <ScaleDivider vertical className="tw-mx-1" />
              </PanelResizeHandle>
              <Panel>
                <SubscriberEditor key={selectedSubscriber.id} />
              </Panel>
            </>
          )}
        </PanelGroup>
      </SubscribersContext.Provider>
    </Page>
  );
};

export default SubscribersPage;
