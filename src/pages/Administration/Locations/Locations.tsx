import { Page } from "../../Page";
import ProbeLocations from "@tessa-portal/features/administration/locations/components/LocationsGrid";
import {
  useGetProbes,
  useGetProbeLocations,
} from "@tessa-portal/hooks/services/probe";
import { useGetSubscribers } from "@tessa-portal/hooks/services/simManager/subscriber/useGetSubscribers";
import { useGetUsers } from "@tessa-portal/hooks/services/user";

const Locations = () => {
  const { data: probes } = useGetProbes();
  const { data: locations } = useGetProbeLocations();
  const { data: subscribers } = useGetSubscribers();
  const { data: users } = useGetUsers();

  return (
    <Page title="Locations" heading="Locations">
      {probes && locations && subscribers && users && (
        <ProbeLocations
          probes={probes}
          locations={locations}
          subscribers={subscribers}
          users={users}
        />
      )}
    </Page>
  );
};

export default Locations;
