import { Page } from "../../Page";
import SplitLayout from "@tessa-portal/layout/SplitLayout";
import { ProbesContext } from "@tessa-portal/features/administration/probes/context/ProbesContext";
import {
  ProbeEditor,
  ProbesTable,
} from "@tessa-portal/features/administration/probes";
import { useState } from "react";
import { Probe } from "@tessa-portal/types/services/probe";

const Probes = () => {
  const [selectedProbe, setSelectedProbe] = useState<Probe>();

  return (
    <Page title="Probes" heading="Probes">
      <ProbesContext.Provider
        value={{
          showModal: false,
          newProbe: () => null,
          selectedProbe,
          setSelectedProbe,
        }}
      >
        <SplitLayout
          left={<ProbesTable />}
          right={
            selectedProbe && <ProbeEditor key={selectedProbe.probe_name} />
          }
          rightWidth="400px"
        />
      </ProbesContext.Provider>
    </Page>
  );
};

export default Probes;
