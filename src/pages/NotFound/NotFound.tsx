import { useNavigate } from "react-router-dom";
import { H1, H2 } from "@tessa-portal/components/common/Typography";
import { ScaleButton } from "@telekom/scale-components-react";

const NotFound = () => {
  const navigate = useNavigate();

  return (
    <div className="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-start tw-gap-8 tw-p-8">
      <div className="tw-flex tw-flex-col tw-items-center tw-gap-4">
        <H1 className="tw-text-telekom-color-text-and-icon-primary tw-text-6xl tw-font-bold">
          404
        </H1>
        <H2 className="tw-text-telekom-color-text-and-icon tw-text-2xl">
          Page Not Found
        </H2>
        <p className="tw-text-telekom-color-text-and-icon tw-max-w-md tw-text-center">
          The page you are looking for doesn't exist or has been moved.
        </p>
      </div>
      <div className="tw-flex tw-gap-4">
        <ScaleButton variant="secondary" onClick={() => navigate(-1)}>
          Go Back
        </ScaleButton>
        <ScaleButton variant="primary" onClick={() => navigate("/")}>
          Go to Home
        </ScaleButton>
      </div>
    </div>
  );
};

export default NotFound;
