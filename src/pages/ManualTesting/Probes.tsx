import { useCallback, useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import { Page } from "../Page";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import ProbesTable from "@tessa-portal/features/manualTesting/ProbesTable/ProbesTable";
import ProbeView from "@tessa-portal/features/manualTesting/ProbeView/ProbeView";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { ScaleDivider } from "@telekom/scale-components-react";

const Probes = () => {
  const { data, isLoading } = useGetProbeData();
  const { probeName } = useParams();
  const navigate = useNavigate();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const handleRowSelectionChange = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const value =
        typeof updaterOrValue === "function"
          ? updaterOrValue(rowSelection)
          : updaterOrValue;
      const selectedRows = Object.keys(value);
      const selectedRow = selectedRows[0];

      // Temporary disable unselecting a probe
      // if (Object.keys(value).length === 0 && probeName) {
      //   setRowSelection({});
      //   navigate("/manual-testing/reservations");
      //   return;
      // }

      if (selectedRow) {
        setRowSelection(value);
        navigate(`/manual-testing/probes/${selectedRow}`);
      }
    },
    [navigate, rowSelection],
  );

  useEffect(() => {
    setRowSelection(probeName ? { [probeName]: true } : {});
  }, [probeName]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <Page title="Probes" heading="Probes">
      <PanelGroup direction="horizontal">
        <Panel defaultSize={probeName ? 40 : 100}>
          <ProbesTable
            data={data}
            onRowSelectionChange={handleRowSelectionChange}
            rowSelection={rowSelection}
          />
        </Panel>
        {probeName && (
          <>
            <PanelResizeHandle>
              <ScaleDivider vertical className="tw-mx-1" />
            </PanelResizeHandle>
            <Panel>
              <ProbeView probeName={probeName} />
            </Panel>
          </>
        )}
      </PanelGroup>
    </Page>
  );
};

export default Probes;
