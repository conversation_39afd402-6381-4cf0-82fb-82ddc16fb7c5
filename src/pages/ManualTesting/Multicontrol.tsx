import { Page } from "../Page";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import useUser from "@tessa-portal/hooks/useUser";
import { getReservationStatus } from "@tessa-portal/features/manualTesting/helpers/getReservationStatus";
import ProbeControl from "@tessa-portal/features/manualTesting/ProbeView/DeviceControl/DeviceControl";

export const ManualTestingMulticontrol = () => {
  const { data: probeData } = useGetProbeData();
  const { userId, isAdmin } = useUser();

  const reservedProbes = probeData.filter(
    (res) =>
      getReservationStatus(res.reservation?.from, res.reservation?.until) ===
        "Reserved" &&
      (String(res.reservation?.user_id) === userId || isAdmin) &&
      res.device?.type &&
      ["Android", "iOS"].includes(res.device?.type),
  );

  return (
    <Page title="Multicontrol" heading="Multicontrol">
      <div className="tw-grid tw-grid-cols-2 tw-gap-4">
        {reservedProbes.map((probe) => (
          <ProbeControl key={probe.probe_name} reservedProbe={probe} />
        ))}
      </div>
    </Page>
  );
};

export default ManualTestingMulticontrol;
