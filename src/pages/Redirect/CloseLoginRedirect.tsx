import { useEffect } from "react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";

export const CloseLoginRedirect = () => {
  const auth = useAuth();

  useEffect(() => {
    if (auth.isAuthenticated && auth.user) {
      window?.opener?.postMessage(
        {
          type: "AUTH_SUCCESS",
          accessToken: auth.user.access_token,
          idToken: auth.user.id_token,
          profile: auth.user.profile,
        },
        "*",
      );
      window.close();
    }
  }, [auth.isAuthenticated, auth.user]);

  return (
    <>
      <h1>Login Successful</h1>
      <p>Please close this tab to continue.</p>
    </>
  );
};

export default CloseLoginRedirect;
