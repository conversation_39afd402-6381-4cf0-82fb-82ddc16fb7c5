import { ScaleButton } from "@telekom/scale-components-react";
import { H1 } from "@tessa-portal/components/common/Typography";

export const IframeLogin = () => {
  const handleLogin = () => {
    window.open("/login-redirect", "_blank");
  };

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px",
        textAlign: "center",
      }}
    >
      <H1>TESSA Login</H1>
      <ScaleButton onClick={handleLogin} type="button">
        Login
      </ScaleButton>
      <p>Please log in to access TESSA</p>
    </div>
  );
};

export default IframeLogin;
