import { useState, useEffect } from "react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import { Navigate } from "react-router-dom";
import { ScaleLoadingSpinner } from "@telekom/scale-components-react";

export const LoginRedirect = () => {
  const auth = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<null | string>(null);

  useEffect(() => {
    const handleLogin = async () => {
      if (!auth.isAuthenticated && !auth.isLoading && !isLoading) {
        try {
          setIsLoading(true);

          // Check if we're in an iframe
          const isInIframe = window.top !== window.self;

          // Set appropriate redirect URI based on context
          const redirectUri = isInIframe
            ? `${window.location.origin}/login-redirect-close`
            : window.location.origin + window.location.pathname;

          // If we're in an iframe and using TokenRelayAuthProvider,
          // we need to use the parent window's auth
          if (isInIframe && !auth.signinRedirect) {
            // Open login in parent window
            if (window.top) {
              window.top.location.href = "/login-redirect";
            }
            return;
          }

          if (auth.signinRedirect) {
            await auth.signinRedirect({
              redirect_uri: redirectUri,
              extraQueryParams: {
                // Add kc_idp_hint if needed for specific identity provider
                // kc_idp_hint: "your-idp",
              },
              // Add state parameter for additional security
              state: Math.random().toString(36).substring(7),
            });
          }
        } catch (err) {
          console.error("Error during sign-in redirect:", err);
          setError(
            "Failed to authenticate. Please check your browser settings and ensure third-party cookies are enabled.",
          );
          setIsLoading(false);
        }
      }
    };

    handleLogin();
  }, [auth, isLoading]);

  if (auth.isAuthenticated) {
    return <Navigate to="/login-redirect-close" replace />;
  }

  if (isLoading || auth.isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <ScaleLoadingSpinner
          size="large"
          text="Authenticating..."
          alignment="vertical"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          padding: "20px",
          textAlign: "center",
        }}
      >
        <h2>Authentication Error</h2>
        <p>{error}</p>
        <p>Please try the following:</p>
        <ul style={{ textAlign: "left" }}>
          <li>Enable third-party cookies in your browser settings</li>
          <li>Clear your browser cache and cookies</li>
          <li>Try using a different browser</li>
          <li>
            If using Firefox, try disabling Enhanced Tracking Protection for
            this site
          </li>
        </ul>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: "10px 20px",
            marginTop: "20px",
            cursor: "pointer",
          }}
        >
          Try Again
        </button>
      </div>
    );
  }

  return null;
};

export default LoginRedirect;
