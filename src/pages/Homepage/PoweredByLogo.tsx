import { useEffect, useState } from "react";

const PoweredByLogo = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const matchMedia = window.matchMedia("(prefers-color-scheme: dark)");
    setIsDarkMode(matchMedia.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsDarkMode(e.matches);
    };

    matchMedia.addEventListener("change", handleChange);
    return () => matchMedia.removeEventListener("change", handleChange);
  }, []);

  const logoSrc = isDarkMode ? "/img/TDI_logo.png" : "/img/TDI_logo_black.png";

  return (
    <img src={logoSrc} alt="Powered by TDI" className="tw-mx-auto tw-w-auto" />
  );
};

export default PoweredByLogo;
