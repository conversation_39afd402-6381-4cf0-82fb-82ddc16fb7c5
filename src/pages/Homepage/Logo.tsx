import { useEffect, useState } from "react";

const Logo = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const matchMedia = window.matchMedia("(prefers-color-scheme: dark)");
    setIsDarkMode(matchMedia.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsDarkMode(e.matches);
    };

    matchMedia.addEventListener("change", handleChange);
    return () => matchMedia.removeEventListener("change", handleChange);
  }, []);

  const logoSrc = isDarkMode
    ? "/img/TESSA_Logo_Vorschlag_3.png"
    : "/img/TESSA_Logo_Vorschlag_3_black.png";

  return (
    <img src={logoSrc} alt="TESSA Logo" className="tw-mx-auto tw-w-auto" />
  );
};

export default Logo;
