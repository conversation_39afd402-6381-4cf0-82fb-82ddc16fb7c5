export const headingData = [
  {
    heading: "Telekom Evolved System for Service Automation (TESSA)",
    description:
      "Welcome to TESSA the self-service portal of the Telekom Evolved System for Service Automation. We enable you to complete your test tasks quickly and easily.",
    level: 2,
  },
  {
    heading: "TESSA Test Categories",
    description: "Discover the different test categories in TESSA.",
    level: 3,
  },
  {
    heading: "Advantages of TESSA",
    description:
      "Discover the powerful advantages of TESSA and see how it simplifies your workflow, and boosts efficiency.",
    level: 3,
  },
  {
    heading: "Client Testimonials",
    description: "Read what our clients have to say about the TESSA portal.",
    level: 3,
  },
];

export const cardData = [
  {
    iconName: "ScaleIconDevicePhoneWithMobilePlan",
    heading: "Manual Remote Testing",
    subHeading:
      "Your solution for seamless, remote device control and testing from anywhere.",
  },
  {
    iconName: "ScaleIconActionLaunch",
    heading: "Automated Testing",
    subHeading:
      "For efficient, hands-free testing automated of mobile and fixed line devices from anywhere.",
  },
  {
    iconName: "ScaleIconTProductMeasureInternetSpeed",
    heading: "KPI Measurements",
    subHeading:
      "The key to tracking, analyzing, and optimizing performance across your projects.",
  },
  {
    iconName: "ScaleIconActionSearch",
    heading: "OSIX Tracing",
    subHeading:
      "Capture and analyze system or application events for performance monitoring and debugging.",
    isCentered: true,
  },
  {
    iconName: "ScaleIconContentSimCard",
    heading: "SIM Multiplexing",
    subHeading:
      "Allows mapping a SIM card profile to different devices, allowing flexible, cost-effective use across smartphones.",
  },
  {
    iconName: "ScaleIconContentKey",
    heading: "Quick & Seamless Access",
    subHeading:
      "Simplifies your testing processes and accelerates your automation journey with our efficient platform.",
  },
  {
    iconName: "ScaleIconProcessSepaTransaction",
    heading: "Travel Cost Reduction",
    subHeading:
      "Minimizes your travel costs by optimizing test plans, using local SIMs and integrating remote testing solutions.",
  },
  {
    iconName: "ScaleIconHomeLightBulb",
    heading: "Support of Future Capabilities",
    subHeading:
      "Leverage ready-to-use features and stay prepared for future capabilities, such as Data Analytics and CI/CD integrations. Embrace innovation and ensure scalability for your operations.",
  },
  {
    iconName: "ScaleIconServiceMaintanance",
    heading: "In-House Probe Development",
    subHeading:
      "Offers tailored solutions, faster customization, cost savings, enhanced security, direct expertise, and long-term availability, ensuring greater flexibility and efficiency for customers.",
  },
  {
    iconName: "ScaleIconCommunicationChat",
    heading: "Argiris Stamateas",
    subHeading:
      "“Working with TESSA was a game-changer for our project. TESSA helps our customers to complete their daily test tasks ways more quickly and reliably.”",
  },
  {
    iconName: "ScaleIconCommunicationChat",
    heading: "Tim Crozier",
    subHeading:
      "“TESSA's SIM multiplexing revolutionized our tariff testing, especially for roaming, improving efficiency and driving significant cost savings through reduced travel expenses and time savings.”",
  },
  {
    iconName: "ScaleIconCommunicationChat",
    heading: "Jan Dressel",
    subHeading:
      "“I was impressed by the professionalism and commitment of the colleagues who work on TESSA. Your work exceeded my expectations.”",
  },
];
