import { Page } from "../Page";
import { MaxGridWidth } from "@tessa-portal/components/common/Main/MaxGridWidth";
import Card from "@tessa-portal/components/common/Homepage/Card";
import Heading from "@tessa-portal/components/common/Homepage/Heading";
import { ScaleDivider, ScaleButton } from "@telekom/scale-components-react";
import { cardData, headingData } from "./homepageData";
import { useHandleLogin } from "@tessa-portal/layout/DefaultLayout/hooks/useHandleLogin";
import Logo from "./Logo";
import PoweredByLogo from "./PoweredByLogo";

const Homepage = () => {
  const handleLogin = useHandleLogin();

  return (
    <Page title="TESSA">
      <div className="tw-my-8">
        <MaxGridWidth>
          <div className="tw-grid tw-grid-cols-1 tw-gap-8 lg:tw-grid-cols-2 2xl:tw-max-w-[var(--scl-grid-max-width)]">
            <Heading
              heading={headingData[0].heading}
              description={headingData[0].description}
              level={headingData[0].level}
            />
            <div className="tw-p-4">
              <div className="tw-flex tw-flex-col tw-items-end">
                <div className="tw-h-auto tw-w-3/4">
                  <Logo />
                </div>

                <div className="tw-flex tw-items-start">
                  <span className="tw-mr-2 tw-text-sm">Powered by</span>
                  <div className="tw-h-auto tw-w-24">
                    <PoweredByLogo />
                  </div>
                </div>
              </div>
            </div>
            <div className="tw-col-span-1 tw-flex tw-justify-center lg:tw-col-span-2">
              <ScaleButton onClick={handleLogin}>Log in</ScaleButton>
            </div>
          </div>

          <div className="tw-my-8">
            <ScaleDivider />
          </div>
          <Heading
            heading={headingData[1].heading}
            description={headingData[1].description}
            level={headingData[1].level}
          />
          <div className="tw-grid tw-auto-rows-fr tw-grid-cols-1 tw-gap-8 lg:tw-grid-cols-3 2xl:tw-max-w-[var(--scl-grid-max-width)]">
            {cardData.slice(0, 3).map((card, index) => (
              <Card
                key={index}
                iconName={card.iconName}
                heading={card.heading}
                subHeading={card.subHeading}
              />
            ))}
            <div className="tw-col-span-1 tw-flex tw-justify-center lg:tw-col-span-3">
              <div className="tw-w-full lg:tw-w-1/3">
                <Card
                  iconName={cardData[3].iconName}
                  heading={cardData[3].heading}
                  subHeading={cardData[3].subHeading}
                />
              </div>
            </div>
          </div>

          <div className="tw-my-8">
            <ScaleDivider />
          </div>
          <Heading
            heading={headingData[2].heading}
            description={headingData[2].description}
            level={headingData[2].level}
          />
          <div className="tw-grid tw-auto-rows-fr tw-grid-cols-1 tw-gap-8 lg:tw-grid-cols-3 2xl:tw-max-w-[var(--scl-grid-max-width)]">
            {cardData.slice(4, 7).map((card, index) => (
              <Card
                key={index}
                iconName={card.iconName}
                heading={card.heading}
                subHeading={card.subHeading}
              />
            ))}
            <div className="tw-col-span-1 tw-flex tw-justify-center lg:tw-col-span-3">
              <div className="tw-grid tw-w-full tw-grid-cols-1 tw-gap-8 lg:tw-w-2/3 lg:tw-grid-cols-2">
                {cardData.slice(7, 9).map((card, index) => (
                  <Card
                    key={index}
                    iconName={card.iconName}
                    heading={card.heading}
                    subHeading={card.subHeading}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="tw-my-8">
            <ScaleDivider />
          </div>
          <Heading
            heading={headingData[3].heading}
            description={headingData[3].description}
            level={headingData[3].level}
          />
          <div className="tw-grid tw-auto-rows-fr tw-grid-cols-1 tw-gap-8 lg:tw-grid-cols-3 2xl:tw-max-w-[var(--scl-grid-max-width)]">
            {cardData.slice(9, 12).map((card, index) => (
              <Card
                key={index}
                iconName={card.iconName}
                heading={card.heading}
                subHeading={card.subHeading}
              />
            ))}
          </div>
        </MaxGridWidth>
      </div>
    </Page>
  );
};

export default Homepage;
