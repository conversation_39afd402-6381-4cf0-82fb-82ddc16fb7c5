import { useCallback, useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import { Page } from "../Page";
import { useGetStatusData } from "@tessa-portal/features/automationJobs/hooks";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { ScaleDivider } from "@telekom/scale-components-react";
import AutomationJobsTable from "@tessa-portal/features/automationJobs/features/AutomationJobsTable";
import JobView from "@tessa-portal/features/automationJobs/JobView/JobView";

const AutomationJobs = () => {
  const { data, isLoading } = useGetStatusData();
  const { uuid } = useParams();
  const navigate = useNavigate();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const handleRowSelectionChange = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const value =
        typeof updaterOrValue === "function"
          ? updaterOrValue(rowSelection)
          : updaterOrValue;
      const selectedRows = Object.keys(value);
      const selectedRow = selectedRows[0];

      if (selectedRow) {
        setRowSelection(value);
        navigate(`/automation/${selectedRow}`);
      }
    },
    [navigate, rowSelection],
  );

  useEffect(() => {
    setRowSelection(uuid ? { [uuid]: true } : {});
  }, [uuid]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <Page title="Automation" heading="Automation">
      <PanelGroup direction="horizontal">
        <Panel defaultSize={uuid ? 40 : 100}>
          <AutomationJobsTable
            data={data}
            onRowSelectionChange={handleRowSelectionChange}
            rowSelection={rowSelection}
          />
        </Panel>
        {uuid && (
          <>
            <PanelResizeHandle>
              <ScaleDivider vertical className="tw-mx-1" />
            </PanelResizeHandle>
            <Panel>
              <JobView uuid={uuid} />
            </Panel>
          </>
        )}
      </PanelGroup>
    </Page>
  );
};

export default AutomationJobs;
