import { useEffect, useMemo } from "react";
import useDeviceDisplay from "@tessa-portal/hooks/useDeviceDisplay";
import { Screen } from "@tessa-portal/features/remoteControl/features/Android/components/Screen";
import DeviceButtons from "@tessa-portal/features/remoteControl/features/Android/components/DeviceButtons";
import { useGetStfDevices } from "@tessa-portal/hooks/services/stf";
import useUser from "@tessa-portal/hooks/useUser";
import { StfDevice } from "@tessa-portal/types/services/stf";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import {
  useGetStatusData,
  StatusData,
} from "@tessa-portal/features/automationJobs/hooks";
import { useParams } from "react-router-dom";

interface ProbeRendererProps {
  probeName: string;
  selectedJob: StatusData;
}

const ProbeRenderer = ({ probeName, selectedJob }: ProbeRendererProps) => {
  const { userId, isAdmin } = useUser();
  const { data: probeData } = useGetProbeData();
  const { openDeviceDisplay, getDeviceDisplay } = useDeviceDisplay();
  const { data: stfDevices } = useGetStfDevices();

  const reservedProbe = probeData?.find(
    (res) =>
      res.probe_name === probeName &&
      res.reservationStatus === "Reserved" &&
      (String(res.reservation?.user_id) === userId || isAdmin),
  );

  const stfDevice = useMemo(() => {
    if (!stfDevices || !reservedProbe) {
      return undefined;
    }

    return stfDevices.find(
      (d: StfDevice) => reservedProbe.device?.serial === d.serial,
    );
  }, [stfDevices, reservedProbe]);

  useEffect(() => {
    if (stfDevice) {
      openDeviceDisplay(stfDevice.serial, stfDevice.display.url);
    }
  }, [stfDevice, openDeviceDisplay]);

  if (!stfDevice) return <div>Loading</div>;

  const deviceDisplay = getDeviceDisplay(stfDevice.serial);

  if (!deviceDisplay) return <div>Loading</div>;

  return stfDevice.ready ? (
    <div className="tw-max-h=[600px] tw-max-w-[300px]">
      <div className="tw-flex tw-justify-center tw-p-2">{probeName}</div>
      <Screen
        deviceDisplay={deviceDisplay}
        stfDevice={stfDevice}
        disableControl={!isAdmin || !selectedJob?.developer_job}
      />
      {selectedJob?.developer_job && (
        <DeviceButtons channel={stfDevice.channel} />
      )}
    </div>
  ) : null;
};

export const DeviceView = () => {
  const { data: statusData } = useGetStatusData();
  const { uuid } = useParams();

  const selectedJob = statusData.find((res) => res.uuid === uuid);

  return (
    <div className="tw-flex tw-flex-row tw-justify-center tw-gap-16 tw-p-8">
      {selectedJob?.probes
        .filter(
          (probe) =>
            probe.probe_type === "Android" || probe.probe_type === "iOS",
        )
        .map((probe) => (
          <ProbeRenderer
            key={probe.probe_name}
            probeName={probe.probe_name}
            selectedJob={selectedJob}
          />
        ))}
    </div>
  );
};

export default DeviceView;
