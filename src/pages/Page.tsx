import { Helmet } from "react-helmet-async";
import { ReactNode } from "react";
import { Heading } from "@tessa-portal/components/common/Typography";

export type PageProps = {
  children: ReactNode;
  title?: string;
  heading?: string;
};

export const Page = ({ title, heading, children }: PageProps) => (
  <>
    {title && (
      <Helmet>
        <title>{title} | TESSA | Test Automation</title>
      </Helmet>
    )}
    {heading && <Heading className="tw-mx-4">{heading}</Heading>}
    <div className={"tw-mx-4 tw-h-full"}>{children}</div>
  </>
);
