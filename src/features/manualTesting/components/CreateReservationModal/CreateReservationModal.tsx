import { ReactNode, useEffect, useRef, useState, useCallback } from "react";
import Modal, { ModalRef } from "@tessa-portal/components/common/Modal";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { useCreateReservation } from "@tessa-portal/hooks/services/reservation";
import {
  SubmitHandler,
  useForm,
  FormProvider,
  useFormContext,
} from "react-hook-form";
import {
  ScaleHelperText,
  ScaleLoadingSpinner,
} from "@telekom/scale-components-react";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import moment from "moment";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";
import clsx from "clsx";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import ProbesTable from "../../ProbesTable/ProbesTable";
import { Updater, RowSelectionState } from "@tanstack/react-table";

type CreateReservationModalProps = {
  onClose: () => void;
};

export type CreateReservationFormValues = {
  probes: string[];
  reservation: ReservationTimeData;
};

const CreateReservationModal = ({
  onClose: handleClose,
}: CreateReservationModalProps) => {
  const {
    mutateAsync: createReservation,
    error,
    isError,
  } = useCreateReservation();
  const { data: probeData, isLoading } = useGetProbeData();

  const defaultValues: CreateReservationFormValues = {
    probes: [],
    reservation: {
      from: moment().format(scaleDateTimeFormat),
      until: moment().add(2, "hour").format(scaleDateTimeFormat),
    },
  };

  const form = useForm<CreateReservationFormValues>({
    mode: "all",
    defaultValues,
  });

  const selectedProbes = form.watch("probes");

  const onSubmit: SubmitHandler<CreateReservationFormValues> = async (data) => {
    const { reservation, probes } = data;

    // TODO: clean up this function
    const parseDate = (dateStr: string): string => {
      return moment(dateStr, scaleDateTimeFormat).toISOString();
    };

    let fromDate = parseDate(reservation.from);
    const untilDate = parseDate(reservation.until);

    if (moment(reservation.from, scaleDateTimeFormat).isBefore(moment())) {
      fromDate = moment().add(5, "second").toISOString();
    }

    probes.forEach((probe) => {
      createReservation(
        {
          probe_name: probe,
          reservation: {
            from: fromDate,
            until: untilDate,
          },
        },
        { onSuccess: handleClose },
      );
    });
  };

  const handleRowSelection = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const newState =
        typeof updaterOrValue === "function"
          ? updaterOrValue({})
          : updaterOrValue;

      form.setValue("probes", Object.keys(newState));
    },
    [form],
  );

  if (isLoading)
    return (
      <div className="tw-flex tw-flex-grow tw-justify-center">
        <ScaleLoadingSpinner />
      </div>
    );

  return (
    <FormProvider {...form}>
      <FormModal onClose={handleClose}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="tw-flex tw-justify-evenly tw-gap-4 tw-pb-4">
            <TextField
              className="tw-w-full"
              label="From"
              name="reservation.from"
              // FIXME: fix date validation
              rules={{
                validate: (value) =>
                  moment(value).add(60, "second").isAfter(moment()) ||
                  "From date must be in the future",
                required: "From date is required",
              }}
              step="60"
              type="datetime-local"
            />
            <TextField
              label="Until"
              className="tw-w-full"
              name="reservation.until"
              rules={{
                validate: (value) =>
                  moment(value).isAfter(moment()) ||
                  "Until date must be in the future",
                required: "From date is required",
              }}
              step="60"
              type="datetime-local"
            />
          </div>
          <ProbesTable
            data={probeData}
            showCheckbox={true}
            enableMultiRowSelection={true}
            showTableActions={false}
            onRowSelectionChange={handleRowSelection}
          />
          <div className={clsx(!isError && "tw-invisible")}>
            <ScaleHelperText variant="danger">{error?.message}</ScaleHelperText>
          </div>
          <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4">
            <Modal.CloseButton type="button" variant="secondary">
              Cancel
            </Modal.CloseButton>
            <StyledScaleButton
              variant="primary"
              type="submit"
              disabled={!form.formState.isValid || selectedProbes.length === 0}
            >
              Reserve
            </StyledScaleButton>
          </div>
        </form>
      </FormModal>
    </FormProvider>
  );
};

export default CreateReservationModal;

type FormModalProps = {
  children: ReactNode;
  onClose: () => void;
};

const FormModal = ({ children, onClose: handleClose }: FormModalProps) => {
  const modalRef = useRef<ModalRef>(null);

  const [confirmed, setConfirmed] = useState(false);

  const {
    formState: { isDirty },
  } = useFormContext();

  useEffect(() => {
    if (confirmed && modalRef.current) {
      modalRef.current?.closeModal();
    }
  }, [confirmed]);

  const showConfirm = useConfirm();

  const handleBeforeClose = (e: Event) => {
    if (isDirty && !confirmed) {
      e.preventDefault();

      showConfirm({
        title: "Close Reservation",
        message: "Are you sure you want to close the reservation?",
        onConfirm: () => {
          setConfirmed(true);
        },
      });
    }
  };

  return (
    <Modal
      disablePortal
      heading="New reservation"
      onScale-before-close={handleBeforeClose}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.75 * var(--scl-grid-max-width))));}"
      }
      ref={modalRef}
    >
      {children}
    </Modal>
  );
};
