import { ReactNode, useRef } from "react";
import Modal, { ModalRef } from "@tessa-portal/components/common/Modal";
import {
  usePatchReservation,
  useUpdateReservation,
} from "@tessa-portal/hooks/services/reservation";
import { SubmitHandler, useForm, FormProvider } from "react-hook-form";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import moment from "moment";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";

type Reservation = {
  id: string | number;
  reservation: ReservationTimeData;
};

type CreateReservationModalProps = {
  onClose: () => void;
  reservation: ProbeData;
};

// TODO: move this component to the ProbeView folder
const EditReservationModal = ({
  onClose: handleClose,
  reservation,
}: CreateReservationModalProps) => {
  const { mutateAsync: patchReservation, isPending: isPendingPatch } =
    usePatchReservation();
  const { mutateAsync: updateReservation, isPending: isPendingUpdate } =
    useUpdateReservation();

  const defaultValues: Reservation = {
    id: reservation.reservation?.id || "",
    reservation: {
      from: moment(reservation.reservation?.from).format(scaleDateTimeFormat),
      until: moment(reservation.reservation?.until).format(scaleDateTimeFormat),
    },
  };

  const form = useForm<Reservation>({
    mode: "all",
    defaultValues,
  });

  const onSubmit: SubmitHandler<Reservation> = async (data) => {
    const {
      id,
      reservation: { from, until },
    } = data;

    if (moment(reservation.reservation?.from).isBefore(moment())) {
      patchReservation(
        {
          id: id,
          reservation: {
            until: moment(until).toISOString(),
          },
        },
        { onSuccess: handleClose },
      );
    } else {
      updateReservation(
        {
          id: id,
          reservation: {
            from: moment(from).toISOString(),
            until: moment(until).toISOString(),
          },
        },
        { onSuccess: handleClose },
      );
    }
  };

  return (
    <FormProvider {...form}>
      <FormModal onClose={handleClose} probeAlias={reservation.probe_alias}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="tw-flex tw-justify-evenly tw-gap-4 tw-pb-4">
            <TextField
              disabled={moment(reservation.reservation?.from).isBefore(
                moment(),
              )}
              className="tw-w-full"
              label="From"
              name="reservation.from"
              rules={{
                validate: (value) =>
                  moment(value).add(5, "second").isAfter(moment()) ||
                  "From date must be in the future",
                required: "From date is required",
              }}
              step="60"
              type="datetime-local"
            />
            <TextField
              label="Until"
              className="tw-w-full"
              name="reservation.until"
              rules={{
                validate: (value) =>
                  moment(value).isAfter(moment()) ||
                  "Until date must be in the future",
                required: "From date is required",
              }}
              step="60"
              type="datetime-local"
            />
          </div>
          <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4">
            <Modal.CloseButton type="button" variant="secondary">
              Cancel
            </Modal.CloseButton>
            <StyledScaleButton
              variant="primary"
              type="submit"
              disabled={
                !form.formState.isValid || isPendingPatch || isPendingUpdate
              }
            >
              Update
            </StyledScaleButton>
          </div>
        </form>
      </FormModal>
    </FormProvider>
  );
};

export default EditReservationModal;

type FormModalProps = {
  children: ReactNode;
  onClose: () => void;
  probeAlias: string;
};

const FormModal = ({
  children,
  onClose: handleClose,
  probeAlias,
}: FormModalProps) => {
  const modalRef = useRef<ModalRef>(null);
  return (
    <Modal
      disablePortal
      heading={`Edit reservation for ${probeAlias}`}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      size="small"
      ref={modalRef}
    >
      {children}
    </Modal>
  );
};
