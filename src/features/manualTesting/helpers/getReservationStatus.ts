import moment from "moment";

export type ReservationStatus = "Available" | "Reserved" | "Upcoming";

export const getReservationStatus = (
  from?: Date,
  until?: Date,
): ReservationStatus => {
  const momentFrom = moment(from);
  const momentUntil = moment(until);
  const momentNow = moment();

  if (momentFrom.isAfter(momentNow) && momentUntil.isAfter(momentNow)) {
    return "Upcoming";
  }
  if (momentUntil.isAfter(momentNow)) {
    return "Reserved";
  }
  return "Available";
};
