import { ScaleTooltip } from "@telekom/scale-components-react";
import { Reservation } from "@tessa-portal/types/services/reservations";
import { useGetUsers } from "@tessa-portal/hooks/services/user";
import { dateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import moment from "moment";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";
import {
  ReservationStatus,
  getReservationStatus,
} from "@tessa-portal/features/manualTesting/helpers/getReservationStatus";

interface ReservationStatusCellProps {
  reservation?: Reservation;
}

interface ReservationTooltipContentProps {
  reservation: Reservation;
  userEmail?: string;
  userName?: string;
}

const ReservationTooltipContent = ({
  reservation,
  userEmail,
  userName,
}: ReservationTooltipContentProps) => (
  <span>
    <span>From: {moment(reservation.from).format(dateTimeFormat)}</span>
    <br />
    <span>Until: {moment(reservation.until).format(dateTimeFormat)}</span>
    <br />
    <span>
      By:
      {userEmail ? (
        <a href={`mailto:${userEmail}`}> {userName}</a>
      ) : (
        <span> {userName || "Unknown User"}</span>
      )}
    </span>
  </span>
);

const getStatusColor = (
  status?: ReservationStatus,
): "teal" | "orange" | "violet" => {
  switch (status) {
    case "Available":
      return "teal";
    case "Reserved":
      return "orange";
    case "Upcoming":
      return "violet";
    default:
      return "teal";
  }
};

export const ReservationStatusCell = ({
  reservation,
}: ReservationStatusCellProps) => {
  const { data: users } = useGetUsers();

  const status = getReservationStatus(reservation?.from, reservation?.until);

  const tagColor = getStatusColor(status);

  if (!reservation) {
    return (
      <div className="tw-w-16">
        <StyledScaleTag type="standard" color={tagColor} width="100%">
          {status}
        </StyledScaleTag>
      </div>
    );
  }

  const user = users?.find((user) => user.uid === reservation.user_id);

  return (
    <div className="tw-w-16">
      <ScaleTooltip>
        <span slot="content">
          <ReservationTooltipContent
            reservation={reservation}
            userEmail={user?.mail}
            userName={user?.cn}
          />
        </span>
        <StyledScaleTag type="standard" color={tagColor} width="100%">
          {status}
        </StyledScaleTag>
      </ScaleTooltip>
    </div>
  );
};

export default ReservationStatusCell;
