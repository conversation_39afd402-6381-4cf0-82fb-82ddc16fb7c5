import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { ReservationStatusCell } from "./ReservationStatusCell";
import { createColumnHelper } from "@tanstack/react-table";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";

const columnHelper = createColumnHelper<ProbeData>();

export const useColumns = (currentUserName?: string) => [
  columnHelper.accessor("reservationStatus", {
    header: "Status",
    cell: ({ row }) => (
      <ReservationStatusCell reservation={row.original.reservation} />
    ),
  }),

  columnHelper.accessor("location", {
    header: "Location",
  }),

  columnHelper.accessor("probe_alias", {
    header: "Probe name",
  }),

  columnHelper.accessor("device.name", {
    header: "Model",
    cell: ({ row }) => row.original?.device?.name,
  }),

  columnHelper.accessor("combinedStatus", {
    header: "Probe status",
    cell: ({ getValue }) => {
      const color = getValue() === "online" ? "teal" : "red";
      const status = getValue() === "online" ? "Online" : "Offline";

      return (
        <StyledScaleTag color={color} type="standard">
          {status}
        </StyledScaleTag>
      );
    },
    sortingFn: (a, b) => {
      const aIsOnline = a.original.combinedStatus === "online";
      const bIsOnline = b.original.combinedStatus === "online";

      if (aIsOnline === bIsOnline) return 0;
      if (aIsOnline) return -1;
      if (bIsOnline) return 1;
      return 0;
    },
  }),

  columnHelper.accessor("user", {
    header: "Reserved by",
    cell: ({ row }) => row.original.user?.cn,
    sortingFn: (a, b) => {
      const aName = a.original.user?.cn || "";
      const bName = b.original.user?.cn || "";

      // If one is current user and other isn't, prioritize current user
      if (currentUserName) {
        if (aName === currentUserName && bName !== currentUserName) return -1;
        if (aName !== currentUserName && bName === currentUserName) return 1;
      }

      // If names are different, sort by name
      const nameCompare = aName.localeCompare(bName);
      if (nameCompare !== 0) return nameCompare;

      // If names are the same, sort by reservation time (latest first)
      const aTime = a.original.reservation?.from
        ? new Date(a.original.reservation.from).getTime()
        : 0;
      const bTime = b.original.reservation?.from
        ? new Date(b.original.reservation.from).getTime()
        : 0;
      return bTime - aTime;
    },
  }),
];
