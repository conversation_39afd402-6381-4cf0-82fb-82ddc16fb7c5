import { useState } from "react";
import Table from "@tessa-portal/components/common/Table";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { useColumns } from "./columns";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { ScaleIconActionAdd } from "@telekom/scale-components-react";
import CreateReservationModal from "../components/CreateReservationModal";
import useUser from "@tessa-portal/hooks/useUser";

type ProbesTableProps = {
  data: ProbeData[];
  onRowSelectionChange: (updaterOrValue: Updater<RowSelectionState>) => void;
  showCheckbox?: boolean;
  enableMultiRowSelection?: boolean;
  showTableActions?: boolean;
  rowSelection?: RowSelectionState;
};

const ProbesTable = ({
  data,
  onRowSelectionChange,
  showCheckbox = false,
  enableMultiRowSelection = false,
  showTableActions = true,
  rowSelection,
}: ProbesTableProps) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { name: currentUserName } = useUser();
  const tableColumns = useColumns(currentUserName);

  return (
    <>
      <Table
        columns={tableColumns}
        data={data}
        getRowId={(row) => row.probe_name}
        selectableRows={true}
        enableMultiRowSelection={enableMultiRowSelection}
        showCheckbox={showCheckbox}
        showTableActions={showTableActions}
        searchTags
        tableActions={{
          customActions: [
            {
              label: "New Reservation",
              icon: <ScaleIconActionAdd />,
              onClick: () => setShowCreateModal(true),
            },
          ],
        }}
        onRowSelectionChange={onRowSelectionChange}
        rowSelection={rowSelection}
        enableSorting
        initialState={{
          sorting: [{ id: "user", desc: false }],
        }}
        showSort
      />
      {showCreateModal && (
        <CreateReservationModal onClose={() => setShowCreateModal(false)} />
      )}
    </>
  );
};

export default ProbesTable;
