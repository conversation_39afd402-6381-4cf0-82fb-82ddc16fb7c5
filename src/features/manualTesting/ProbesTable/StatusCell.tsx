import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";
type TagColor = "teal" | "red";

export type ProbeStatus = "online" | "offline";
export type DeviceStatus = "online" | "offline";
export type StfStatus = "online" | "offline" | "irrelevant";

interface StatusCellProps {
  probeStatus: ProbeStatus;
  deviceStatus?: DeviceStatus;
  stfStatus?: StfStatus;
}

// TODO: delete this file
export const StatusCell = ({
  probeStatus,
  deviceStatus,
  stfStatus,
}: StatusCellProps) => {
  const isOnline =
    probeStatus === "online" &&
    deviceStatus === "online" &&
    (stfStatus === "online" || stfStatus === "irrelevant");

  const color: TagColor = isOnline ? "teal" : "red";
  const status = isOnline ? "Online" : "Offline";

  return (
    <StyledScaleTag color={color} type="standard">
      {status}
    </StyledScaleTag>
  );
};

export default StatusCell;
