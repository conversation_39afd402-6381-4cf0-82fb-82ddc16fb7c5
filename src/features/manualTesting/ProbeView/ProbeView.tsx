import { Fragment } from "react";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import useUser from "@tessa-portal/hooks/useUser";
import ProbeControl from "./DeviceControl/DeviceControl";
import ProbeReservation from "./ProbeReservation/ProbeReservation";
import { getReservationStatus } from "@tessa-portal/features/manualTesting/helpers/getReservationStatus";

const ProbeView = ({ probeName }: { probeName: string }) => {
  const { data: probeData } = useGetProbeData();
  const { userId, isAdmin } = useUser();

  const reservedProbe = probeData.find(
    (res) =>
      res.probe_name === probeName &&
      getReservationStatus(res.reservation?.from, res.reservation?.until) ===
        "Reserved" &&
      (String(res.reservation?.user_id) === userId || isAdmin),
  );

  const selectedProbe = probeData.find((res) => res.probe_name === probeName);

  return (
    <Fragment>
      {reservedProbe ? (
        <ProbeControl reservedProbe={reservedProbe} />
      ) : (
        selectedProbe && <ProbeReservation selectedProbe={selectedProbe} />
      )}
    </Fragment>
  );
};

export default ProbeView;
