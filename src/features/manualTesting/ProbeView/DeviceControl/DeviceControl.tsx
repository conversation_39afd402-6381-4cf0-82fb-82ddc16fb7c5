import { useMemo } from "react";
import Iphone from "@tessa-portal/features/remoteControl/features/Iphone/Iphone";
import Android from "@tessa-portal/features/remoteControl/features/Android/Android";
import { useGetStfDevices } from "@tessa-portal/hooks/services/stf";
import { StfDevice } from "@tessa-portal/types/services/stf";
import { DeviceType } from "@tessa-portal/types/services/probe";
import {
  ProbeData,
  useGetProbeData,
} from "@tessa-portal/hooks/util/useGetProbeData";
import ReservationHeader from "../ReservationHeader/ReservationHeader";
import {
  ProbeControlNotSupportedNotification,
  ProbeOfflineNotification,
} from "../ProbeReservation/components/ProbeNotifications";
import { ScaleCard } from "@telekom/scale-components-react";

const ProbeControl = ({ reservedProbe }: { reservedProbe: ProbeData }) => {
  const { data: probeData } = useGetProbeData();
  const { data: stfDevices } = useGetStfDevices();
  const stfDevice = useMemo(() => {
    if (!stfDevices) {
      return undefined;
    }

    return stfDevices.find(
      (d: StfDevice) => reservedProbe.device?.serial === d.serial,
    );
  }, [stfDevices, reservedProbe]);

  const renderProbe = (key?: string) => {
    if (!reservedProbe) {
      return null;
    }
    switch (key) {
      case DeviceType.ios:
        return (
          <Iphone
            probe={reservedProbe}
            reservation={reservedProbe.reservation}
          />
        );
      case DeviceType.android:
        return (
          <Android stfDevice={stfDevice} reservationData={reservedProbe} />
        );
      case DeviceType.analogueModem:
        return <div>Modem</div>;
      case DeviceType.ipPhone:
        return <div>IP Phone</div>;
      default:
        return null;
    }
  };

  return (
    <>
      <ScaleCard>
        <ReservationHeader probe={reservedProbe} />
        {reservedProbe.combinedStatus === "offline" && (
          <ProbeOfflineNotification />
        )}
        {reservedProbe.device?.type !== "Android" &&
          reservedProbe.device?.type !== "iOS" && (
            <span className="tw-mx-1">
              <ProbeControlNotSupportedNotification />
            </span>
          )}
        {probeData && renderProbe(reservedProbe?.device?.type)}
      </ScaleCard>
    </>
  );
};

export default ProbeControl;
