import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { Heading2 } from "@tessa-portal/components/common/Typography";
import { ScaleIconActionCircleClose } from "@telekom/scale-components-react";
import { ScaleTooltip } from "@telekom/scale-components-react";
import { useNavigate } from "react-router-dom";
import ReservationControls from "./ReservationControls";

type ReservationHeaderProps = {
  probe: ProbeData;
};

const ReservationHeader = ({ probe }: ReservationHeaderProps) => {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate("/manual-testing/probes");
  };

  return (
    <div className="tw-shadow-sm tw-mb-2 tw-rounded-lg">
      <div className="tw-flex tw-flex-col">
        <div className="tw-flex tw-items-center tw-justify-between tw-gap-8">
          <Heading2 className="tw-whitespace-nowrap tw-font-semibold">
            {probe.probe_alias} - {probe.device?.name}
          </Heading2>
          <div className="tw-flex tw-justify-end">
            <ScaleTooltip content={"Close"} placement="top-end">
              <button
                className="tw-cursor-pointer"
                aria-label={"Close"}
                onClick={handleClose}
              >
                <ScaleIconActionCircleClose decorative={true} />
              </button>
            </ScaleTooltip>
          </div>
        </div>
        <ReservationControls probe={probe} />
      </div>
    </div>
  );
};

export default ReservationHeader;
