import { useState, useEffect, useCallback } from "react";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import EditReservationModal from "../../components/EditReservationModal/EditReservationModal";
import moment from "moment";
import {
  ScaleIconActionCircleClose,
  ScaleIconActionEdit,
} from "@telekom/scale-components-react";
import { useDeleteReservation } from "@tessa-portal/hooks/services/reservation";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";

type ReservationControlsProps = {
  probe: ProbeData;
};

const ReservationControls = ({ probe }: ReservationControlsProps) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [timeLeft, setTimeLeft] = useState("");
  const showConfirm = useConfirm();
  const toast = useToastNotification();
  const { mutateAsync: deleteReservation } = useDeleteReservation();

  const getTimeLeft = useCallback(() => {
    const now = moment();
    const end = moment(probe.reservation?.until);
    const duration = moment.duration(end.diff(now));

    const days = Math.floor(duration.asDays());
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
  }, [probe.reservation?.until]);

  useEffect(() => {
    // Update time immediately
    setTimeLeft(getTimeLeft());

    // Set up interval to update every second
    const timer = setInterval(() => {
      setTimeLeft(getTimeLeft());
    }, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(timer);
  }, [probe.reservation?.until, getTimeLeft]);

  const handleCancelReservation = async () => {
    if (!probe.reservation?.id) return;

    try {
      await deleteReservation({ id: probe.reservation.id });
      toast.open({
        heading: "Success",
        text: "Reservation aborted successfully",
        variant: "success",
      });
    } catch (error) {
      toast.open({
        heading: "Error",
        text:
          error instanceof Error
            ? error.message
            : "Failed to abort reservation",
        variant: "danger",
      });
    }
  };

  const handleCancelReservationConfirm = () => {
    showConfirm({
      title: "Abort Reservation",
      message: `Are you sure you want to abort the reservation of ${probe.probe_alias}?`,
      onConfirm: () => {
        handleCancelReservation();
      },
    });
  };

  if (!probe.reservation) {
    return null;
  }

  return (
    <div className="tw-flex tw-items-center tw-gap-8 tw-text-gray-600">
      <p className="tw-whitespace-nowrap">
        <b>Location:</b> {probe.location}
      </p>
      <p className="tw-whitespace-nowrap">
        <b>Time left:</b> {timeLeft}
      </p>
      <div className="tw-ml-auto tw-flex tw-gap-4">
        <StyledScaleButton
          variant="secondary"
          size="small"
          onClick={() => setIsEditModalOpen(true)}
        >
          <ScaleIconActionEdit decorative={true} size={12} />
          Edit
        </StyledScaleButton>
        <StyledScaleButton
          variant="secondary"
          onClick={handleCancelReservationConfirm}
          size="small"
        >
          <ScaleIconActionCircleClose decorative={true} size={12} />
          Abort
        </StyledScaleButton>
      </div>

      {isEditModalOpen && (
        <EditReservationModal
          onClose={() => setIsEditModalOpen(false)}
          reservation={probe}
        />
      )}
    </div>
  );
};

export default ReservationControls;
