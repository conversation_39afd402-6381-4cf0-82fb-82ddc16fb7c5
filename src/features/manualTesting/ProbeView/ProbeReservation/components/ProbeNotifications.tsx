import { useState } from "react";
import { ScaleNotification } from "@telekom/scale-components-react";
import { Link } from "react-router-dom";
import { ScaleIconNavigationExternalLink } from "@telekom/scale-components-react";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { Reservation } from "@tessa-portal/types/services/reservations";
import moment from "moment";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import EditReservationModal from "@tessa-portal/features/manualTesting/components/EditReservationModal/EditReservationModal";
import { useDeleteReservation } from "@tessa-portal/hooks/services/reservation";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";

interface ProbeNotificationsProps {
  selectedProbe: ProbeData;
  reservations: Reservation[];
}

export const ProbeOfflineNotification = () => {
  return (
    <ScaleNotification
      variant="danger"
      type="inline"
      opened
      heading="This probe is offline"
    >
      <span slot="text">
        Unfortunately, this probe is offline. You can report an incident via the
        Service Desk to get it back online.
        <br />
        <Link
          to="https://jira.telekom.de/plugins/servlet/desk/portal/2242/create/8181"
          target="_blank"
          className="tw-underline"
          style={{ color: "var(--telekom-color-text-and-icon-link-hovered)" }}
        >
          Service Desk
          <ScaleIconNavigationExternalLink
            size={16}
            slot="icon"
            decorative={true}
          />
        </Link>
      </span>
    </ScaleNotification>
  );
};

export const ProbeControlNotSupportedNotification = () => {
  return (
    <ScaleNotification
      variant="informational"
      type="inline"
      opened
      heading="Remote control not supported"
    >
      <span slot="text">
        This device does not support remote control functionality yet. We are
        working on being able to offer this service as soon as possible.
      </span>
    </ScaleNotification>
  );
};

export const UpcomingReservationNotification = ({
  reservation,
  selectedProbe,
}: {
  reservation: Reservation;
  selectedProbe: ProbeData;
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const showConfirm = useConfirm();
  const toast = useToastNotification();
  const { mutateAsync: deleteReservation } = useDeleteReservation();

  const from = moment(reservation.from).format(scaleDateTimeFormat);
  const until = moment(reservation.until).format(scaleDateTimeFormat);

  const handleCancelReservation = async () => {
    if (!reservation?.id) return;

    try {
      await deleteReservation({ id: reservation.id });
      toast.open({
        heading: "Success",
        text: "Reservation aborted successfully",
        variant: "success",
      });
    } catch (error) {
      toast.open({
        heading: "Error",
        text:
          error instanceof Error
            ? error.message
            : "Failed to abort reservation",
        variant: "danger",
      });
    }
  };

  const handleCancelReservationConfirm = () => {
    showConfirm({
      title: "Abort Reservation",
      message: `Are you sure you want to abort the reservation of ${selectedProbe.probe_alias}?`,
      onConfirm: () => {
        handleCancelReservation();
      },
    });
  };

  return (
    <>
      <ScaleNotification
        variant="informational"
        type="inline"
        opened
        heading="Upcoming Reservation"
      >
        <div slot="text" className="tw-flex tw-flex-col tw-gap-2">
          <div className="tw-flex tw-w-full tw-flex-col tw-gap-2">
            <p>
              There is an upcoming reservation for{" "}
              <strong>{reservation.probe_name}</strong>.
            </p>
            <p>
              From <strong>{from}</strong> until <strong>{until}</strong>.
            </p>
          </div>
          <div>
            <StyledScaleButton
              variant="secondary"
              size="small"
              onClick={() => setIsEditModalOpen(true)}
            >
              Edit
            </StyledScaleButton>
            <StyledScaleButton
              variant="secondary"
              size="small"
              onClick={handleCancelReservationConfirm}
            >
              Delete
            </StyledScaleButton>
          </div>
        </div>
      </ScaleNotification>
      {isEditModalOpen && (
        <EditReservationModal
          onClose={() => setIsEditModalOpen(false)}
          reservation={selectedProbe}
        />
      )}
    </>
  );
};

export const ProbeNotifications = ({
  selectedProbe,
  reservations,
}: ProbeNotificationsProps) => {
  const isProbeOffline =
    selectedProbe.status !== "online" ||
    selectedProbe.device?.status !== "online" ||
    selectedProbe.device?.stf_status === "offline";

  const upcomingReservations = reservations.filter(
    (reservation) =>
      reservation.probe_name === selectedProbe.probe_name &&
      moment(reservation.from).isAfter(moment()),
  );

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      {isProbeOffline && <ProbeOfflineNotification />}
      {upcomingReservations.map((reservation) => (
        <UpcomingReservationNotification
          key={reservation.id}
          reservation={reservation}
          selectedProbe={selectedProbe}
        />
      ))}
    </div>
  );
};
