import { ScaleHelperText } from "@telekom/scale-components-react";
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from "react-hook-form";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import clsx from "clsx";
import moment from "moment";
import { useEffect } from "react";

interface ReservationFormProps {
  onSubmit: SubmitHandler<CreateReservationFormValues>;
  error?: Error;
  isError: boolean;
  defaultValues: CreateReservationFormValues;
}

export type CreateReservationFormValues = {
  probes: string[];
  reservation: ReservationTimeData;
};

export const ReservationForm = ({
  onSubmit,
  error,
  isError,
  defaultValues,
}: ReservationFormProps) => {
  const form = useForm<CreateReservationFormValues>({
    mode: "all",
    defaultValues,
  });

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  const validateDateTime = (value: string, field: "from" | "until") => {
    const selectedTime = moment(value);
    const oneMinuteAgo = moment().subtract(1, "minute");

    if (field === "from") {
      if (selectedTime.isBefore(oneMinuteAgo)) {
        return "Start time must be in the future or within the last minute";
      }
    } else {
      const fromValue = form.getValues("reservation.from");
      if (selectedTime.isBefore(moment(fromValue))) {
        return "End time must be after start time";
      }
    }

    return true;
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="tw-flex tw-justify-evenly tw-gap-4 tw-py-4">
          <TextField
            className="tw-w-full"
            label="From"
            name="reservation.from"
            rules={{
              validate: (value) => validateDateTime(value, "from"),
              required: "From date is required",
            }}
            step="1"
            type="datetime-local"
          />
          <TextField
            label="Until"
            className="tw-w-full"
            name="reservation.until"
            rules={{
              validate: (value) => validateDateTime(value, "until"),
              required: "Until date is required",
            }}
            step="1"
            type="datetime-local"
          />
        </div>
        <div className={clsx(!isError && "tw-invisible")}>
          <ScaleHelperText variant="danger">{error?.message}</ScaleHelperText>
        </div>
        <div className="tw-flex tw-justify-end tw-gap-4 tw-py-4">
          <StyledScaleButton variant="primary" type="submit">
            Reserve
          </StyledScaleButton>
        </div>
      </form>
    </FormProvider>
  );
};
