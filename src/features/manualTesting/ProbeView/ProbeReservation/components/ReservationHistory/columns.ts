import { createColumnHelper } from "@tanstack/react-table";

export interface ReservationHistoryColumns {
  from: string;
  until: string;
  user: string;
  timeSinceCompletion: string;
}

const columnHelper = createColumnHelper<ReservationHistoryColumns>();

export const useColumns = () => {
  return [
    columnHelper.accessor("from", {
      header: "Start",
    }),
    columnHelper.accessor("until", {
      header: "End",
    }),
    columnHelper.accessor("user", {
      header: "User",
    }),
    columnHelper.accessor("timeSinceCompletion", {
      header: "Completed",
    }),
  ];
};
