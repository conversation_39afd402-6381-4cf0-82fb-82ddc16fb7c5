import { Heading3 } from "@tessa-portal/components/common/Typography";
import moment from "moment";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import { useGetUsers } from "@tessa-portal/hooks/services/user";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { useColumns, ReservationHistoryColumns } from "./columns";
import { useMemo } from "react";
import Table from "@tessa-portal/components/common/Table";
import { useGetReservationsByProbe } from "@tessa-portal/hooks/services/reservation";
interface ReservationHistoryProps {
  selectedProbe: ProbeData;
}

export const ReservationHistory = ({
  selectedProbe,
}: ReservationHistoryProps) => {
  const { data: users } = useGetUsers();
  const tableColumns = useColumns();
  const { data: probeReservations } = useGetReservationsByProbe(
    selectedProbe.probe_name,
  );
  const tableData = useMemo(() => {
    return probeReservations
      ?.filter(
        (reservation) => reservation.probe_name === selectedProbe.probe_name,
      )
      .map(
        (reservation) =>
          ({
            from: moment(reservation.from).format(scaleDateTimeFormat),
            until: moment(reservation.until).format(scaleDateTimeFormat),
            user:
              users?.find((user) => user.uid === reservation.user_id)?.cn ||
              "Unknown User",
            timeSinceCompletion: moment(reservation.until).fromNow(),
          }) as ReservationHistoryColumns,
      );
  }, [probeReservations, users, selectedProbe]);

  if (!tableData) {
    return null;
  }

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <Heading3>Reservation History</Heading3>
      <Table columns={tableColumns} data={tableData} showSort enableSorting />
    </div>
  );
};
