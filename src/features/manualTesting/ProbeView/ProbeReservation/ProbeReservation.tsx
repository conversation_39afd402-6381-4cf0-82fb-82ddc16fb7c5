import { ScaleCard, ScaleDivider } from "@telekom/scale-components-react";
import { Heading3, Text } from "@tessa-portal/components/common/Typography";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import {
  useCreateReservation,
  useGetReservations,
} from "@tessa-portal/hooks/services/reservation";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import moment from "moment";
import { ProbeNotifications } from "./components/ProbeNotifications";
import {
  ReservationForm,
  CreateReservationFormValues,
} from "./components/ReservationForm";
import { ReservationHistory } from "./components/ReservationHistory/ReservationHistory";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import ReservationHeader from "../ReservationHeader/ReservationHeader";

const ProbeReservation = ({ selectedProbe }: { selectedProbe: ProbeData }) => {
  const toast = useToastNotification();
  const {
    mutateAsync: createReservation,
    error,
    isError,
  } = useCreateReservation();

  const { data: reservations = [] } = useGetReservations();

  const defaultValues: CreateReservationFormValues = {
    probes: [selectedProbe.probe_name],
    reservation: {
      from: moment().format(scaleDateTimeFormat),
      until: moment().add(2, "hour").format(scaleDateTimeFormat),
    },
  };

  const onSubmit = async (data: CreateReservationFormValues) => {
    const { reservation, probes } = data;

    const parseDate = (dateStr: string): string => {
      return moment(dateStr, scaleDateTimeFormat).toISOString();
    };

    let fromDate = parseDate(reservation.from);
    const untilDate = parseDate(reservation.until);

    const selectedTime = moment(reservation.from, scaleDateTimeFormat);
    const oneMinuteAgo = moment().subtract(1, "minute");

    if (selectedTime.isBefore(oneMinuteAgo)) {
      throw new Error(
        "Start time must be in the future or within the last minute",
      );
    }

    if (selectedTime.isBefore(moment())) {
      fromDate = moment().add(5, "seconds").toISOString();
    }

    try {
      await Promise.all(
        probes.map((probe) =>
          createReservation({
            probe_name: probe,
            reservation: {
              from: fromDate,
              until: untilDate,
            },
          }),
        ),
      );

      toast.open({
        heading: "Success",
        text: "Probe reserved successfully",
        variant: "success",
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <ScaleCard>
        <ReservationHeader probe={selectedProbe} />
        <div className="tw-flex tw-flex-col">
          <Heading3>New Reservation</Heading3>
          <ProbeNotifications
            selectedProbe={selectedProbe}
            reservations={reservations}
          />
          <span>
            <Text>
              Please select the timeframe for your new reservation of{" "}
              {selectedProbe.probe_alias}
            </Text>
          </span>
        </div>
        <ReservationForm
          onSubmit={onSubmit}
          error={error || undefined}
          isError={isError}
          defaultValues={defaultValues}
        />
        <ScaleDivider />
        <ReservationHistory selectedProbe={selectedProbe} />
      </ScaleCard>
    </div>
  );
};

export default ProbeReservation;
