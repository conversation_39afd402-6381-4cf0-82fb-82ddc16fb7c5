import { useContext } from "react";
import { Tab, Tabs } from "@tessa-portal/components/common/Tabs";
import {
  SimCardsTable,
  OfflineSimCardsTable,
  FixedNumbersTable,
  SipAccountsTable,
  ImportSubscribers,
} from "@tessa-portal/features/subscribers/components";
import { useGetSubscriberData } from "@tessa-portal/features/subscribers/hooks";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import { SubscribersContext } from "@tessa-portal/features/subscribers/context/SubscribersContext";

export const SubscribersTable = () => {
  const { data: subscriberData, isLoading } = useGetSubscriberData();
  const { setSelectedSubscriber } = useContext(SubscribersContext);

  if (isLoading) return <LoadingSpinner />;

  return (
    <div>
      <Tabs>
        <Tab label="SIM Cards">
          <SimCardsTable data={subscriberData} />
        </Tab>
        <Tab label="Offline SIM Cards">
          <OfflineSimCardsTable data={subscriberData} />
        </Tab>
        <Tab label="Fixed Numbers">
          <FixedNumbersTable data={subscriberData} />
        </Tab>
        <Tab label="SIP Accounts">
          <SipAccountsTable data={subscriberData} />
        </Tab>
        <Tab
          label="Import Subscribers"
          onClick={() => setSelectedSubscriber(undefined)}
        >
          <ImportSubscribers />
        </Tab>
      </Tabs>
    </div>
  );
};

export default SubscribersTable;
