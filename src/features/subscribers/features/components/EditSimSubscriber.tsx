import {
  ScaleCheckbox,
  ScaleDropdownSelect,
  ScaleDropdownSelectItem,
} from "@telekom/scale-components-react";
import { fields } from "../../helpers";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { useFormContext } from "react-hook-form";

export const EditSimSubscriber = () => {
  const form = useFormContext();

  return (
    <div>
      <div className="tw-grid tw-grid-cols-2 tw-gap-4">
        {fields.map((field) => {
          switch (field.type) {
            case "number":
            case "text": {
              return (
                <TextField
                  key={field.name}
                  rules={field.rules}
                  type={field.type}
                  {...field}
                />
              );
            }
          }
        })}
        <div className="tw-my-auto tw-flex tw-flex-row tw-gap-4">
          <ScaleCheckbox
            {...form.register("lab")}
            label={"lab"}
            ariaLabelCheckbox={"lab"}
            checked={!!form.watch("lab")}
          />
          <ScaleCheckbox
            {...form.register("live")}
            label={"live"}
            ariaLabelCheckbox={"live"}
            checked={!!form.watch("live")}
          />
        </div>
      </div>
      <div className="tw-pt-4">
        <ScaleDropdownSelect label="Tags" {...form.register("tags")} />
      </div>
      <div className="tw-grid tw-grid-cols-3 tw-gap-4 tw-pt-4">
        <ScaleDropdownSelect label="Prepaid" {...form.register("prepaid")}>
          <ScaleDropdownSelectItem value="true">True</ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="false">False</ScaleDropdownSelectItem>
        </ScaleDropdownSelect>
        <ScaleDropdownSelect label="Type" {...form.register("type")}>
          <ScaleDropdownSelectItem value="static">
            Static
          </ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="mappable">
            Mappable
          </ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="sip">SIP</ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="fixed_line">
            Fixed Line
          </ScaleDropdownSelectItem>
        </ScaleDropdownSelect>
        <ScaleDropdownSelect label="SIM Type" {...form.register("sim_type")}>
          <ScaleDropdownSelectItem value="no">
            No SIM type
          </ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="single">
            Single
          </ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="master">
            Master
          </ScaleDropdownSelectItem>
          <ScaleDropdownSelectItem value="slave">Slave</ScaleDropdownSelectItem>
        </ScaleDropdownSelect>
      </div>
      <div className="tw-grid tw-grid-cols-2 tw-gap-4 tw-pt-4">
        <TextField type="text" label="Position" name="position" readonly />
        <TextField type="text" label="Modified" name="modified" readonly />
      </div>
    </div>
  );
};

export default EditSimSubscriber;
