import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { ScaleCheckbox } from "@telekom/scale-components-react";

export const EditOtherSubscriber = () => {
  return (
    <div className="tw-grid tw-grid-cols-2 tw-gap-4 tw-pb-4 tw-pt-1">
      <TextField
        className="tw-col-start-1"
        label="Number *"
        name="msisdn"
        rules={{
          required: "Number is required",
        }}
        type="number"
      />
      <TextField
        className="tw-col-start-2"
        label="Label"
        name="sub_name"
        type="text"
      />
      <div className="tw-flex tw-flex-col tw-gap-1">
        <ScaleCheckbox label="Lab" />
        <ScaleCheckbox label="Live" />
      </div>
      <TextField
        label="Tariff"
        className="tw-start-2"
        name="tariff"
        type="text"
      />
      <TextField
        className="tw-col-span-2 tw-col-start-1"
        label="Tags"
        name="tags"
        type="text"
      />
    </div>
  );
};

export default EditOtherSubscriber;
