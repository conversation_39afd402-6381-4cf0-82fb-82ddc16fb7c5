import { useState, useContext } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Text } from "@tessa-portal/components/common/Typography";

import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import { SubscribersContext } from "../context/SubscribersContext";
import { Subscriber } from "@tessa-portal/types/services/simManager";

import EditSimSubscriber from "./components/EditSimSubscriber";
import EditOtherSubscriber from "./components/EditOtherSubscriber";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";

interface SubscriberFormValues extends Omit<Subscriber, "assigned_to"> {}

export const SubscriberEditor = () => {
  const [state, forceUpdate] = useState(false);
  const { selectedSubscriber } = useContext(SubscribersContext);

  const defaultValues = selectedSubscriber || {};
  const form = useForm<SubscriberFormValues>({
    mode: "all",
    defaultValues,
  });

  const onSubmit = (data: SubscriberFormValues) => {
    console.log(data);
  };

  const renderTitle = (subscriber: Subscriber) => {
    let title = "Edit ";
    switch (subscriber.type) {
      case "mappable":
      case "static":
        title += `Sim ${subscriber.iccid}`;
        break;
      case "fixed_line":
        title += `Fixed Number ${subscriber.msisdn}`;
        break;
      case "sip":
        title += `SIP Account ${subscriber.msisdn}`;
        break;
    }

    return title;
  };

  if (!selectedSubscriber) {
    return <LoadingSpinner />;
  }

  return (
    <FormProvider {...form}>
      <Text className="tw-pb-6 tw-pt-2" variant="large">
        {renderTitle(selectedSubscriber)}
      </Text>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {selectedSubscriber?.type &&
        ["mappable", "static"].includes(selectedSubscriber.type) ? (
          <EditSimSubscriber />
        ) : (
          <EditOtherSubscriber />
        )}
        <div className="tw-flex tw-w-full tw-justify-end tw-gap-4 tw-pt-4">
          <StyledScaleButton
            type="button"
            variant="secondary"
            // disabled={!form.formState.isDirty}
            onClick={() => {
              forceUpdate(!state);
              return form.reset(defaultValues);
            }}
            innerAriaLabel="Discard"
          >
            Discard
          </StyledScaleButton>
          <StyledScaleButton
            type="submit"
            variant="primary"
            // disabled={!form.formState.isDirty || !form.formState.isValid}
            innerAriaLabel="Save"
          >
            Save
          </StyledScaleButton>
        </div>
      </form>
    </FormProvider>
  );
};
