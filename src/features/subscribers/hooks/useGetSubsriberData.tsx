import { useMemo } from "react";
import { useGetSubscribers } from "@tessa-portal/hooks/services/simManager/subscriber";
import { useGetMappings } from "@tessa-portal/hooks/services/simManager/mapping";
import { useGetProbes } from "@tessa-portal/hooks/services/probe";
import {
  SimMapping,
  Subscriber,
} from "@tessa-portal/types/services//simManager";
import { Probe } from "@tessa-portal/types/services/probe";

export interface SubscriberData extends Subscriber {
  mapping: string;
  status: "Available" | "In Use" | "Inserting";
  network: string;
}

export const useGetSubscriberData = () => {
  const { data: subscribers, isLoading: isLoadingSubscribers } =
    useGetSubscribers();
  const { data: mappings, isLoading: isLoadingMappings } = useGetMappings();
  const { data: probes, isLoading: isLoadingProbes } = useGetProbes();

  const subscriberData = useMemo(() => {
    if (!subscribers || !mappings) return [];

    return subscribers.map((subscriber) => {
      const mapping = mappings
        ? mappings.find(
            (mapping: SimMapping) =>
              mapping.mapped?.iccid === subscriber.iccid ||
              mapping.using?.iccid === subscriber.iccid,
          )
        : null;
      const mappedProbe = mapping
        ? probes?.find(
            (probe: Probe) =>
              probe.probe_name === mapping.channel.path.split(".")[0],
          )?.probe_alias
        : "";
      let mappingStatus = "Available";
      mappings?.find((mapping: SimMapping) => {
        if (
          mapping &&
          mapping.using &&
          mapping.using.iccid === subscriber.iccid
        ) {
          mappingStatus = "In Use";
          return (
            mapping && mapping.using && mapping.using.iccid === subscriber.iccid
          );
        }
        if (
          mapping &&
          mapping.mapped &&
          mapping.mapped.iccid === subscriber.iccid
        ) {
          mappingStatus = "Inserting";
          return (
            mapping &&
            mapping.mapped &&
            mapping.mapped.iccid === subscriber.iccid
          );
        }
        mappingStatus = "Available";
        return null;
      });

      const { lab, live } = subscriber;
      const networks: string[] = [];
      if (lab) networks.push("lab");
      if (live) networks.push("live");

      return {
        ...subscriber,
        network: networks.join(", "),
        mapping: mappedProbe,
        status: mappingStatus,
      } as SubscriberData;
    });
  }, [subscribers, mappings, probes]);

  return {
    data: subscriberData,
    isLoading: isLoadingSubscribers || isLoadingMappings || isLoadingProbes,
  };
};
