import { useCallback, useContext } from "react";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { SubscribersContext } from "@tessa-portal/features/subscribers/context/SubscribersContext";
import { Subscriber } from "@tessa-portal/types/services/simManager";

type FieldType = {
  label: string;
  name: string;
  rules?: Record<string, string>;
  readonly?: boolean;
  inputModeType?: "numeric" | "tel" | "text" | "none" | undefined;
  type?:
    | "number"
    | "text"
    | "hidden"
    | "email"
    | "password"
    | "tel"
    | "date"
    | "month"
    | "week"
    | "time"
    | "datetime-local"
    | "url"
    | undefined;
};

export const fields: FieldType[] = [
  {
    label: "ICCID",
    name: "iccid",
    rules: { required: "ICCID is required" },
    type: "number",
    readonly: true,
  },
  {
    label: "MSISDN",
    name: "msisdn",
    rules: { required: "MSISDN is required" },
    type: "number",
    inputModeType: "numeric",
  },
  {
    label: "IMSI",
    name: "imsi",
    rules: { required: "IMSI is required" },
    type: "number",
  },
  {
    label: "Tariff",
    name: "tariff",
    type: "text",
  },
  {
    label: "WNW",
    name: "wnw",
    type: "number",
  },
  {
    label: "OP WNW",
    name: "op_wnw",
    type: "text",
  },
  {
    label: "PSP",
    name: "psp",
    type: "text",
  },
  {
    label: "Wholesale ID",
    name: "wholesale_id",
    type: "text",
  },
  {
    label: "ITG ID",
    name: "itg_id",
    type: "text",
  },
  {
    label: "PIN",
    name: "secret",
    type: "number",
  },
  {
    label: "Name",
    name: "name",
    type: "text",
  },
  {
    label: "Origin",
    name: "origin",
    type: "text",
  },
];

// Find subscriber and assign as selected subscriber for editing
export const useSubscriberHelper = (data: Subscriber[]) => {
  const { setSelectedSubscriber } = useContext(SubscribersContext);
  const handleRowSelectionChange = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const value =
        typeof updaterOrValue === "function"
          ? updaterOrValue({})
          : updaterOrValue;
      const selectedRows = Object.keys(value);
      const selectedRow = selectedRows[0];

      if (selectedRow) {
        const selectedSubscriber = data.find(
          (subscriber) => subscriber.id === Number(selectedRow),
        );

        if (selectedSubscriber) {
          setSelectedSubscriber(selectedSubscriber);
        }
      } else {
        setSelectedSubscriber(undefined);
      }
    },
    [data, setSelectedSubscriber],
  );

  return { handleRowSelectionChange };
};
