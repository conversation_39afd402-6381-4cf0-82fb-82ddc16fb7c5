import { useMemo } from "react";
import Table from "@tessa-portal/components/common/Table";
import { useColumns } from "./columns";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";
import { useSubscriberHelper } from "@tessa-portal/features/subscribers/helpers";
import { SubscriberData } from "../../hooks";

export const OfflineSimCardsTable = ({ data }: { data: SubscriberData[] }) => {
  const userTableSettings = useUserTableSettings("offline-sim-cards");
  const tableColumns = useColumns();
  const tableData = useMemo(() => {
    return data.filter(
      (subscriber) =>
        !subscriber.position &&
        (subscriber.type === "mappable" || subscriber.type === "static"),
    );
  }, [data]);
  const { handleRowSelectionChange } = useSubscriberHelper(tableData);

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        selectableRows={true}
        getRowId={(row) => String(row.id)}
        showSort
        enableSorting
        showSettingsMenu
        settingsMenuItems={["columns", "export"]}
        showTableActions
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
        onRowSelectionChange={handleRowSelectionChange}
      />
    </>
  );
};

export default OfflineSimCardsTable;
