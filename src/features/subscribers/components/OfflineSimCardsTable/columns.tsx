import { createColumnHelper } from "@tanstack/react-table";
import useCommonColumns, { CommonSubscriberColumns } from "../commonColumns";

const columnHelper = createColumnHelper<CommonSubscriberColumns>();

export const useColumns = () => {
  const commonColumns = useCommonColumns();
  return [
    columnHelper.accessor("iccid", {
      header: "ICCID",
    }),
    columnHelper.accessor("msisdn", {
      header: "MSISDN",
    }),
    columnHelper.accessor("imsi", {
      header: "IMSI",
    }),
    columnHelper.accessor("sim_type", {
      header: "SIM Type",
    }),
    ...commonColumns,
  ];
};
