import { createColumnHelper } from "@tanstack/react-table";
import SimStatusCell from "@tessa-portal/components/common/SimStatusCell/SimStatusCell";
import useCommonColumns, { CommonSubscriberColumns } from "../commonColumns";

const columnHelper = createColumnHelper<CommonSubscriberColumns>();

export const useColumns = () => {
  const commonColumns = useCommonColumns();
  return [
    columnHelper.accessor("status", {
      header: "Status",
      cell: (cell) => {
        return <SimStatusCell status={cell.row.original.status} />;
      },
    }),
    columnHelper.accessor("mapping", {
      header: "Assigned To",
    }),
    columnHelper.accessor("iccid", {
      header: "ICCID",
    }),
    columnHelper.accessor("msisdn", {
      header: "MSISDN",
    }),
    columnHelper.accessor("imsi", {
      header: "IMSI",
    }),
    columnHelper.accessor("sim_type", {
      header: "SIM Type",
    }),
    ...commonColumns,
  ];
};
