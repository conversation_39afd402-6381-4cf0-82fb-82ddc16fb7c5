import { useMemo } from "react";
import Table from "@tessa-portal/components/common/Table";
import { useColumns } from "./columns";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";
import { SubscriberData } from "../../hooks";
import { useSubscriberHelper } from "@tessa-portal/features/subscribers/helpers";
import { useReloadArrays } from "@tessa-portal/hooks/services/simManager/array";
import useUser from "@tessa-portal/hooks/useUser";
import { ScaleIconActionRefresh } from "@telekom/scale-components-react";

export const SimCardsTable = ({ data }: { data: SubscriberData[] }) => {
  const userTableSettings = useUserTableSettings("sim-cards");
  const tableColumns = useColumns();
  const tableData = useMemo(() => {
    return data.filter(
      (subscriber) =>
        subscriber.position &&
        (subscriber.type === "mappable" || subscriber.type === "static"),
    );
  }, [data]);
  const { handleRowSelectionChange } = useSubscriberHelper(tableData);
  const { mutateAsync: reloadArrays } = useReloadArrays();
  const { isAdmin, preferred_username: username } = useUser();

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        selectableRows={true}
        getRowId={(row) => String(row.id)}
        showSort
        enableSorting
        showSettingsMenu
        settingsMenuItems={["columns", "export"]}
        showTableActions
        tableActions={{
          customActions: [
            {
              label: "Reload SIMs",
              icon: <ScaleIconActionRefresh />,
              onClick: () => {
                reloadArrays();
              },
              hidden:
                !isAdmin && username !== "ivancic" && username !== "loeffler",
            },
          ],
        }}
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
        onRowSelectionChange={handleRowSelectionChange}
      />
    </>
  );
};

export default SimCardsTable;
