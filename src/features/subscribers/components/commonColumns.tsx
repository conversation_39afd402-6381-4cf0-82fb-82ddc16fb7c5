import { createColumnHelper } from "@tanstack/react-table";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";
import { SubscriberData } from "../hooks";

export interface CommonSubscriberColumns extends SubscriberData {
  network: string;
  tags: string[];
}

export const useCommonColumns = <T extends CommonSubscriberColumns>() => {
  const columnHelper = createColumnHelper<T>();
  return [
    columnHelper.accessor((col) => col.tags, {
      id: "tags",
      header: "Tags",
      cell: (cell) => {
        return cell.row.original?.tags.map((tag) => (
          <StyledScaleTag
            key={tag}
            color="yellow"
            size="small"
            type="strong"
            className="tw-px-1"
          >
            {tag}
          </StyledScaleTag>
        ));
      },
    }),
    columnHelper.accessor((col) => col.tariff, {
      id: "tariff",
      header: "Tariff",
    }),
    columnHelper.accessor((col) => col.wnw, {
      id: "wnw",
      header: "WNW",
    }),
    columnHelper.accessor((col) => col.op_wnw, {
      id: "op_wnw",
      header: "OP_WNW",
    }),
    columnHelper.accessor((col) => col.psp, {
      id: "psp",
      header: "PSP",
    }),
    columnHelper.accessor((col) => col.wholesale_id, {
      id: "wholesale_id",
      header: "WHOLESALE ID",
    }),
    columnHelper.accessor((col) => col.itg_id, {
      id: "itg_id",
      header: "ITG ID",
    }),
    columnHelper.accessor((col) => col.secret, {
      id: "secret",
      header: "PIN",
    }),
    columnHelper.accessor((col) => col.name, {
      id: "name",
      header: "Name",
    }),
    columnHelper.accessor((col) => col.origin, {
      id: "origin",
      header: "Origin",
    }),
    columnHelper.accessor((col) => col.prepaid, {
      id: "prepaid",
      header: "Prepaid",
    }),
    columnHelper.accessor((col) => col.type, {
      id: "type",
      header: "Type",
    }),
    columnHelper.accessor((col) => col.modified, {
      id: "modified",
      header: "Modified",
    }),
    columnHelper.accessor((col) => col.position, {
      id: "position",
      header: "Position",
    }),
    columnHelper.accessor((col) => col.network, {
      id: "network",
      header: "Network",
      cell: ({ row }) => {
        const { lab, live } = row.original;
        const networks: string[] = [];
        if (lab) networks.push("lab");
        if (live) networks.push("live");
        return <>{networks.join(", ")}</>;
      },
    }),
  ];
};

export default useCommonColumns;
