import { useMemo, useState } from "react";
import FileHandler from "@tessa-portal/components/common/FileHandler/FileHandler";
import Table from "@tessa-portal/components/common/Table";
import { Subscriber } from "@tessa-portal/types/services/simManager";
import { subscriberSchema } from "@tessa-portal/helpers/parsers/subscriberSchema";
import { useColumns } from "./columns";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";

import {
  useAddSubscriber,
  useEditSubscriber,
  useGetSubscribers,
} from "@tessa-portal/hooks/services/simManager/subscriber";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";

// TODO: is this only used for SIM cards or all subscribers?
export const ImportSubscribers = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [data, setData] = useState<any[] | undefined>([]);
  const { data: subscribers } = useGetSubscribers();
  const toast = useToastNotification();
  const tableColumns = useColumns();

  const { mutateAsync: addSubscriber } = useAddSubscriber();
  const { mutateAsync: editSubscriber } = useEditSubscriber();

  const handleDataParsed = <T,>(parsedData: T[]) => {
    setData(parsedData);
  };

  const omitColumns = [
    "import_action",
    "id",
    "modified",
    "position",
    "status",
    "network",
    "mapping",
    "assigned_to",
  ];

  const processedData = useMemo(() => {
    if (!subscribers || !data) return [];
    return data.map((sim) => {
      return {
        ...sim,
        import_action: subscribers?.find((sub) => sub.iccid === sim.iccid)
          ? "update"
          : "create",
      };
    });
  }, [data, subscribers]);

  const handleUpdateSubscriber = async (subscriber: Subscriber) => {
    const existingSubscriber = subscribers?.find(
      (sub) => sub.iccid === subscriber.iccid,
    );

    if (existingSubscriber) {
      const filterd = Object.keys(existingSubscriber).filter(
        (key) => !omitColumns.includes(key),
      );

      const existingSubscriberFiltered = Object.entries(filterd).reduce(
        (acc, [key, value]) => {
          if (value !== null && value !== "") {
            acc[key] = value;
          }
          return acc;
        },
        {} as Subscriber,
      );

      await editSubscriber({
        id: existingSubscriber.id,
        subscriber: { ...existingSubscriberFiltered, ...subscriber },
      });
    }
  };

  const handleAddSims = async () => {
    const promises = processedData.map(async (subscriber: Subscriber) => {
      const action = subscriber.import_action as "create" | "update";

      const filtered = Object.keys(subscriber).filter(
        (key) => !omitColumns.includes(key),
      );

      const newSubscriber = Object.entries(filtered).reduce(
        (acc, [key, value]) => {
          if (value !== null && value !== "") {
            acc[key] = value;
          }
          return acc;
        },
        {} as Subscriber,
      );

      if (action === "create") {
        await addSubscriber(newSubscriber);
      } else if (action === "update") {
        await handleUpdateSubscriber(newSubscriber);
      }
    });

    await Promise.all(promises);
  };

  const handleClick = async () => {
    await handleAddSims()
      .then(() => {
        setData(undefined);
        toast.open({
          heading: "Completed",
          text: "Subscribers added successfully",
          variant: "success",
        });
      })
      .catch((e) => {
        toast.open({
          heading: "Error",
          text: e.message,
          variant: "danger",
        });
      });
  };

  if (!subscribers) return <LoadingSpinner />;

  return (
    <div>
      <FileHandler<Subscriber>
        onDataParsed={handleDataParsed}
        schema={subscriberSchema}
      />
      {data && data?.length > 0 && (
        <>
          <Table columns={tableColumns} data={processedData} />
          <StyledScaleButton onClick={handleClick}>
            Add Subscribers
          </StyledScaleButton>
        </>
      )}
    </div>
  );
};

export default ImportSubscribers;
