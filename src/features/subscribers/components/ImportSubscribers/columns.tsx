import { createColumnHelper } from "@tanstack/react-table";
import { Subscriber } from "@tessa-portal/types/services/simManager";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";
export interface ImportSubscriberColumns
  extends Omit<Subscriber, "id" | "modified" | "status" | "position"> {
  action: string;
}

const columnHelper = createColumnHelper<ImportSubscriberColumns>();
export const useColumns = () => {
  return [
    columnHelper.accessor("import_action", {
      header: "Action",
    }),
    columnHelper.accessor("iccid", {
      header: "ICCID",
    }),
    columnHelper.accessor("msisdn", {
      header: "MSISDN",
    }),
    columnHelper.accessor("imsi", {
      header: "IMSI",
    }),
    columnHelper.accessor("sim_type", {
      header: "SIM Type",
    }),
    columnHelper.accessor((col) => col.tags, {
      id: "tags",
      header: "Tags",
      cell: (cell) => {
        if (Array.isArray(cell.row.original?.tags)) {
          return cell.row.original?.tags.map((tag: string) => (
            <StyledScaleTag
              key={tag}
              color="yellow"
              size="small"
              type="strong"
              className="tw-px-1"
            >
              {tag}
            </StyledScaleTag>
          ));
        }
      },
    }),
    columnHelper.accessor((col) => col.tariff, {
      id: "tariff",
      header: "Tariff",
    }),
    columnHelper.accessor((col) => col.wnw, {
      id: "wnw",
      header: "WNW",
    }),
    columnHelper.accessor((col) => col.op_wnw, {
      id: "op_wnw",
      header: "OP_WNW",
    }),
    columnHelper.accessor((col) => col.psp, {
      id: "psp",
      header: "PSP",
    }),
    columnHelper.accessor((col) => col.wholesale_id, {
      id: "wholesale_id",
      header: "WHOLESALE ID",
    }),
    columnHelper.accessor((col) => col.itg_id, {
      id: "itg_id",
      header: "ITG ID",
    }),
    columnHelper.accessor((col) => col.secret, {
      id: "secret",
      header: "PIN",
    }),
    columnHelper.accessor((col) => col.name, {
      id: "name",
      header: "Name",
    }),
    columnHelper.accessor((col) => col.origin, {
      id: "origin",
      header: "Origin",
    }),
    columnHelper.accessor((col) => col.prepaid, {
      id: "prepaid",
      header: "Prepaid",
    }),
    columnHelper.accessor((col) => col.type, {
      id: "type",
      header: "Type",
    }),
    columnHelper.accessor((col) => col.lab, {
      id: "lab",
      header: "Lab",
    }),
    columnHelper.accessor((col) => col.live, {
      id: "live",
      header: "Live",
    }),
  ];
};
