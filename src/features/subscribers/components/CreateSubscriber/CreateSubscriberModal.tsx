import Modal from "@tessa-portal/components/common/Modal";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import CreateSubscriberForm from "./CreateSubscriberForm";

type InsertSimCardModalProps = {
  onClose: () => void;
  subscriberType: "fixed_line" | "sip";
  heading: string;
};

const CreateSubscriberModal = ({
  onClose: handleClose,
  subscriberType,
  heading,
}: InsertSimCardModalProps) => {
  return (
    <Modal
      disablePortal
      heading={heading}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.75 * var(--scl-grid-max-width))));}"
      }
    >
      <CreateSubscriberForm
        defaultValues={{
          msisdn: "",
          lab: false,
          live: false,
          tags: [],
          type: subscriberType,
        }}
        onSubmit={() => {}}
      />
      <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4 tw-pb-6">
        <Modal.CloseButton type="button" variant="secondary">
          Cancel
        </Modal.CloseButton>
        <StyledScaleButton variant="primary" type="submit">
          Add
        </StyledScaleButton>
      </div>
    </Modal>
  );
};

export default CreateSubscriberModal;
