import { useMemo, useState } from "react";
import Table from "@tessa-portal/components/common/Table";
import { useColumns } from "./columns";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";
import { useSubscriberHelper } from "@tessa-portal/features/subscribers/helpers";
import { SubscriberData } from "../../hooks";
import CreateSubscriberModal from "../CreateSubscriber/CreateSubscriberModal";
import { ScaleIconActionAdd } from "@telekom/scale-components-react";

export const FixedNumbersTable = ({ data }: { data: SubscriberData[] }) => {
  const [showCreateSubscriberModal, setShowCreateSubscriberModal] =
    useState(false);
  const userTableSettings = useUserTableSettings("fixed-numbers-table");
  const tableColumns = useColumns();
  const tableData = useMemo(() => {
    return data.filter(
      (subscriber) => !subscriber.position && subscriber.type === "fixed_line",
    );
  }, [data]);
  const { handleRowSelectionChange } = useSubscriberHelper(tableData);

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        selectableRows={true}
        getRowId={(row) => String(row.id)}
        showSort
        enableSorting
        showSettingsMenu
        showTableActions
        settingsMenuItems={["columns", "export"]}
        tableActions={{
          customActions: [
            {
              label: "Add",
              icon: <ScaleIconActionAdd />,
              onClick: () => {
                setShowCreateSubscriberModal(true);
              },
            },
          ],
        }}
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
        onRowSelectionChange={handleRowSelectionChange}
      />
      {showCreateSubscriberModal && (
        <CreateSubscriberModal
          heading="Create Fixed Number"
          subscriberType="fixed_line"
          onClose={() => setShowCreateSubscriberModal(false)}
        />
      )}
    </>
  );
};

export default FixedNumbersTable;
