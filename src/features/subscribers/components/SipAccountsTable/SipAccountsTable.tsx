import { useMemo, useState } from "react";
import Table from "@tessa-portal/components/common/Table";
import { useColumns } from "./columns";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";
import CreateSubscriberModal from "../CreateSubscriber/CreateSubscriberModal";
import { useSubscriberHelper } from "@tessa-portal/features/subscribers/helpers";
import { SubscriberData } from "../../hooks";
import { ScaleIconActionAdd } from "@telekom/scale-components-react";

export const SipAccountsTable = ({ data }: { data: SubscriberData[] }) => {
  const [showCreateSubscriberModal, setShowCreateSubscriberModal] =
    useState(false);
  const userTableSettings = useUserTableSettings("sip-accounts-table");
  const tableColumns = useColumns();
  const tableData = useMemo(() => {
    return data.filter(
      (subscriber) => !subscriber.position && subscriber.type === "sip",
    );
  }, [data]);
  const { handleRowSelectionChange } = useSubscriberHelper(tableData);

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        selectableRows={true}
        getRowId={(row) => String(row.id)}
        showSort
        enableSorting
        showSettingsMenu
        settingsMenuItems={["columns", "export"]}
        showTableActions
        tableActions={{
          customActions: [
            {
              label: "Add",
              icon: <ScaleIconActionAdd />,
              onClick: () => {
                setShowCreateSubscriberModal(true);
              },
            },
          ],
        }}
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
        onRowSelectionChange={handleRowSelectionChange}
      />
      {showCreateSubscriberModal && (
        <CreateSubscriberModal
          heading="Create SIP Account"
          subscriberType="sip"
          onClose={() => setShowCreateSubscriberModal(false)}
        />
      )}
    </>
  );
};

export default SipAccountsTable;
