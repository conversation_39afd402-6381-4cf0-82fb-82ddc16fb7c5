import { createColumnHelper } from "@tanstack/react-table";
import useCommonColumns, { CommonSubscriberColumns } from "../commonColumns";

const columnHelper = createColumnHelper<CommonSubscriberColumns>();

export const useColumns = () => {
  const commonColumns = useCommonColumns<CommonSubscriberColumns>();
  return [
    columnHelper.accessor("msisdn", {
      header: "Number",
    }),
    columnHelper.accessor("assigned_to", {
      header: "Assigned To",
      cell: (cell) => {
        return cell.row.original.assigned_to?.probe_name;
      },
    }),
    ...commonColumns,
  ];
};
