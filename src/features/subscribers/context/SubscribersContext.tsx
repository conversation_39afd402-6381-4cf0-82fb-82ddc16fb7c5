import { Subscriber } from "@tessa-portal/types/services/simManager";
import { createContext, Dispatch, SetStateAction } from "react";

export type SubscribersContext = {
  selectedSubscriber?: Subscriber;
  setSelectedSubscriber: Dispatch<SetStateAction<Subscriber | undefined>>;
  newSubscriber: () => void;
  showModal: boolean;
};

const subscribersContext: SubscribersContext = {
  selectedSubscriber: undefined,
  setSelectedSubscriber: () => {
    throw new Error("setSelectedSubscriber function is not initialized");
  },
  newSubscriber: () => {
    throw new Error("newSubscribers function is not initialized");
  },
  showModal: false,
};

export const SubscribersContext =
  createContext<SubscribersContext>(subscribersContext);
