import { getReservationStatus } from "@tessa-portal/features/manualTesting/helpers/getReservationStatus";
import { useGetUsers } from "@tessa-portal/hooks/services/user";
import { Reservation } from "@tessa-portal/types/services/reservations";

export interface ReservationInfoProps {
  reservation?: Reservation;
}
export const ReservationInfo = ({ reservation }: ReservationInfoProps) => {
  const { data: users } = useGetUsers();

  const resStatus = getReservationStatus(reservation?.from, reservation?.until);

  const statusColorClass =
    resStatus === "Upcoming"
      ? "tw-text-blue-600"
      : resStatus === "Reserved"
        ? "tw-text-green-500"
        : "tw-text-gray-500";

  const user = users?.find((user) => user.uid === reservation?.user_id);

  const labelClasses = "tw-mr-2 tw-text-sm tw-font-light tw-text-gray-500";

  return (
    <div className="tw-flex tw-flex-col tw-gap-2">
      {/* From Date */}
      <div className="tw-flex tw-items-center">
        <div className={labelClasses}>From:</div>
        <span>{reservation?.from.toLocaleString()}</span>
      </div>

      {/* Until Date */}
      <div className="tw-flex tw-items-center">
        <div className={labelClasses}>Until:</div>
        <span>{reservation?.until.toLocaleString()}</span>
      </div>

      {/* Reserved By */}
      <div className="tw-flex tw-items-center">
        <div className={labelClasses}>By:</div>
        {user ? (
          <a
            href={`mailto:${user.mail}`}
            className="tw-hover:underline tw-text-blue-300"
          >
            {user.cn}
          </a>
        ) : (
          <span className="tw-text-gray-400">Unknown User</span>
        )}
      </div>

      {/* Status */}
      <div className="tw-flex tw-items-center">
        <div className={labelClasses}>Status:</div>
        <span className={statusColorClass}>{resStatus}</span>
      </div>
      {/* TODO: show how long until reservation expires */}
      {/* {resStatus === "active" && (
        <div className="tw-mt-3">
          <div>{reservation.until.toLocaleString()}</div>
        </div>
      )} */}
    </div>
  );
};
