import { Probe } from "@tessa-portal/types/services/probe";

export interface DeviceInfoProps {
  probe: Probe;
}
export const DeviceInfo = ({ probe }: DeviceInfoProps) => {
  return (
    <div className="tw-flex tw-flex-row">
      <div className="tw-relative tw-mr-9 tw-inline-block">
        <div className="tw-absolute tw-left-[10%] tw-top-1/2 -tw-translate-x-1/2 -tw-translate-y-1/2 tw-rotate-[-90deg] tw-text-center tw-text-2xl tw-text-gray-500">
          {probe?.devices?.[0]?.type?.replace("_", " ")}
        </div>
      </div>
      <div className="tw-flex tw-flex-col tw-gap-2">
        <h1>
          {probe.location} - {probe.probe_alias}
        </h1>
        <p>{probe.devices?.[0].name}</p>
        <div
          style={{
            color:
              probe.status === "online" &&
              probe?.devices &&
              probe?.devices[0]?.status === "online"
                ? "green"
                : "red",
          }}
        >
          {probe.status === "online" &&
          probe?.devices &&
          probe?.devices[0]?.status === "online"
            ? "online"
            : "offline"}
        </div>
      </div>
    </div>
  );
};
