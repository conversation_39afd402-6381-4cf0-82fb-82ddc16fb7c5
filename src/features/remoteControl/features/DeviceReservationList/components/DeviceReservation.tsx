// import CountDown from 'components/CountDown';
import { DeviceInfo } from "./DeviceInfo";
import { ReservationInfo } from "./ReservationInfo";
import { ControlButtons } from "./ControlButtons";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";

// TODO: implement countdown
const DeviceReservation = ({
  deviceReservation,
}: {
  deviceReservation: ProbeData;
}): JSX.Element => {
  return (
    <div
      className="tw-flex tw-flex-row tw-justify-between tw-rounded-lg tw-bg-[var(--telekom-color-background-surface)] tw-px-10 tw-py-4"
      style={{ boxShadow: "var(--telekom-shadow-raised-standard)" }}
    >
      <DeviceInfo probe={deviceReservation} />
      <ReservationInfo reservation={deviceReservation.reservation} />
      <ControlButtons probe={deviceReservation} />
    </div>
  );
};

export default DeviceReservation;
