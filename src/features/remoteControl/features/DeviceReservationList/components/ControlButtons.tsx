import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import useWindowsManager from "@tessa-portal/hooks/useWindowManager";
import { Probe } from "@tessa-portal/types/services/probe";
// import { StfDevice } from "@tessa-portal/types/services/stf";

export const ControlButtons = ({
  probe,
  // stfDevice,
}: {
  probe: Probe;
  // stfDevice?: StfDevice;
}) => {
  const { openWindow, closeWindow, focusWindow, getState } =
    useWindowsManager();

  const handleOpenWindow = () => {
    openWindow({
      id: probe.probe_alias,
      title: probe.probe_alias,
      url: `${window.location.origin}/manual-testing/${probe.probe_alias}`,
      size: { width: 1070, height: 900 },
    });
  };

  const renderDevicesSwitch = () => {
    switch (probe?.devices?.[0]?.type) {
      case "Android":
      case "iOS":
        if (getState(probe?.probe_alias) !== "OPEN") {
          return (
            <StyledScaleButton variant="primary" onClick={handleOpenWindow}>
              Use device
            </StyledScaleButton>
          );
        } else {
          return (
            <>
              <StyledScaleButton
                variant="secondary"
                onClick={() => closeWindow(probe?.probe_alias)}
              >
                Release Device
              </StyledScaleButton>
              <StyledScaleButton
                variant="secondary"
                onClick={() => focusWindow(probe?.probe_alias)}
              >
                Focus Device
              </StyledScaleButton>
            </>
          );
        }
      default:
        return null;
    }
  };
  return (
    <div className="tw-grid tw-w-36 tw-grid-cols-1 tw-grid-rows-2 tw-gap-4 tw-py-2">
      {renderDevicesSwitch()}
    </div>
  );
};
