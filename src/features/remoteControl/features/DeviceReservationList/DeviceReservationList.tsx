import { useMemo } from "react";
import moment from "moment";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import DeviceReservation from "./components/DeviceReservation";
import {
  useGetProbeData,
  ProbeData,
} from "@tessa-portal/hooks/util/useGetProbeData";
import useUser from "@tessa-portal/hooks/useUser";

interface ReservationGroup {
  myReservations: ProbeData[];
  otherReservations: ProbeData[];
}

const DeviceReservationList = () => {
  const { data: probeData, isLoading } = useGetProbeData();
  const { userId, isAdmin } = useUser();

  const { myReservations, otherReservations } =
    probeData.reduce<ReservationGroup>(
      (acc, probe) => {
        if (moment(probe.reservation?.until).isAfter(moment())) {
          if (String(probe.reservation?.user_id) === userId) {
            acc.myReservations.push(probe);
          } else if (String(probe.reservation?.user_id) !== userId && isAdmin) {
            acc.otherReservations.push(probe);
          }
        }
        return acc;
      },
      { myReservations: [], otherReservations: [] },
    );

  const reservedProbes = useMemo(() => {
    const sortedMyReservations = myReservations.sort(
      (r1, r2) =>
        moment(r2.reservation?.from).unix() -
        moment(r1.reservation?.from).unix(),
    );

    const sortedOtherReservations = otherReservations.sort(
      (r1, r2) =>
        moment(r2.reservation?.from).unix() -
        moment(r1.reservation?.from).unix(),
    );

    const combinedSortedList = [
      ...sortedMyReservations,
      ...sortedOtherReservations,
    ];

    return combinedSortedList;
  }, [myReservations, otherReservations]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      {reservedProbes.map((device) => (
        <DeviceReservation key={device.probe_name} deviceReservation={device} />
      ))}
    </div>
  );
};

export default DeviceReservationList;
