import { SubmitH<PERSON><PERSON>, useForm, FormProvider } from "react-hook-form";
import { useCreateReservation } from "@tessa-portal/hooks/services/reservation";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import moment from "moment";
import { scaleDateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";

export type CreateReservationFormValues = {
  probes: string[];
  reservation: ReservationTimeData;
};

// TODO: delete this file

export const DeviceReservationPanel = ({
  probeName,
}: {
  probeName: string;
}) => {
  const { mutateAsync: createReservation } = useCreateReservation();

  const defaultValues: CreateReservationFormValues = {
    probes: [probeName],
    reservation: {
      from: moment().format(scaleDateTimeFormat),
      until: moment().add(2, "hour").format(scaleDateTimeFormat),
    },
  };

  const form = useForm<CreateReservationFormValues>({
    mode: "all",
    defaultValues,
  });

  const onSubmit: SubmitHandler<CreateReservationFormValues> = async (data) => {
    const { reservation, probes } = data;

    const parseDate = (dateStr: string): string => {
      return moment(dateStr, scaleDateTimeFormat).toISOString();
    };

    let fromDate = parseDate(reservation.from);
    const untilDate = parseDate(reservation.until);

    if (moment(reservation.from, scaleDateTimeFormat).isBefore(moment())) {
      fromDate = moment().add(5, "second").toISOString();
    }

    probes.forEach((probe) => {
      createReservation({
        probe_name: probe,
        reservation: {
          from: fromDate,
          until: untilDate,
        },
      });
    });
  };

  return (
    <FormProvider {...form}>
      <h2 className="tw-mb-4 tw-text-lg tw-font-bold">
        No active reservation found
      </h2>
      <h2 className="tw-mb-4 tw-text-lg tw-font-bold">Reserve Device</h2>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="tw-flex tw-justify-evenly tw-gap-4 tw-pb-4">
          <TextField
            className="tw-w-full"
            label="From"
            name="reservation.from"
            rules={{
              validate: (value) =>
                moment(value).add(5, "second").isAfter(moment()) ||
                "From date must be in the future",
              required: "From date is required",
            }}
            step="60"
            type="datetime-local"
          />
          <TextField
            label="Until"
            className="tw-w-full"
            name="reservation.until"
            rules={{
              validate: (value) =>
                moment(value).isAfter(moment()) ||
                "Until date must be in the future",
              required: "From date is required",
            }}
            step="60"
            type="datetime-local"
          />
        </div>
        <StyledScaleButton variant="primary" type="submit">
          Reserve
        </StyledScaleButton>
      </form>
    </FormProvider>
  );
};
