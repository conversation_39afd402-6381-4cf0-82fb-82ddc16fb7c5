import { useState, useRef } from "react";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import clsx from "clsx";
import { DeviceDisplay } from "@tessa-portal/providers/DeviceDisplayProvider/DeviceDisplayProvider";
import { StfDevice } from "@tessa-portal/types/services/stf";
import useDeviceDisplay from "@tessa-portal/hooks/useDeviceDisplay";
import ContextMenu from "@tessa-portal/features/remoteControl/components/ContextMenu/ContextMenu";
import useContextMenu from "@tessa-portal/features/remoteControl/components/ContextMenu/useContextMenu";

type ScreenData = {
  deviceDisplay: DeviceDisplay;
  stfDevice: StfDevice;
  disableControl?: boolean;
};

let seq = -1;
const cycle = 100;

export const Screen = ({
  deviceDisplay,
  stfDevice,
  disableControl,
}: ScreenData) => {
  const imgRef = useRef<HTMLImageElement | null>(null);
  const { xPos, yPos, showMenu } = useContextMenu(imgRef);

  const [swiping, setSwiping] = useState(false);
  const [throttleTime, setThrottleTime] = useState(0);

  const { getControlSocket } = useDeviceDisplay();

  const sendMsg = (msg: string, ws: WebSocket | null) => {
    if (ws && ws.OPEN) ws.send(msg);
    else console.log("unable to send msg to websocket");
  };

  // send msg via command websocket
  const sendCmdMsg = (msg: string) => {
    sendMsg(msg, getControlSocket());
  };

  // send msg[] via command websocket
  const sendCmdArray = (msgs: string[]) => {
    msgs.forEach((item) => sendCmdMsg(item));
  };

  const getRealPoint = (xFraction, yFraction) => {
    let refPoint = { x: 0, y: 0 };
    let swap = false;
    switch (deviceDisplay.info?.orientation) {
      default:
      case 0:
        break;
      case 90:
        refPoint = { x: 0, y: 1 };
        swap = true;
        break;
      case 180:
        refPoint = { x: 1, y: 1 };
        break;
      case 270:
        refPoint = { x: 1, y: 0 };
        swap = true;
        break;
    }
    return swap
      ? {
          y: Math.abs(refPoint.x - xFraction),
          x: Math.abs(refPoint.y - yFraction),
        }
      : {
          x: Math.abs(refPoint.x - xFraction),
          y: Math.abs(refPoint.y - yFraction),
        };
  };

  // ws message sequence number generator
  const nextSeq = (): number => {
    seq += 1;
    if (seq >= cycle) seq = 0;
    return seq;
  };

  // handle swipe
  const handleMouseMove = (e) => {
    if (disableControl) return;
    if (!swiping || !imgRef.current) return;
    const time = Date.now();
    if (time - throttleTime > 40) {
      const imgRect = imgRef.current.getBoundingClientRect();
      const x = e.clientX - imgRect.left;
      const y = e.clientY - imgRect.top;

      const xFraction = x / imgRect.width;
      const yFraction = y / imgRect.height;

      const { x: realX, y: realY } = getRealPoint(xFraction, yFraction);

      const messageArray = [
        `42["input.touchMove","${stfDevice.channel}",{"seq":${nextSeq()},"contact":0,"x":${realX},"y":${realY},"pressure":0.5}]`,
        `42["input.touchCommit","${stfDevice.channel}",{"seq":${nextSeq()}}]`,
      ];
      sendCmdArray(messageArray);
      setThrottleTime(time);
    }
  };

  // handle mouse click or start of swipe
  const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    if (disableControl) return;
    if (e.button !== 0 || showMenu) return;
    e.preventDefault();

    if (!imgRef.current) return;
    setSwiping(true);

    const imgRect = imgRef.current?.getBoundingClientRect();
    const x = e.clientX - imgRect.left;
    const y = e.clientY - imgRect.top;

    const xFraction = x / imgRect.width;
    const yFraction = y / imgRect.height;

    const { x: realX, y: realY } = getRealPoint(xFraction, yFraction);

    const messageArray = [
      `42["input.gestureStart","${stfDevice.channel}",{"seq":${nextSeq()}}]`,
      `42["input.touchDown","${stfDevice.channel}",{"seq":${nextSeq()},"contact":0,"x":${realX},"y":${realY},"pressure":0.5}]`,
      `42["input.touchCommit","${stfDevice.channel}",{"seq":${nextSeq()}}]`,
    ];
    sendCmdArray(messageArray);
  };

  // send termination of mouse action
  const endMouseAction = () => {
    if (swiping) setSwiping(false);
    const messageArray = [
      `42["input.touchUp","${stfDevice.channel}",{"seq":${nextSeq()},"contact":0}]`,
      `42["input.touchCommit","${stfDevice.channel}",{"seq":${nextSeq()}}]`,
      `42["input.gestureStop","${stfDevice.channel}",{"seq":${nextSeq()}}]`,
    ];
    sendCmdArray(messageArray);
  };

  // end click or swipe when mouse up
  const handleMouseUp = (e: React.MouseEvent<HTMLImageElement>) => {
    if (disableControl) return;
    if (e.button !== 0 || showMenu) return;

    endMouseAction();
  };

  // stop swiping/clicking, when mouse leaves img borders
  const handleMouseOut = () => {
    if (disableControl) return;
    if (swiping) endMouseAction();
  };

  const CustomMenu = () => (
    <ul className="tw-shadow-lg tw-w-48 tw-overflow-hidden tw-rounded tw-border tw-border-gray-300 tw-bg-white">
      <li className="tw-cursor-pointer tw-px-4 tw-py-2 hover:tw-bg-gray-100">
        Copy
      </li>
      <li className="tw-cursor-pointer tw-px-4 tw-py-2 hover:tw-bg-gray-100">
        Paste
      </li>
    </ul>
  );

  return (
    <>
      <div className="tw-flex tw-flex-col">
        {!disableControl && (
          <ContextMenu
            xPos={xPos}
            yPos={yPos}
            showMenu={showMenu}
            menu={<CustomMenu />}
          />
        )}
        <img
          className={clsx(
            "tw-object-contain",
            disableControl ? "tw-select-none" : "tw-cursor-pointer",
            !deviceDisplay.image && "tw-opacity-50",
          )}
          ref={imgRef}
          src={deviceDisplay.image}
          alt=""
          tabIndex={0}
          width={`${deviceDisplay.screenSize?.width}px`}
          height={`${deviceDisplay.screenSize?.height}px`}
          draggable={false}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
          onMouseOut={handleMouseOut}
        />
        {!deviceDisplay.image && (
          <div className="tw-flex tw-max-h-[692px] tw-max-w-[311px] tw-flex-col tw-justify-center">
            <LoadingSpinner className="tw-relative tw-bottom-[50%] tw-m-auto tw-h-[2rem] tw-w-[2rem] tw-scale-150" />
          </div>
        )}
      </div>
    </>
  );
};
