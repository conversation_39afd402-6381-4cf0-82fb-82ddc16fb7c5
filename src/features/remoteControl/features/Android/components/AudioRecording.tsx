// import { useState, useEffect } from "react";
// import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
// import {
//   useAudioRecordingStart,
//   useAudioRecordingStop,
//   useAudioRecordingDownload,
//   usePlaySample,
// } from "@tessa-portal/hooks/services/audioRecording";
// import {
//   ScaleIconActionRecordNb,
//   ScaleIconContentStopwatch,
// } from "@telekom/scale-components-react";

// export const AudioRecording = ({ probeName }: { probeName: string }) => {
//   const [downloadName, setDownloadName] = useState<string>("");
//   const [recordingName, setRecordingName] = useState<string>("");
//   const [isRecording, setIsRecording] = useState(false);
//   const [timer, setTimer] = useState(0);
//   const { mutateAsync: startRecording } = useAudioRecordingStart();
//   const { mutateAsync: stopRecording } = useAudioRecordingStop();
//   const { mutateAsync: playSample } = usePlaySample();
//   const { data: downloadedFile, refetch: downloadAudioRecording } =
//     useAudioRecordingDownload(probeName, downloadName);

//   useEffect(() => {
//     if (!isRecording && timer <= 60) return;
//     const timerInt = setInterval(() => {
//       setTimer((time) => time + 1);
//     }, 1000);
//     return () => {
//       clearInterval(timerInt);
//     };
//   }, [isRecording]);

//   useEffect(() => {
//     if (timer >= 60) {
//       setIsRecording(false);
//       setTimer(0);
//     }
//   }, [timer]);

//   const fakeDownload = () => {
//     setDownloadName(recordingName);
//     // setRerender(!rerender);
//     const url = window.URL.createObjectURL(
//       new Blob([downloadName], { type: "audio/wav" }),
//     );
//     window.URL.revokeObjectURL(url);
//     // setRerender(!rerender);
//     downloadAudioRecording();
//   };

//   const handleStartRecording = () => {
//     const file = `audio_${new Date().toJSON().slice(0, 16)}.wav`;
//     startRecording({
//       probeName,
//       parameters: {
//         filename: file,
//         audiodevice: "plughw:CARD=Device,DEV=0",
//         stream: true,
//         args: { "-d": 60 },
//       },
//     });
//     setRecordingName(file);
//     setIsRecording(true);
//   };

//   const handleStopRecording = () => {
//     stopRecording({ probeName, parameters: {} });
//     setIsRecording(false);
//     setTimer(0);
//     fakeDownload();
//   };

//   const handlePlaySample = () => {
//     playSample({
//       probeName,
//       parameters: {
//         filename: "channel_check_speech_m.wav",
//         audiodevice: "plughw:CARD=Device,DEV=0",
//       },
//     });
//   };

//   const handleDownload = () => {
//     setDownloadName(recordingName);
//     // setRerender(!rerender);
//     downloadAudioRecording();
//     const url = window.URL.createObjectURL(
//       new Blob([downloadedFile], { type: "audio/wav" }),
//     );
//     const link = document.createElement("a");
//     link.href = url;
//     link.setAttribute("download", downloadName);
//     document.body.appendChild(link);
//     link.click();
//     window.URL.revokeObjectURL(url);
//     link.href = "";
//   };

//   return (
//     <div>
//       {!isRecording && (
//         <StyledScaleButton
//           variant="secondary"
//           size="small"
//           onClick={handleStartRecording}
//           disabled={isRecording}
//         >
//           <ScaleIconActionRecordNb />
//           Record
//         </StyledScaleButton>
//       )}
//       {isRecording && (
//         <StyledScaleButton
//           variant="secondary"
//           size="small"
//           onClick={handleStopRecording}
//           disabled={!isRecording}
//         >
//           <ScaleIconContentStopwatch />
//           Stop
//         </StyledScaleButton>
//       )}
//       <StyledScaleButton
//         variant="secondary"
//         size="small"
//         onClick={handlePlaySample}
//       >
//         Play Sample
//       </StyledScaleButton>
//       <p className="tw-font-mono">
//         {isRecording &&
//           `Recording: 0:${timer < 10 ? `0${timer}` : timer} / 0:60`}
//       </p>
//       {downloadedFile && (
//         <>
//           <audio controls>
//             <source
//               src={URL.createObjectURL(
//                 new Blob([downloadedFile], { type: "audio/wav" }),
//               )}
//               type="audio/wav"
//             />
//             <track
//               src=""
//               kind="captions"
//               srcLang="en"
//               label="english_captions"
//             />
//             Your browser does not support the audio element.
//           </audio>
//           <StyledScaleButton
//             variant="secondary"
//             size="small"
//             onClick={handleDownload}
//           >
//             Download
//           </StyledScaleButton>
//         </>
//       )}
//     </div>
//   );
// };
import { useState, useEffect, useMemo } from "react";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import {
  useAudioRecordingStart,
  useAudioRecordingStop,
  useAudioRecordingDownload,
  usePlaySample,
} from "@tessa-portal/hooks/services/audioRecording";
import {
  ScaleIconActionPlayNb,
  ScaleIconActionRecordNb,
  ScaleIconContentStopwatch,
} from "@telekom/scale-components-react";

export const AudioRecording = ({ probeName }: { probeName: string }) => {
  const [downloadName, setDownloadName] = useState<string>("");
  const [recordingName, setRecordingName] = useState<string>("");
  const [isRecording, setIsRecording] = useState(false);
  const [timer, setTimer] = useState(0);
  const { mutate: startRecording } = useAudioRecordingStart();
  const { mutate: stopRecording } = useAudioRecordingStop();
  const { mutate: playSample } = usePlaySample();
  const { data: downloadedFile, refetch: downloadAudioRecording } =
    useAudioRecordingDownload(probeName, downloadName);

  useEffect(() => {
    let timerInterval;
    if (isRecording) {
      timerInterval = setInterval(() => {
        setTimer((prevTime) => prevTime + 1);
      }, 1000);
    } else {
      setTimer(0);
    }

    return () => clearInterval(timerInterval);
  }, [isRecording]);

  const audioUrl = useMemo(() => {
    if (!downloadedFile) return;
    return URL.createObjectURL(
      new Blob([downloadedFile], { type: "audio/wav" }),
    );
  }, [downloadedFile]);

  useEffect(() => {
    if (timer >= 60) {
      handleStopRecording();
    }
  }, [timer]);

  const fakeDownload = () => {
    setDownloadName(recordingName);
    // setRerender(!rerender);
    const url = window.URL.createObjectURL(
      new Blob([downloadName], { type: "audio/wav" }),
    );
    window.URL.revokeObjectURL(url);
    // setRerender(!rerender);
    downloadAudioRecording();
  };

  const handleStartRecording = () => {
    const file = `audio_${new Date().toJSON().slice(0, 16)}.wav`;
    startRecording({
      probeName,
      parameters: {
        filename: file,
        audiodevice: "plughw:CARD=Device,DEV=0",
        stream: true,
        args: { "-d": 60 },
      },
    });
    setRecordingName(file);
    setIsRecording(true);
  };

  const handleStopRecording = async () => {
    stopRecording({ probeName, parameters: {} });
    setIsRecording(false);
    setTimer(0);
    fakeDownload();
  };

  const handlePlaySample = () => {
    playSample({
      probeName,
      parameters: {
        filename: "channel_check_speech_m.wav",
        audiodevice: "plughw:CARD=Device,DEV=0",
      },
    });
  };

  const handleDownload = () => {
    if (!downloadedFile) return;
    const url = URL.createObjectURL(
      new Blob([downloadedFile], { type: "audio/wav" }),
    );
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", downloadName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div>
      <h3 className="tw-pb-2 tw-text-style-ui-bold">Audio</h3>
      <div className="tw-flex tw-gap-2">
        {!isRecording && (
          <StyledScaleButton
            variant="secondary"
            size="small"
            onClick={handleStartRecording}
            disabled={isRecording}
          >
            <ScaleIconActionRecordNb decorative={true} size={12} />
            Record
          </StyledScaleButton>
        )}
        {isRecording && (
          <StyledScaleButton
            variant="secondary"
            size="small"
            onClick={handleStopRecording}
            disabled={!isRecording}
          >
            <ScaleIconContentStopwatch decorative={true} size={12} />
            Stop
          </StyledScaleButton>
        )}
        <StyledScaleButton
          variant="secondary"
          size="small"
          onClick={handlePlaySample}
        >
          <ScaleIconActionPlayNb decorative={true} size={12} />
          Play Sample
        </StyledScaleButton>
      </div>
      <p className="tw-font-mono">
        {isRecording &&
          `Recording: 0:${timer < 10 ? `0${timer}` : timer} / 0:60`}
      </p>
      {!isRecording && audioUrl && (
        <>
          <audio controls className="tw-my-2">
            <source src={audioUrl} type="audio/wav" />
            <track
              src=""
              kind="captions"
              srcLang="en"
              label="english_captions"
            />
            Your browser does not support the audio element.
          </audio>
          <StyledScaleButton
            variant="secondary"
            size="small"
            onClick={handleDownload}
          >
            Download
          </StyledScaleButton>
        </>
      )}
    </div>
  );
};
