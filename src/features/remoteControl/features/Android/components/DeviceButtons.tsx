import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import {
  ScaleIconActionBackward,
  ScaleIconContentApplications,
  ScaleIconHomeHome,
} from "@telekom/scale-components-react";
import useDeviceDisplay from "@tessa-portal/hooks/useDeviceDisplay";

// phone hadrware & additional buttons/keys
const DeviceButtons = ({ channel }: { channel: string }) => {
  const { getControlSocket } = useDeviceDisplay();

  const sendMsg = (msg: string, ws: WebSocket | null) => {
    if (ws && ws.OPEN) ws.send(msg);
    else console.log("unable to send msg to websocket");
  };

  // const sendMsg = (msg: string) => {
  //   if (getControlSocket() && getControlSocket()?.OPEN) {
  //     getControlSocket()?.send(msg);
  //   }
  // };

  const sendKeyDown = (key: string) => {
    sendMsg(
      `42["input.keyDown","${channel}",{"key":"${key}"}]`,
      getControlSocket(),
    );
  };

  const sendKeyUp = (key: string) => {
    sendMsg(
      `42["input.keyUp","${channel}",{"key":"${key}"}]`,
      getControlSocket(),
    );
  };

  return (
    <div className="tw-flex tw-flex-row tw-justify-center tw-gap-2 tw-bg-[#e20074]">
      <StyledScaleButton
        className="tw-w-[25%]"
        key="home"
        onMouseDown={() => sendKeyDown("home")}
        onMouseUp={() => sendKeyUp("home")}
      >
        <ScaleIconHomeHome className="tw-m-auto tw-flex" />
      </StyledScaleButton>
      <StyledScaleButton
        className="tw-w-[25%]"
        key="app-switch"
        onMouseDown={() => sendKeyDown("app_switch")}
        onMouseUp={() => sendKeyUp("app_switch")}
      >
        <ScaleIconContentApplications className="tw-m-auto tw-flex" />
      </StyledScaleButton>
      <StyledScaleButton
        className="tw-w-[25%]"
        key="back"
        onMouseDown={() => sendKeyDown("back")}
        onMouseUp={() => sendKeyUp("back")}
      >
        <ScaleIconActionBackward className="tw-m-auto tw-flex" />
      </StyledScaleButton>
    </div>
  );
};
export default DeviceButtons;
