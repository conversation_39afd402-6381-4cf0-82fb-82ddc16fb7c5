import {
  ScaleIconActionRestart,
  ScaleIconAlertInformation,
  ScaleIconCommunicationFlightMode,
  ScaleIconContentSignal,
  ScaleIconDeviceMobileServices,
  ScaleIconServiceSettings,
} from "@telekom/scale-components-react";
import { IconButton } from "@tessa-portal/components/common/IconButton";
import CleanIcon from "@tessa-portal/assets/clean-icon.svg?react";
import ChromeIcon from "@tessa-portal/assets/chrome-icon.svg?react";
import useDeviceDisplay from "@tessa-portal/hooks/useDeviceDisplay";

interface DeviceActionsProps {
  channel: string;
  deviceVersion: string;
  deviceManufacturer: string;
}

export const DeviceActions = ({
  channel,
  deviceVersion,
  deviceManufacturer,
}: DeviceActionsProps) => {
  const { sendWithCallback } = useDeviceDisplay();

  const getCmdMsg = (cmd: string): string =>
    `{"command":"${cmd}", "timeout":10000}`;

  const handleAction = (action: string) => {
    switch (action) {
      case "restart":
        sendWithCallback("device.reboot", channel, () => null);
        break;
      case "clean":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          getCmdMsg("pm clear com.android.chrome & rm -rR /sdcard/Download/*"),
        );
        break;
      case "settings":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          getCmdMsg("am start -a android.settings.SETTINGS"),
        );
        break;
      case "network":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          getCmdMsg(
            Number(deviceVersion) >= 13
              ? "am start -n com.samsung.android.app.telephonyui/.netsettings.ui.NetSettingsActivity"
              : "am start -S -a android.settings.NETWORK_OPERATOR_SETTINGS",
          ),
        );
        break;
      case "flight":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          getCmdMsg("am start -S -a android.settings.AIRPLANE_MODE_SETTINGS"),
        );
        break;
      case "service":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          deviceManufacturer === "SAMSUNG"
            ? getCmdMsg(
                "am start --user 0 -a android.intent.action.DIAL && input text *#0011#",
              )
            : getCmdMsg(
                "su -c am start com.android.phone/com.android.phone.settings.RadioInfo ",
              ),
        );
        break;
      case "chrome":
        sendWithCallback(
          "browser.open",
          channel,
          () => null,
          '{"url":"", "browser":"com.android.chrome/com.google.android.apps.chrome.Main"}',
        );
        break;
      case "info":
        sendWithCallback(
          "shell.command",
          channel,
          () => null,
          getCmdMsg("am start -a android.settings.DEVICE_INFO_SETTINGS"),
        );
        break;
      default:
        break;
    }
  };

  return (
    <div>
      <h3 className="tw-pb-2 tw-text-style-ui-bold">Actions</h3>
      <div className="tw-mb-2 tw-flex tw-items-start tw-gap-2">
        <IconButton
          label="Restart"
          icon={<ScaleIconActionRestart />}
          onClick={() => handleAction("restart")}
        />
        <IconButton
          label="Clean device"
          icon={<CleanIcon />}
          onClick={() => handleAction("clean")}
        />
        <IconButton
          label="Settings"
          icon={<ScaleIconServiceSettings />}
          onClick={() => handleAction("settings")}
        />
        <IconButton
          label="Network settings"
          icon={<ScaleIconContentSignal />}
          onClick={() => handleAction("network")}
        />
        <IconButton
          label="Flight mode"
          icon={<ScaleIconCommunicationFlightMode />}
          onClick={() => handleAction("flight")}
        />
        <IconButton
          label="Service mode"
          icon={<ScaleIconDeviceMobileServices />}
          onClick={() => handleAction("service")}
        />
        <IconButton
          label="Chrome browser"
          icon={<ChromeIcon />}
          onClick={() => handleAction("chrome")}
        />
        <IconButton
          label="Device info"
          icon={<ScaleIconAlertInformation />}
          onClick={() => handleAction("info")}
        />
      </div>
    </div>
  );
};
