import { useEffect } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import useDeviceDisplay from "@tessa-portal/hooks/useDeviceDisplay";
import { StfDevice } from "@tessa-portal/types/services/stf";
import { Screen } from "./components/Screen";
import { ScaleDivider } from "@telekom/scale-components-react";
import { Tabs, Tab } from "@tessa-portal/components/common/Tabs";
import DeviceButtons from "./components/DeviceButtons";
import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { AudioRecording } from "./components/AudioRecording";
import { DeviceActions } from "./components/DeviceActions";
import { DeviceSim } from "../../components/DeviceSim";

interface AndroidProps {
  stfDevice?: StfDevice;
  reservationData: ProbeData;
}

const Android = ({ stfDevice, reservationData }: AndroidProps) => {
  const { openDeviceDisplay, getDeviceDisplay } = useDeviceDisplay();

  useEffect(() => {
    if (stfDevice) {
      const stfDisplayUrl = stfDevice.display.url;
      openDeviceDisplay(stfDevice.serial, stfDisplayUrl);
    }
  }, [stfDevice, openDeviceDisplay]);

  if (!stfDevice) return null;

  const deviceDisplay = getDeviceDisplay(stfDevice.serial);

  if (!deviceDisplay) return null;

  return (
    <div className="tw-flex tw-flex-col">
      <PanelGroup direction="horizontal" className="tw-h-full">
        <Panel defaultSize={40} minSize={10}>
          <div className="tw-flex tw-flex-col tw-overflow-auto">
            {stfDevice.ready && (
              <div className="tw-flex tw-flex-col">
                <div className="tw-flex tw-flex-1 tw-flex-col tw-items-center tw-justify-center">
                  <Screen deviceDisplay={deviceDisplay} stfDevice={stfDevice} />
                  <div className="tw-w-full tw-max-w-[600px]">
                    <DeviceButtons channel={stfDevice.channel} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </Panel>

        <PanelResizeHandle>
          <ScaleDivider vertical className="tw-mx-1" />
        </PanelResizeHandle>

        <Panel>
          <Tabs>
            <Tab label="Device">
              {/* <h3 className="tw-pb-2 tw-text-style-ui-bold">Attenuation level</h3> */}
              {/* <AttenuatorInfo /> */}
              <DeviceSim probe={reservationData} />
              <ScaleDivider />
              <DeviceActions
                channel={stfDevice.channel}
                deviceVersion={stfDevice.version}
                deviceManufacturer={stfDevice.manufacturer}
              />
              <ScaleDivider />
              <AudioRecording probeName={reservationData.probe_name} />
            </Tab>
            <Tab label="Attenuator">
              {/* <Info info={deviceDisplay?.info} /> */}
            </Tab>
          </Tabs>
        </Panel>
      </PanelGroup>
    </div>
  );
};
export default Android;
