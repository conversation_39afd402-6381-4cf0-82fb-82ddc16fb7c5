import {
  useSendCommand,
  useSendSwipe,
} from "@tessa-portal/hooks/services/iPhone";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import {
  ScaleIconContentApplications,
  ScaleIconHomeHome,
} from "@telekom/scale-components-react";
import { IphoneDeviceStatus } from "@tessa-portal/types/iphone";

type DeviceButtonsData = {
  probeName: string;
  probeStatus: IphoneDeviceStatus;
};

export const DeviceButtons = ({
  probeName,
  probeStatus,
}: DeviceButtonsData) => {
  const { mutateAsync: sendCommand } = useSendCommand();
  const { mutateAsync: sendSwipe } = useSendSwipe();

  return (
    <div className="tw-flex tw-flex-row tw-justify-center tw-gap-2 tw-bg-[#e20074]">
      <StyledScaleButton
        className="tw-w-[25%]"
        key="home"
        onMouseDown={() => sendCommand({ probeName, command: "homescreen" })}
      >
        <ScaleIconHomeHome className="tw-m-auto tw-flex" />
      </StyledScaleButton>
      <StyledScaleButton
        className="tw-w-[25%]"
        key="app-switch"
        onMouseDown={() =>
          sendSwipe({
            probeName,
            sessionId: probeStatus?.sessionId,
            coordsData: {
              duration: 0.1,
              fromX: 200,
              fromY: 840,
              toX: 200,
              toY: 410,
            },
          })
        }
      >
        <ScaleIconContentApplications className="tw-m-auto tw-flex" />
      </StyledScaleButton>
    </div>
  );
};
