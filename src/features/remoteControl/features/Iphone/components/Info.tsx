import { Probe, Device } from "@tessa-portal/types/services/probe";
import { DeviceSim } from "../../../components/DeviceSim/DeviceSim";
import { Buttons } from "./Buttons";

type InfoProps = {
  device: Device | undefined;
  deadline: number | null;
  onTimeOut?: () => void;
  probe: Probe;
  getStatus: () => void;
  deviceStatus: string;
  showSimAlert: boolean;
};

export const Info = ({
  deadline,
  onTimeOut,
  probe,
  getStatus,
  deviceStatus,
}: InfoProps) => (
  <div style={{ minWidth: "300px" }}>
    <Buttons deadline={deadline} onTimeOut={onTimeOut} probe={probe} />
    <DeviceSim
      disabled={deviceStatus === "unavailable"}
      probe={probe}
      onSimChange={getStatus}
    />
  </div>
);
