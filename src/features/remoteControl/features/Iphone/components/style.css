/* .sim-icon-enabled {
  filter: none;
}

.sim-icon-disabled {
  filter: invert(10%) sepia(10%) saturate(0%) hue-rotate(293deg) brightness(90%)
    contrast(95%);
}

.icon-button {
  display: block;
  background-color: transparent;
  margin: auto;
  padding: 0.2rem 0.8rem;

  &:hover:enabled {
    color: rgb(226, 0, 116);

    ~ span {
      color: rgb(226, 0, 116);
    }
  }
}


.spinner-text {
  position: relative;
  bottom: 48.5%;
  margin: auto;
}

