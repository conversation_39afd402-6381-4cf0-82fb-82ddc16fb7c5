import React, { useState } from "react";
import {
  useSendTap,
  useSendSwipe,
  useSendKey,
} from "@tessa-portal/hooks/services/iPhone";
import { DeviceButtons } from "./DeviceButtons";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";
import clsx from "clsx";
import { Probe } from "@tessa-portal/types/services/probe";
import { IphoneDeviceStatus } from "@tessa-portal/types/iphone";

type ScreenData = {
  probe: Probe;
  sessionId: string | undefined;
  probeStatus: IphoneDeviceStatus;
  getStatus: () => void;
  deviceStatus: string;
};

type Point = {
  x: number;
  y: number;
};

export const Screen = ({
  probe,
  probeStatus,
  sessionId,
  getStatus,
  deviceStatus,
}: ScreenData) => {
  const { VITE_IOS } = import.meta.env;
  const [swiping, setSwiping] = useState(false);
  const [clickStartPosition, setClickStartPosition] = useState<
    Point | undefined
  >(undefined);
  const screenInfo = { width: 400, height: 820 };
  const { mutateAsync: sendTap } = useSendTap();
  const { mutateAsync: sendSwipe } = useSendSwipe();
  const { mutateAsync: sendKey } = useSendKey();

  const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    setSwiping(true);
    setClickStartPosition({
      x: e.nativeEvent.offsetX,
      y: e.nativeEvent.offsetY,
    });
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLImageElement>) => {
    if (swiping) {
      const x = e.nativeEvent.offsetX;
      const y = e.nativeEvent.offsetY;
      setSwiping(false);
      if (clickStartPosition?.x === x && clickStartPosition?.y === y) {
        sendTap({
          probeName: probe.probe_name,
          sessionId: probeStatus.sessionId,
          coordsData: { x, y },
        });
      } else {
        sendSwipe({
          probeName: probe.probe_name,
          sessionId: probeStatus.sessionId,
          coordsData: {
            duration: 0.1,
            fromX: clickStartPosition?.x,
            fromY: clickStartPosition?.y,
            toX: x,
            toY: y,
          },
        });
      }
    }
  };

  const handleMouseOut = (e: React.MouseEvent<HTMLImageElement>) => {
    if (swiping) {
      const x = e.nativeEvent.offsetX;
      const y = e.nativeEvent.offsetY;
      setSwiping(false);
      sendSwipe({
        probeName: probe?.probe_name,
        sessionId: probeStatus?.sessionId,
        coordsData: {
          duration: 0.1,
          fromX: clickStartPosition?.x,
          fromY: clickStartPosition?.y,
          toX: x,
          toY: y,
        },
      }).catch((event) => {
        console.log(event?.response?.data);
        getStatus();
      });
    }
  };

  const handleBlurr = () => {
    if (swiping) setSwiping(false);
  };

  const handleKeyDown = (key) => {
    switch (key) {
      case "Enter":
      case "Shift":
      case "CapsLock":
      case "Backspace":
      case "Delete":
        break;
      default:
        sendKey({
          probeName: probe?.probe_name,
          sessionId: probeStatus?.sessionId,
          keyData: { value: [key], frequency: 10 },
        });
        break;
    }
  };

  return (
    <>
      {probe && (
        <div className="tw-flex tw-flex-col">
          <img
            className={clsx(
              "tw-cursor-pointer tw-select-none",
              !sessionId && "tw-opacity-50",
            )}
            // probeStatus?.sessionId is here just to refresh the image when new session Id
            // is fetched, can be also done with Date.now()
            src={`${VITE_IOS}/d/${probe?.probe_alias}/?${sessionId || "screen"}`}
            alt=""
            tabIndex={0}
            width={`${screenInfo.width}px`}
            height={`${screenInfo.height}px`}
            draggable={false}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onKeyDown={(e) => handleKeyDown(e.key)}
            onKeyUp={() => null}
            onMouseOut={handleMouseOut}
            onBlur={handleBlurr}
          />
          <DeviceButtons
            probeName={probe?.probe_name}
            probeStatus={probeStatus}
          />
          {!sessionId && (
            <>
              <LoadingSpinner
                className="tw-relative tw-bottom-[50%] tw-m-auto tw-h-[2rem] tw-w-[2rem] tw-scale-150"
                message={`${deviceStatus === "undefined" ? "Loading" : "Restarting"} ...`}
              />
            </>
          )}
        </div>
      )}
    </>
  );
};
