import { useState, useEffect } from "react";
import { Tab, Tabs } from "@tessa-portal/components/common/Tabs";
import moment from "moment";
// import { useGetProbeAttenuatorDetails } from "@tessa-portal/hooks/services/attenuator";
import {
  useGetStatus,
  useCreateSession,
  useSendCommand,
} from "@tessa-portal/hooks/services/iPhone";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
// import Attenuator from "components/stf/Attenuator/Attenuator";

import { Info, Screen } from "./components";
import { ScaleDivider } from "@telekom/scale-components-react";
import { Reservation } from "@tessa-portal/types/services/reservations";
import { Probe } from "@tessa-portal/types/services/probe";

type IphoneProps = {
  probe: Probe;
  reservation?: Reservation;
};

const Iphone = ({ reservation, probe }: IphoneProps) => {
  // const { data: attDetails } = useGetProbeAttenuatorDetails(probe?.probe_name);
  const { mutateAsync: createSession } = useCreateSession();
  const {
    data: probeStatus,
    refetch: refetchStatus,
    error,
  } = useGetStatus(probe?.probe_name);
  const [refreshStatus, setRefreshStatus] = useState(false);
  const [sessionId, setSessionId] = useState<string | undefined>(
    probeStatus?.sessionId,
  );
  const [deviceStatus, setDeviceStatus] = useState<
    "available" | "unavailable" | "undefined"
  >("undefined");
  const [showSimAlert, setShowSimAlert] = useState(false);
  const { mutateAsync: sendCommand } = useSendCommand();

  useEffect(() => {
    if (probe?.probe_name && !probeStatus?.sessionId) {
      createSession({
        probeName: probe?.probe_name,
        sessionCaps: { capabilities: {} },
      });
    }
  }, [probe?.probe_name, probeStatus?.sessionId, createSession]);

  useEffect(() => {
    console.log("probeStatus", probeStatus);

    if (
      (probeStatus && !probeStatus?.sessionId) ||
      (!probeStatus && refreshStatus)
    ) {
      setRefreshStatus(true);
      setSessionId(undefined);
    }
  }, [probeStatus, refreshStatus]);

  useEffect(() => {
    if (error) {
      setSessionId(undefined);
    }
  }, [error]);

  useEffect(() => {
    if (!error) {
      if (refreshStatus) {
        setDeviceStatus("unavailable");
        setShowSimAlert(true);
        sendCommand({ probeName: probe?.probe_name, command: "homescreen" });
      }
      setSessionId(probeStatus?.sessionId);
    }
  }, [probeStatus, error, probe?.probe_name, refreshStatus, sendCommand]);

  useEffect(() => {
    if (refreshStatus) {
      const timer = setInterval(() => {
        refetchStatus();
      }, 5000);
      return () => {
        setRefreshStatus(false);
        setDeviceStatus(
          probeStatus && probeStatus?.sessionId ? "available" : "unavailable",
        );
        console.log(
          `setting device status ${probeStatus && probeStatus?.sessionId ? "available" : "unavailable"}`,
        );
        clearInterval(timer);
      };
    }
    return () => {};
  }, [refreshStatus, probeStatus?.sessionId, probeStatus, refetchStatus]);

  return (
    <>
      <PanelGroup direction="horizontal">
        <Panel defaultSize={35}>
          <Screen
            probe={probe}
            sessionId={sessionId}
            probeStatus={probeStatus}
            getStatus={() => refetchStatus()}
            deviceStatus={deviceStatus}
          />
        </Panel>
        <PanelResizeHandle>
          <ScaleDivider vertical className="tw-mx-1" />
        </PanelResizeHandle>
        <Panel>
          <Tabs>
            <Tab label="Device">
              <Info
                device={probe?.devices?.[0]}
                deadline={+moment(reservation?.until)}
                probe={probe}
                getStatus={() => {
                  setRefreshStatus(true);
                  setSessionId(undefined);
                  setDeviceStatus("unavailable");
                }}
                deviceStatus={deviceStatus}
                showSimAlert={showSimAlert}
              />
            </Tab>
            <Tab label="Attenuator">
              {/* <Attenuator
                probe={probe}
                attDetails={attDetails}
                probeStatus={probeStatus}
                getStatus={() => refetchStatus()}
              /> */}
            </Tab>
          </Tabs>
        </Panel>
      </PanelGroup>
      {/* <div className="tw-flex tw-flex-row tw-gap-4">
        <Screen
          probe={probe}
          sessionId={sessionId}
          probeStatus={probeStatus}
          getStatus={() => refetchStatus()}
          deviceStatus={deviceStatus}
        />
        <div>
          <ScaleDivider vertical className="tw-mx-3" />
        </div>
        <div>
          <Tabs>
            <Tab label="Device">
              <Info
                device={probe?.devices?.[0]}
                deadline={+moment(reservation.until)}
                probe={probe}
                getStatus={() => {
                  setRefreshStatus(true);
                  setSessionId(undefined);
                  setDeviceStatus("unavailable");
                }}
                deviceStatus={deviceStatus}
                showSimAlert={showSimAlert}
              />
            </Tab>
            <Tab label="Attenuator">
              <Attenuator
                probe={probe}
                attDetails={attDetails}
                probeStatus={probeStatus}
                getStatus={() => refetchStatus()}
              />
            </Tab>
          </Tabs>
        </div>
      </div> */}
    </>
  );
};
export default Iphone;
