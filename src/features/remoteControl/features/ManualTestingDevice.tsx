import { useMemo } from "react";
import Iphone from "@tessa-portal/features/remoteControl/features/Iphone/Iphone";
import Android from "@tessa-portal/features/remoteControl/features/Android/Android";
import { useGetStfDevices } from "@tessa-portal/hooks/services/stf";
import { StfDevice } from "@tessa-portal/types/services/stf";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import useUser from "@tessa-portal/hooks/useUser";
import { DeviceReservationPanel } from "./DeviceReservationPanel/DeviceReservationPanel";

const ManualTestingDevice = ({ probeName }: { probeName: string }) => {
  const { data: probeData } = useGetProbeData();
  const { data: stfDevices } = useGetStfDevices();
  const { userId, isAdmin } = useUser();

  const reservedProbe = probeData?.find(
    (res) =>
      res.probe_name === probeName &&
      res.reservationStatus === "Reserved" &&
      (String(res.reservation?.user_id) === userId || isAdmin),
  );

  const stfDevice = useMemo(() => {
    if (!stfDevices || !reservedProbe) {
      return undefined;
    }

    return stfDevices.find(
      (d: StfDevice) => reservedProbe.device?.serial === d.serial,
    );
  }, [stfDevices, reservedProbe]);

  const renderProbe = (key?: string) => {
    // TODO show different screen for upcoming reservations?
    if (!reservedProbe) {
      return <DeviceReservationPanel probeName={probeName} />;
    }
    switch (key) {
      case "iOS":
        return (
          <Iphone
            probe={reservedProbe}
            reservation={reservedProbe.reservation}
          />
        );
      case "Android":
        return (
          <Android stfDevice={stfDevice} reservationData={reservedProbe} />
        );
      case "Analogue_Modem":
        return <div>Modem</div>;
      case "IP_Phone":
        return <div>IP Phone</div>;
      default:
        return null;
    }
  };

  return <>{probeData && renderProbe(reservedProbe?.device?.type)}</>;
};

export default ManualTestingDevice;
