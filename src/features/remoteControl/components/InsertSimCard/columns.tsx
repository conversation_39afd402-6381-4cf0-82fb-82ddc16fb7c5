import SimStatusCell from "@tessa-portal/components/common/SimStatusCell/SimStatusCell";
import { createColumnHelper } from "@tanstack/react-table";
import {
  useCommonColumns,
  CommonSubscriberColumns,
} from "@tessa-portal/features/subscribers/components";

export interface InsertSimCardTableRow extends CommonSubscriberColumns {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
  actions?: {
    render: (row: InsertSimCardTableRow) => JSX.Element;
    key: string;
  }[];
}
const columnHelper = createColumnHelper<InsertSimCardTableRow>();

export const useColumns = () => {
  const commonColumns = useCommonColumns<InsertSimCardTableRow>();
  return [
    columnHelper.accessor("status", {
      header: "Status",
      cell: (cell) => {
        return <SimStatusCell status={cell.row.original.status} />;
      },
    }),
    columnHelper.accessor("iccid", {
      header: "ICCID",
    }),
    columnHelper.accessor("msisdn", {
      header: "MSISDN",
    }),
    columnHelper.accessor("imsi", {
      header: "IMSI",
    }),
    columnHelper.accessor("sim_type", {
      header: "SIM Type",
    }),
    ...commonColumns,
  ];
};
