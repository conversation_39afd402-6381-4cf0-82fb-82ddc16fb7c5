import { useMemo, useState } from "react";
import InsertSimCardModal from "@tessa-portal/features/remoteControl/components/InsertSimCard/InsertSimCardModal";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import { useGetSubscribers } from "@tessa-portal/hooks/services/simManager/subscriber";
import { useGetMappings } from "@tessa-portal/hooks/services/simManager/mapping";
import {
  useCreateMapping,
  useDeleteMapping,
} from "@tessa-portal/hooks/services/simManager/mapping";
import { Probe } from "@tessa-portal/types/services/probe";
import SimCardIcon from "@tessa-portal/assets/sim_card.svg?react";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import { SimInfo } from "./SimInfo";
import { ScaleNotification } from "@telekom/scale-components-react";

type DeviceSimProps = {
  probe: Probe;
  disabled?: boolean;
  onSimChange?: () => void;
};

export const DeviceSim = ({ disabled, probe }: DeviceSimProps) => {
  const { data: mappings } = useGetMappings();
  const { data: subscribers } = useGetSubscribers();
  const [showInsertSimModal, setShowInsertSimModal] = useState(false);
  const { mutateAsync: createMapping } = useCreateMapping();
  const { mutateAsync: deleteMapping } = useDeleteMapping();
  const toast = useToastNotification();

  // Mapped sim card
  const mappedSim = useMemo(() => {
    if (!mappings || !probe) {
      return undefined;
    }
    const mapping = mappings.find((m) =>
      m.channel.path.includes(probe.probe_name),
    );
    if (!mapping) {
      return undefined;
    }
    return subscribers?.find((s) => s.iccid === mapping.mapped?.iccid);
  }, [mappings, subscribers, probe]);

  // Find fixed subscriber
  const fixedSubscriber = useMemo(() => {
    if (!probe) {
      return undefined;
    }
    return subscribers?.find(
      (s) => s.msisdn === probe?.devices?.[0].subscribers?.msisdn,
    );
  }, [subscribers, probe]);

  const handleCreateMapping = (iccid: string) => {
    createMapping(
      { probeName: probe.probe_name, iccid: iccid },
      {
        onSuccess: () => {
          setShowInsertSimModal(false);
        },
        onError: () => {
          toast.open({
            heading: "Error mapping sim card",
            text: "An error occurred while mapping the sim card",
            variant: "danger",
          });
        },
      },
    );
  };

  const handleDeleteMapping = () => {
    deleteMapping({ probeName: probe.probe_name });
  };

  return (
    <div>
      <h3 className="tw-pb-2 tw-text-style-ui-bold">SIM</h3>
      <div className="tw-flex tw-flex-row tw-justify-start tw-gap-2">
        <div className="tw-flex tw-flex-col tw-gap-2">
          {!mappedSim && !fixedSubscriber && (
            <ScaleNotification opened type="inline">
              <span slot="text" className="te-p-0 tw-m-0">
                <b>No SIM inserted.</b> Please insert a SIM card to attach to a
                netwrok.
              </span>
            </ScaleNotification>
          )}
          <div className="tw-flex tw-flex-row tw-gap-2">
            <SimCardIcon
              width="40"
              height="40"
              viewBox="0 0 64 64"
              className="tw-text-text-&-icon tw-my-auto"
              aria-hidden={true}
              // TODO: fix color
              // className={clsx(
              //   mappedSim
              //     ? "tw-filter-[invert(10%) sepia(10%) saturate(0%) hue-rotate(293deg) brightness(90%) contrast(95%)]"
              //     : "tw-filter-none",
              // )}
            />
            <div className="tw-my-auto">
              <StyledScaleButton
                variant="secondary"
                size="small"
                onClick={() => {
                  if (mappedSim) {
                    handleDeleteMapping();
                  } else {
                    setShowInsertSimModal(true);
                  }
                }}
                disabled={disabled}
              >
                {mappedSim ? "Remove SIM" : "Insert SIM"}
              </StyledScaleButton>
            </div>
          </div>
        </div>
        <div>
          {fixedSubscriber && (
            <SimInfo
              msisdn={fixedSubscriber.msisdn || ""}
              iccid={fixedSubscriber.iccid || ""}
              imsi={fixedSubscriber.imsi || ""}
            />
          )}
          {mappedSim && (
            <SimInfo
              msisdn={mappedSim.msisdn || ""}
              iccid={mappedSim.iccid || ""}
              imsi={mappedSim.imsi || ""}
            />
          )}
        </div>
        <div></div>
      </div>
      {showInsertSimModal && (
        <InsertSimCardModal
          onClose={() => setShowInsertSimModal(false)}
          handleInsertSimCard={handleCreateMapping}
        />
      )}
    </div>
  );
};
