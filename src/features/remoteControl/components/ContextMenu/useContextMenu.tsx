import { useState, useCallback, useEffect } from "react";

const useContextMenu = (ref) => {
  const [xPos, setXPos] = useState("0px");
  const [yPos, setYPos] = useState("0px");
  const [showMenu, setShowMenu] = useState(false);

  const handleContextMenu = useCallback(
    (e) => {
      e.preventDefault();

      setXPos(`${e.pageX}px`);
      setYPos(`${e.pageY}px`);
      setShowMenu(true);
    },
    [setXPos, setYPos],
  );

  const handleClick = useCallback(() => {
    showMenu && setShowMenu(false);
  }, [showMenu]);

  useEffect(() => {
    const node = ref.current;
    if (node) {
      node.addEventListener("contextmenu", handleContextMenu);
      document.addEventListener("click", handleClick);
      return () => {
        node.removeEventListener("contextmenu", handleContextMenu);
        document.removeEventListener("click", handleClick);
      };
    }
  }, [ref, handleClick, handleContextMenu]);

  return { xPos, yPos, showMenu };
};

export default useContextMenu;
