import { createColumnHelper } from "@tanstack/react-table";

export interface JobResourcesColumns {
  probe_name: string;
  device_name: string;
  msisdn: string;
  iccid: string;
}

const columnHelper = createColumnHelper<JobResourcesColumns>();

export const useColumns = () => {
  return [
    columnHelper.accessor("probe_name", {
      header: "Probe Name",
    }),
    columnHelper.accessor("device_name", {
      header: "Model",
    }),
    columnHelper.accessor("msisdn", {
      header: "MSISDN",
    }),
    columnHelper.accessor("iccid", {
      header: "ICCID",
    }),
  ];
};
