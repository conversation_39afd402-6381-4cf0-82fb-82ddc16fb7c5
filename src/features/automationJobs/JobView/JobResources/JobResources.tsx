import { Heading3 } from "@tessa-portal/components/common/Typography";
import { useColumns, JobResourcesColumns } from "./columns";
import { useMemo } from "react";
import Table from "@tessa-portal/components/common/Table";
import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";
import { ScaleIconDevicePhoneWithMobilePlan } from "@telekom/scale-components-react";
import { useNavigate } from "react-router-dom";
import { useGetProbes } from "@tessa-portal/hooks/services/probe";

type JobResourcesProps = {
  job: StatusData;
};

export const JobResources = ({ job }: JobResourcesProps) => {
  const tableColumns = useColumns();
  const { data: probes } = useGetProbes();
  const navigate = useNavigate();

  const tableData = useMemo(() => {
    return job?.probes.map(
      (probe) =>
        ({
          probe_name: probes?.find((p) => probe.probe_name === p.probe_alias)
            ?.probe_alias,
          device_name: probe.device_name,
          msisdn: probe.msisdn,
          iccid: probe.iccid,
        }) as JobResourcesColumns,
    );
  }, [job, probes]);

  if (!tableData) {
    return null;
  }

  return (
    <div className="tw-mb-2 tw-flex tw-flex-col tw-gap-4">
      <div className="tw-mb-2 tw-flex tw-items-center tw-justify-between tw-gap-8">
        <Heading3>Resources</Heading3>
      </div>
      <Table
        columns={tableColumns}
        data={tableData}
        showSort
        enableSorting
        showTableActions
        tableActions={{
          customActions: [
            {
              label: "View devices",
              icon: <ScaleIconDevicePhoneWithMobilePlan />,
              onClick: () => {
                navigate(`/automation/${job.uuid}/devices`);
              },
              hidden: job.status !== "working",
            },
          ],
        }}
      />
    </div>
  );
};

export default JobResources;
