import { Fragment } from "react";
import JobDetail from "./JobDetail/JobDetail";
import { useGetStatusData } from "@tessa-portal/features/automationJobs/hooks";

const JobView = ({ uuid }: { uuid: string }) => {
  const { data: statusData } = useGetStatusData();

  const selectedJob = statusData.find((res) => res.uuid === uuid);

  return (
    <Fragment>
      {selectedJob ? <JobDetail selectedJob={selectedJob} /> : null}
    </Fragment>
  );
};

export default JobView;
