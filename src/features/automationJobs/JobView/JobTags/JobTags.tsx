import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";
import { Heading3 } from "@tessa-portal/components/common/Typography";

type ReservationHeaderProps = {
  job: StatusData;
};

const JobTags = ({ job }: ReservationHeaderProps) => {
  return job.jobTags ? (
    <div className="tw-shadow-sm tw-mb-2 tw-rounded-lg">
      <div className="tw-flex tw-flex-col">
        <div className="tw-flex tw-items-center tw-justify-between tw-gap-8">
          <Heading3>Tags</Heading3>
        </div>
        {job.jobTags}
      </div>
    </div>
  ) : null;
};

export default JobTags;
