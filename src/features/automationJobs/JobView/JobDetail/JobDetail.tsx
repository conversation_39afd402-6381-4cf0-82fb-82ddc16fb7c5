import { ScaleCard } from "@telekom/scale-components-react";
import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";

import JobHeader from "../JobHeader/JobHeader";
import JobStatus from "../JobStatus/JobStatus";
import JobResources from "../JobResources/JobResources";
import JobTags from "../JobTags/JobTags";

const JobDetail = ({ selectedJob }: { selectedJob: StatusData }) => {
  return (
    <div className="tw-flex tw-flex-col tw-gap-4">
      <ScaleCard>
        <JobHeader job={selectedJob} />
        <JobStatus job={selectedJob} />
        <JobResources job={selectedJob} />
        <JobTags job={selectedJob} />
      </ScaleCard>
    </div>
  );
};

export default JobDetail;
