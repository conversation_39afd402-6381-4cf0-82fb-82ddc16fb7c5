import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";
import { Heading3 } from "@tessa-portal/components/common/Typography";
import {
  ScaleTextarea,
  ScaleButton,
  ScaleIconActionChangelog,
  ScaleIconActionCircleClose,
  ScaleIconActionRestart,
} from "@telekom/scale-components-react";
import { useKillStatus } from "@tessa-portal/hooks/services/scheduler/useKillStatus";
import { useDeleteQueuedStatus } from "@tessa-portal/hooks/services/scheduler/useDeleteQueuedStatus";
import { useRemoveQueuedStatus } from "@tessa-portal/hooks/services/scheduler/useRemoveQueuedStatus";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";

type ReservationHeaderProps = {
  job: StatusData;
};

const JobStatus = ({ job }: ReservationHeaderProps) => {
  const showConfirm = useConfirm();
  const toast = useToastNotification();
  const { mutateAsync: killStatus } = useKillStatus();
  const { mutateAsync: deleteQueuedStatus } = useDeleteQueuedStatus();
  const { mutateAsync: removeQueuedStatus } = useRemoveQueuedStatus();

  const handleKillStatus = async () => {
    try {
      await killStatus({ id: job.uuid });
      toast.open({
        heading: "Success",
        text: "Job aborted successfully",
        variant: "success",
      });
    } catch (error) {
      toast.open({
        heading: "Error",
        text: error instanceof Error ? error.message : "Failed to abort job",
        variant: "danger",
      });
    }
  };

  const handleKillStatusConfirm = () => {
    showConfirm({
      title: "Abort job",
      message: `Do you want to abort job ${job.uuid}?`,
      onConfirm: () => {
        handleKillStatus();
      },
    });
  };

  const handleDeleteQueuedStatus = async () => {
    try {
      await deleteQueuedStatus({ id: job.uuid });
      toast.open({
        heading: "Success",
        text: "Job aborted successfully",
        variant: "success",
      });
    } catch (error) {
      toast.open({
        heading: "Error",
        text: error instanceof Error ? error.message : "Failed to abort job",
        variant: "danger",
      });
    }
  };

  const handleDeleteQueuedStatusConfirm = () => {
    showConfirm({
      title: "Abort job",
      message: `Do you want to abort job ${job.uuid}?`,
      onConfirm: () => {
        handleDeleteQueuedStatus();
      },
    });
  };

  const handleRemoveQueuedStatus = async () => {
    try {
      await removeQueuedStatus({ id: job.uuid });
      toast.open({
        heading: "Success",
        text: "Job retried successfully",
        variant: "success",
      });
    } catch (error) {
      toast.open({
        heading: "Error",
        text: error instanceof Error ? error.message : "Failed to retry job",
        variant: "danger",
      });
    }
  };

  const handleRemoveQueuedStatusConfirm = () => {
    showConfirm({
      title: "Retry job",
      message: `Do you want to retry job ${job.uuid}?`,
      onConfirm: () => {
        handleRemoveQueuedStatus();
      },
    });
  };

  return (
    <div className="tw-shadow-sm tw-mb-2 tw-rounded-lg">
      <div className="tw-flex tw-flex-col">
        <div className="tw-mb-2 tw-flex tw-items-center tw-justify-between tw-gap-8">
          <Heading3>Status</Heading3>
          <div className="tw-ml-auto tw-flex tw-gap-2">
            <ScaleButton
              variant="secondary"
              size="small"
              disabled={job.type === "approved_job"}
              onClick={() => {
                handleRemoveQueuedStatusConfirm();
              }}
            >
              <ScaleIconActionRestart size={16} />
              Retry
            </ScaleButton>
            <ScaleButton
              variant="secondary"
              size="small"
              disabled={
                job.status === "completed" ||
                job.status === "failed" ||
                job.status === "killed"
              }
              onClick={() => {
                if (job.type === "pending_job") {
                  handleDeleteQueuedStatusConfirm();
                } else if (job.type === "approved_job") {
                  handleKillStatusConfirm();
                }
              }}
            >
              <ScaleIconActionCircleClose size={16} />
              Abort
            </ScaleButton>
            <a
              href={`https://grafana01.its-telekom.eu/d/_p3ZeeUZk/appium-environment-logs?orgId=1&var-env_id=${job?.uuid}`}
              target="_blank"
              rel="noreferrer"
            >
              <ScaleButton
                variant="secondary"
                size="small"
                disabled={job.type === "pending_job"}
              >
                <ScaleIconActionChangelog size={16} />
                Logs
              </ScaleButton>
            </a>
          </div>
        </div>
        <ScaleTextarea
          className="tw-mb-2"
          rows={5}
          resize="vertical"
          value={job.status_description}
        ></ScaleTextarea>
      </div>
    </div>
  );
};

export default JobStatus;
