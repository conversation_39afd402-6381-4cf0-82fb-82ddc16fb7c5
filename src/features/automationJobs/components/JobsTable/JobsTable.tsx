import { useState, useMemo } from "react";
import Table from "@tessa-portal/components/common/Table";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { useColumns, AutomationJobTableRow } from "./columns";
import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";
import { useGetUsers } from "@tessa-portal/hooks/services/user";
import useUser from "@tessa-portal/hooks/useUser";
import {
  ScaleIconActionCheckmark,
  ScaleIconActionAdd,
} from "@telekom/scale-components-react";
import CreateDeveloperReservationModal from "@tessa-portal/features/automationJobs/components/CreateDeveloperReservationModal";
import { CustomMenuItem } from "@tessa-portal/components/common/Table/TableSettingsMenu";

type AutomationJobsTableProps = {
  data: StatusData[];
  onRowSelectionChange: (updaterOrValue: Updater<RowSelectionState>) => void;
  rowSelection?: RowSelectionState;
  showReservationButton: boolean;
};

export const JobsTable = ({
  data,
  onRowSelectionChange,
  rowSelection,
  showReservationButton,
}: AutomationJobsTableProps) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { data: users } = useGetUsers();
  const { userId: userId } = useUser();
  const [filter, setFilter] = useState<string | null>(null);
  let tableColumns = useColumns();

  if (!showReservationButton) {
    tableColumns = tableColumns.filter(
      (column) => column.accessorKey !== "selenium_port",
    );
  }

  const tableData = useMemo(() => {
    const filteredData = filter
      ? data.filter((job) => job.type !== filter)
      : data;

    return filteredData.map((job) => {
      return {
        ...job,
        created_by: users?.find((user) => String(user.uid) === job.user_id)
          ?.username,
      };
    }) as unknown as AutomationJobTableRow[];
  }, [data, filter, users]);

  const customMenuItems: CustomMenuItem[] = [
    {
      label: (
        <span className="tw-flex tw-items-center tw-justify-between tw-gap-2">
          Approved jobs{" "}
          {(filter === null || filter === "pending_job") && (
            <ScaleIconActionCheckmark size={16} />
          )}
        </span>
      ),
      closeOnSelect: false,
      onClick: () =>
        setFilter(filter === "approved_job" ? null : "approved_job"),
    },
    {
      label: (
        <span className="tw-flex tw-items-center tw-justify-between tw-gap-2">
          Pending jobs{" "}
          {(filter === null || filter === "approved_job") && (
            <ScaleIconActionCheckmark size={16} />
          )}
        </span>
      ),
      closeOnSelect: false,
      onClick: () => setFilter(filter === "pending_job" ? null : "pending_job"),
    },
  ];

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        getRowId={(row) => row.uuid}
        selectableRows={true}
        enableMultiRowSelection={false}
        showCheckbox={false}
        showTableActions={true}
        tableActions={{
          customActions: [
            {
              label: "Reservation",
              icon: <ScaleIconActionAdd />,
              onClick: () => setShowCreateModal(true),
              hidden: !showReservationButton,
            },
          ],
        }}
        onRowSelectionChange={onRowSelectionChange}
        rowSelection={rowSelection}
        enableSorting
        initialState={{
          sorting: [{ id: "created_by", desc: false }],
        }}
        showSort
        showSettingsMenu
        customSettingsMenuItems={customMenuItems}
      />
      {showCreateModal && (
        <CreateDeveloperReservationModal
          onClose={() => setShowCreateModal(false)}
          userId={userId ?? ""}
        />
      )}
    </>
  );
};

export default JobsTable;
