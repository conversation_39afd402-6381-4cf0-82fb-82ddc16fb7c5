import { createColumnHelper } from "@tanstack/react-table";
import {
  ScaleProgressBar,
  ScaleLink,
  ScaleIconNavigationExternalLink,
  ScaleIconActionCopyPaste,
  ScaleTooltip,
} from "@telekom/scale-components-react";
import moment from "moment";
import { Status } from "@tessa-portal/types/services/scheduler";

export interface AutomationJobTableRow extends Status {
  start: string;
  execution_progress: number;
  created_by: string;
  report: string;
  selenium_port: string;
}

const columnHelper = createColumnHelper<AutomationJobTableRow>();

export const useColumns = () => {
  return [
    columnHelper.accessor("status", {
      header: "Job Status",
      cell: (cell) => {
        return !cell.row.original?.status
          ? "queued"
          : cell.row.original?.status;
      },
    }),
    columnHelper.accessor("time", {
      header: "Started at",
      cell: (cell) => {
        const start = cell.row.original?.start;
        return start ? (
          <ScaleTooltip content={cell.row.original?.start}>
            <span>{moment(cell.row.original?.start).fromNow()}</span>
          </ScaleTooltip>
        ) : (
          <span style={{ color: "#888" }}>Not available</span>
        );
      },
    }),
    columnHelper.accessor("execution_progress", {
      header: "Execution progress",
      cell: (cell) => {
        return (
          <div className="tw-flex tw-gap-4">
            <ScaleProgressBar
              percentage={cell.row.original?.execution_progress}
              label=""
              status-description=""
              show-status="true"
              style={{ width: "75%" }}
            ></ScaleProgressBar>
          </div>
        );
      },
    }),
    columnHelper.accessor("created_by", {
      header: "Created By",
    }),
    columnHelper.accessor("report", {
      header: "Report",
      cell: (cell) => {
        return cell.row.original?.status ? (
          <div className="tw-flex tw-gap-4">
            <ScaleLink target="_blank" href={cell.row.original?.report}>
              Details
              <ScaleIconNavigationExternalLink
                size={16}
                accessibility-title="open link in new tab"
                slot="icon"
              ></ScaleIconNavigationExternalLink>
            </ScaleLink>
          </div>
        ) : null;
      },
    }),
    columnHelper.accessor("selenium_port", {
      header: "Selenium port",
      cell: (cell) => {
        return cell.row.original?.selenium_port ? (
          <div className="tw-flex tw-gap-4">
            <span>{cell.row.original?.selenium_port}</span>
            <ScaleTooltip content="Copy grid port to clipboard">
              <ScaleIconActionCopyPaste
                size={16}
                onClick={() =>
                  navigator.clipboard.writeText(
                    cell.row.original?.selenium_port,
                  )
                }
              ></ScaleIconActionCopyPaste>
            </ScaleTooltip>
          </div>
        ) : null;
      },
    }),
    columnHelper.accessor("uuid", {
      header: "Job ID",
    }),
  ];
};
