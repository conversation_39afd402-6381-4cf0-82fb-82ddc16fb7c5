import { ScaleTooltip } from "@telekom/scale-components-react";
import { Reservation } from "@tessa-portal/types/services/reservations";
import { useGetUsers } from "@tessa-portal/hooks/services/user";
import { dateTimeFormat } from "@tessa-portal/helpers/moment/timeFormat";
import moment from "moment";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";

interface ReservationStatusCellProps {
  reservation?: Reservation;
}

interface ReservationTooltipContentProps {
  reservation: Reservation;
  userEmail?: string;
  userName?: string;
}

const ReservationTooltipContent = ({
  reservation,
  userEmail,
  userName,
}: ReservationTooltipContentProps) => (
  <span>
    <span>From: {moment(reservation.from).format(dateTimeFormat)}</span>
    <br />
    <span>Until: {moment(reservation.until).format(dateTimeFormat)}</span>
    <br />
    <span>
      By:
      {userEmail ? (
        <a href={`mailto:${userEmail}`}> {userName}</a>
      ) : (
        <span> {userName || "Unknown User"}</span>
      )}
    </span>
  </span>
);

export const ReservationStatusCell = ({
  reservation,
}: ReservationStatusCellProps) => {
  const { data: users } = useGetUsers();

  if (!reservation) {
    return (
      <StyledScaleTag type="standard" color="teal">
        Available
      </StyledScaleTag>
    );
  }

  const user = users?.find((user) => user.uid === reservation.user_id);

  return (
    <ScaleTooltip>
      <span slot="content">
        <ReservationTooltipContent
          reservation={reservation}
          userEmail={user?.mail}
          userName={user?.cn}
        />
      </span>
      <StyledScaleTag type="standard" color="orange">
        Reserved
      </StyledScaleTag>
    </ScaleTooltip>
  );
};

export default ReservationStatusCell;
