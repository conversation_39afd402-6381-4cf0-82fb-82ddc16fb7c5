import Table from "@tessa-portal/components/common/Table";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { useColumns, ProbeDataExtended } from "./columns";

type ProbesTableProps = {
  data: ProbeDataExtended[];
  onRowSelectionChange: (updaterOrValue: Updater<RowSelectionState>) => void;
  showCheckbox?: boolean;
  rowSelection?: RowSelectionState;
};

const ProbesTable = ({
  onRowSelectionChange,
  showCheckbox = false,
  rowSelection,
  data,
}: ProbesTableProps) => {
  const tableColumns = useColumns();

  return (
    <>
      <Table
        columns={tableColumns}
        data={data}
        getRowId={(row) => row.probe_alias}
        selectableRows={true}
        showCheckbox={showCheckbox}
        enableMultiRowSelection={true}
        showTableActions
        onRowSelectionChange={onRowSelectionChange}
        rowSelection={rowSelection}
        enableSorting
        initialState={{
          sorting: [{ id: "user", desc: false }],
        }}
        showSort
      />
    </>
  );
};

export default ProbesTable;
