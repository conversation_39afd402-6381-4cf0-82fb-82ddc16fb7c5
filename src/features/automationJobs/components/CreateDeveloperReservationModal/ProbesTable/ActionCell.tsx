import { useState } from "react";
import InsertSimCardModal from "@tessa-portal/features/automationJobs/components/InsertSimCardModal/InsertSimCardModal";
import { ScaleButton } from "@telekom/scale-components-react";
import { useFormContext } from "react-hook-form";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { CreateDeveloperReservationFormValues } from "../CreateDeveloperReservationModal";
import { ProbeFilters } from "@tessa-portal/types/services/scheduler";

interface ActionCellProps {
  probeAlias: string;
  disabled?: boolean;
}

export const ActionCell = ({ probeAlias, disabled }: ActionCellProps) => {
  const showConfirm = useConfirm();
  const [showInsertSimModal, setShowInsertSimModal] = useState(false);
  const { setValue, getValues } =
    useFormContext<CreateDeveloperReservationFormValues>();

  const selectedSim = () => {
    const match = getValues("filters").find(
      (filter) =>
        filter.probe_alias === probeAlias &&
        filter.desired_mapping !== undefined,
    );
    return match?.desired_mapping?.iccid;
  };

  const removeSim = () => {
    showConfirm({
      title: "Remove selected SIM",
      message: "Are you sure to remove the selected SIM?",
      confirmButtonText: "Remove",
      onConfirm: () => {
        const currentFilters = getValues("filters");

        const updatedFilters = currentFilters.map((probe) => {
          if (
            probe.probe_alias === probeAlias &&
            probe.desired_mapping?.iccid
          ) {
            const rest = { ...probe };
            delete rest.desired_mapping;
            return rest;
          }
          return probe;
        });

        setValue("filters", updatedFilters, { shouldDirty: true });
      },
    });
  };

  const handleSelectSimCard = (iccid: string) => {
    const currentFilters = getValues("filters");

    const filteredProbes: ProbeFilters[] = currentFilters.map((probe) => {
      const baseProbe: ProbeFilters = {
        probe_alias: probe.probe_alias,
      };

      if (probe.probe_alias === probeAlias) {
        baseProbe.desired_mapping = { iccid: iccid };
      } else if (probe.desired_mapping) {
        baseProbe.desired_mapping = probe.desired_mapping;
      }

      return baseProbe;
    });

    setValue("filters", filteredProbes, { shouldDirty: true });
    setShowInsertSimModal(false);
  };

  return (
    <div>
      <div className="tw-my-auto">
        <ScaleButton
          disabled={disabled}
          size="small"
          type="button"
          onClick={(event) => {
            event.preventDefault();
            event.stopPropagation();
            selectedSim() ? removeSim() : setShowInsertSimModal(true);
          }}
        >
          {selectedSim() ? "Remove SIM" : "Select SIM"}
        </ScaleButton>
      </div>
      {showInsertSimModal && (
        <div
          onClick={(e) => e.stopPropagation()}
          onKeyDown={(e) => e.stopPropagation()}
        >
          <InsertSimCardModal
            onClose={() => setShowInsertSimModal(false)}
            handleSelectSimCard={handleSelectSimCard}
          />
        </div>
      )}
    </div>
  );
};

export default ActionCell;
