import { ProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import { ReservationStatusCell } from "./ReservationStatusCell";
import { ActionCell } from "./ActionCell";
import { SimInfoCell } from "./SimInfoCell";
import { createColumnHelper } from "@tanstack/react-table";
import StatusCell, { ProbeStatus, DeviceStatus, StfStatus } from "./StatusCell";
import { Subscriber } from "@tessa-portal/types/services//simManager";
import useUser from "@tessa-portal/hooks/useUser";
import { useWatch } from "react-hook-form";
import { useGetSubscribers } from "@tessa-portal/hooks/services/simManager/subscriber";

export interface ProbeDataExtended extends ProbeData {
  action?: string;
  sim_info?: string;
  network?: string;
  mappedSim?: Subscriber;
}

const columnHelper = createColumnHelper<ProbeDataExtended>();

export const useColumns = () => {
  const { name: currentUserName } = useUser();
  const { data: subscribers } = useGetSubscribers();
  const filterProbes = useWatch({ name: "filters" });

  return [
    columnHelper.accessor("reservation", {
      header: "Status",
      cell: ({ row }) => (
        <ReservationStatusCell reservation={row.original.reservation} />
      ),
    }),

    columnHelper.accessor("location", {
      header: "Location",
    }),

    columnHelper.accessor("probe_alias", {
      header: "Probe name",
    }),

    columnHelper.accessor("device", {
      header: "Model",
      cell: ({ row }) => row.original?.device?.name,
    }),

    columnHelper.accessor("status", {
      header: "Probe status",
      cell: ({ row }) => (
        <StatusCell
          probeStatus={row.original.status as ProbeStatus}
          deviceStatus={row.original.device?.status as DeviceStatus}
          stfStatus={row.original.device?.stf_status as StfStatus}
        />
      ),
    }),

    columnHelper.accessor("action", {
      header: "Action",
      cell: ({ row }) =>
        (row.original.devices?.[0]?.type === "Android" ||
          row.original.devices?.[0]?.type === "iOS") &&
        !row.original.devices?.[0]?.subscribers ? (
          <ActionCell
            probeAlias={row.original.probe_alias}
            disabled={!row.getIsSelected()}
          />
        ) : null,
    }),

    columnHelper.accessor("sim_info", {
      header: "SIM Info",
      cell: ({ row }) => {
        const selectedSim = filterProbes.find(
          (filter) => filter.probe_alias === row.original.probe_alias,
        )?.desired_mapping?.iccid;
        const subscriber = subscribers?.find(
          (subscriber) => subscriber.iccid === selectedSim,
        );
        return subscriber ? <SimInfoCell mappedSim={subscriber} /> : null;
      },
    }),

    columnHelper.accessor("network", {
      header: "Network",
      cell: ({ row }) => row.original?.mappedSim?.network,
    }),

    columnHelper.accessor("user", {
      header: "Reserved by",
      cell: ({ row }) => row.original.user?.cn,
      sortingFn: (a, b) => {
        const aName = a.original.user?.cn || "";
        const bName = b.original.user?.cn || "";

        // If one is current user and other isn't, prioritize current user
        if (currentUserName) {
          if (aName === currentUserName && bName !== currentUserName) return -1;
          if (aName !== currentUserName && bName === currentUserName) return 1;
        }

        // If names are different, sort by name
        const nameCompare = aName.localeCompare(bName);
        if (nameCompare !== 0) return nameCompare;

        // If names are the same, sort by reservation time (latest first)
        const aTime = a.original.reservation?.from
          ? new Date(a.original.reservation.from).getTime()
          : 0;
        const bTime = b.original.reservation?.from
          ? new Date(b.original.reservation.from).getTime()
          : 0;
        return bTime - aTime;
      },
    }),
  ];
};
