import {
  ScaleIconContentSimCard,
  ScaleTooltip,
} from "@telekom/scale-components-react";
import { Subscriber } from "@tessa-portal/types/services//simManager";

interface NetworkCellProps {
  mappedSim: Subscriber;
}

export const SimInfoCell = ({ mappedSim }: NetworkCellProps) => {
  return (
    <span className="tw-flex tw-grow-0 tw-items-center tw-justify-start">
      <div className="tw-m-auto">
        <ScaleTooltip>
          <span slot="content">
            <u>SIM info</u>
            <br />
            ICCID: {mappedSim.iccid}
            <br />
            MSISDN: {mappedSim.msisdn}
            <br />
            IMSI: {mappedSim.imsi}
            <br />
            Tag: {mappedSim.tags}
          </span>
          <ScaleIconContentSimCard fill="#E20074" selected size={24} />
        </ScaleTooltip>
      </div>
    </span>
  );
};

export default SimInfoCell;
