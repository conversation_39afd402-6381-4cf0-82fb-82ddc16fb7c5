import {
  ReactNode,
  useEffect,
  useRef,
  useState,
  useCallback,
  useMemo,
} from "react";
import Modal, { ModalRef } from "@tessa-portal/components/common/Modal";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { useScheduleSleepJob } from "@tessa-portal/hooks/services/scheduler";
import {
  SubmitHandler,
  useForm,
  FormProvider,
  useFormContext,
  useWatch,
} from "react-hook-form";
import {
  ScaleHelperText,
  ScaleLoadingSpinner,
} from "@telekom/scale-components-react";
import { useGetProbeData } from "@tessa-portal/hooks/util/useGetProbeData";
import clsx from "clsx";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import ProbesTable from "./ProbesTable/ProbesTable";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { Updater, RowSelectionState } from "@tanstack/react-table";
import { ProbeFilters } from "@tessa-portal/types/services/scheduler";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";

type CreateDeveloperReservationModalProps = {
  onClose: () => void;
  userId: string;
};

export type CreateDeveloperReservationFormValues = {
  filters: ProbeFilters[];
  reservation_duration: number;
};

const CreateDeveloperReservationModal = ({
  onClose: handleClose,
  userId,
}: CreateDeveloperReservationModalProps) => {
  const {
    mutateAsync: scheduleSleepJob,
    error,
    isError,
  } = useScheduleSleepJob();
  const { data: probeData, isLoading } = useGetProbeData();

  const toast = useToastNotification();

  const defaultValues: CreateDeveloperReservationFormValues = {
    filters: [],
    reservation_duration: 60,
  };

  const form = useForm<CreateDeveloperReservationFormValues>({
    mode: "all",
    defaultValues,
  });

  const onSubmit: SubmitHandler<CreateDeveloperReservationFormValues> = async (
    data,
  ) => {
    scheduleSleepJob(
      {
        ...data,
        user_id: userId,
      },
      {
        onSuccess: () => {
          handleClose();
          toast.open({
            heading: "Developer reservation successfull",
            text: "The developer reservation was done successfully.",
            variant: "success",
          });
        },
        onError: () => {
          toast.open({
            heading: "Developer reservation failed",
            text: "Unable to create developer reservation",
            variant: "danger",
          });
        },
      },
    );
  };

  const handleRowSelection = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const newState =
        typeof updaterOrValue === "function"
          ? updaterOrValue({})
          : updaterOrValue;

      const currentFilters = form.getValues("filters");

      const selectedProbeAliases = new Set(Object.keys(newState));

      const mergedProbes = currentFilters.filter((probe) =>
        selectedProbeAliases.has(probe.probe_alias),
      );

      Object.keys(newState).forEach((probe_alias) => {
        if (!mergedProbes.find((p) => p.probe_alias === probe_alias)) {
          mergedProbes.push({ probe_alias });
        }
      });

      form.setValue("filters", mergedProbes, { shouldDirty: true });
    },
    [form],
  );

  const filters = useWatch({
    control: form.control,
    name: "filters",
    defaultValue: [],
  });

  const hasAllMappings = useMemo(() => {
    return (
      filters?.length > 0 &&
      filters.every((filter) => filter.probe_alias && filter.desired_mapping)
    );
  }, [filters]);

  if (isLoading)
    return (
      <div className="tw-flex tw-flex-grow tw-justify-center">
        <ScaleLoadingSpinner />
      </div>
    );

  return (
    <FormProvider {...form}>
      <FormModal onClose={handleClose}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="tw-flex tw-flex-col tw-gap-2 tw-pb-4">
            <span>Set the length of the new reservation.</span>
            <TextField
              className="tw-w-1/4"
              label="Reservation time in minutes"
              name="reservation_duration"
              step="1"
              type="number"
            />
          </div>
          Select the probe(s) to be reserved and select SIM card(s) to be
          inserted after reservation.
          <ProbesTable
            data={probeData}
            showCheckbox={true}
            onRowSelectionChange={handleRowSelection}
          />
          <div className={clsx(!isError && "tw-invisible")}>
            <ScaleHelperText variant="danger">{error?.message}</ScaleHelperText>
          </div>
          <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4">
            <Modal.CloseButton type="button" variant="secondary">
              Cancel
            </Modal.CloseButton>
            <StyledScaleButton
              variant="primary"
              type="submit"
              disabled={!hasAllMappings}
            >
              Reserve
            </StyledScaleButton>
          </div>
        </form>
      </FormModal>
    </FormProvider>
  );
};

export default CreateDeveloperReservationModal;

type FormModalProps = {
  children: ReactNode;
  onClose: () => void;
};

const FormModal = ({ children, onClose: handleClose }: FormModalProps) => {
  const modalRef = useRef<ModalRef>(null);

  const [confirmed, setConfirmed] = useState(false);

  const {
    formState: { isDirty },
  } = useFormContext();

  useEffect(() => {
    if (confirmed && modalRef.current) {
      modalRef.current?.closeModal();
    }
  }, [confirmed]);

  const showConfirm = useConfirm();

  const handleBeforeClose = (e: Event) => {
    if (isDirty && !confirmed) {
      e.preventDefault();

      showConfirm({
        title: "Close Reservation",
        message: "Are you sure you want to close the reservation?",
        onConfirm: () => {
          setConfirmed(true);
        },
      });
    }
  };

  return (
    <Modal
      disablePortal
      heading="New developer reservation"
      onScale-before-close={handleBeforeClose}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.75 * var(--scl-grid-max-width))));}"
      }
      ref={modalRef}
    >
      {children}
    </Modal>
  );
};
