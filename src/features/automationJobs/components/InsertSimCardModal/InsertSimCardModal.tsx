import Modal from "@tessa-portal/components/common/Modal";
import InsertSimCardTable from "./InsertSimCardTable";
import { useGetSubscriberData } from "@tessa-portal/features/subscribers/hooks";
import { useMemo } from "react";

type InsertSimCardModalProps = {
  onClose: () => void;
  handleSelectSimCard: (iccid: string) => void;
};

const InsertSimCardModal = ({
  onClose: handleClose,
  handleSelectSimCard,
}: InsertSimCardModalProps) => {
  const { data: subscriberData } = useGetSubscriberData();

  const tableData = useMemo(() => {
    if (!subscriberData) return [];
    return subscriberData?.filter(
      (subscriber) =>
        subscriber.iccid &&
        subscriber.type === "mappable" &&
        subscriber.position,
    );
  }, [subscriberData]);

  return (
    <Modal
      disablePortal
      heading="Insert SIM card"
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.75 * var(--scl-grid-max-width))));}"
      }
    >
      <InsertSimCardTable
        data={tableData}
        onRowSelected={handleSelectSimCard}
      />
      <div className="tw-flex tw-flex-none tw-justify-end tw-pt-4">
        <Modal.CloseButton type="button" variant="secondary">
          Cancel
        </Modal.CloseButton>
      </div>
    </Modal>
  );
};

export default InsertSimCardModal;
