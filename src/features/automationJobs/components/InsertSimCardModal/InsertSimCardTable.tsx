import { useCallback } from "react";
import { Updater, RowSelectionState } from "@tanstack/react-table";
import Table from "@tessa-portal/components/common/Table";
import { useColumns } from "./columns";
import { SubscriberData } from "@tessa-portal/features/subscribers/hooks";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";

const InsertSimCardTable = ({
  data,
  onRowSelected,
}: {
  data: SubscriberData[];
  onRowSelected: (iccid: string) => void;
}) => {
  const tableColumns = useColumns();
  const userTableSettings = useUserTableSettings("insert-sim-cards");

  const handleRowSelectionChange = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const value =
        typeof updaterOrValue === "function"
          ? updaterOrValue({})
          : updaterOrValue;
      const selectedRows = Object.keys(value);
      const selectedRow = selectedRows[0];

      onRowSelected(selectedRow);
    },
    [onRowSelected],
  );

  return (
    <>
      <Table
        columns={tableColumns}
        data={data}
        getRowId={(row) => String(row.iccid)}
        onRowSelectionChange={handleRowSelectionChange}
        selectableRows={true}
        showSort
        enableSorting
        showSettingsMenu
        settingsMenuItems={["columns"]}
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
      />
    </>
  );
};

export default InsertSimCardTable;
