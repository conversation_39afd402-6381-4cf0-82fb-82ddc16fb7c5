import { useMemo } from "react";
import { useGetStatuses } from "@tessa-portal/hooks/services/scheduler/useGetStatuses";
import { useGetQueuedStatuses } from "@tessa-portal/hooks/services/scheduler/useGetQueuedStatuses";
import { useGetProbes } from "@tessa-portal/hooks/services/probe";
import { useGetSubscribers } from "@tessa-portal/hooks/services/simManager/subscriber";
import {
  Status,
  QueuedStatusJob,
} from "@tessa-portal/types/services/scheduler";

export interface StatusData extends Status {
  user: string;
  start: string;
  type: string;
  user_id: string;
  probes: Array<{
    probe_name: string;
    device_name: string;
    msisdn: string;
    iccid: string;
    identifier: string;
    probe_type: string;
  }>;
  jobTags: string;
  status_description: string;
  developer_job: boolean;
}

export const useGetStatusData = () => {
  const { data: probesData, isLoading: isLoadingProbes } = useGetProbes();
  const { data: subscribers, isLoading: isLoadingSubscribers } =
    useGetSubscribers();
  const { data: Statuses, isLoading: isLoadingStatuses } = useGetStatuses();
  const { data: QueuedStatuses, isLoading: isLoadingQueuedStatuses } =
    useGetQueuedStatuses();

  const statusData = useMemo(() => {
    if (!Statuses || !QueuedStatuses) return [];

    const combinedStatuses = [
      ...(QueuedStatuses.jobs || []),
      ...(Statuses.statuses || []),
    ];

    const result = combinedStatuses.map((job) => {
      const isQueued = "approval_id" in job;
      const isApproved = "time" in job;

      let executionProgress;
      if (isApproved) {
        const statusJob = job as Status;
        if (statusJob.status === "completed") {
          executionProgress = 100;
        } else if (statusJob.status === "working") {
          executionProgress = Math.round(
            (statusJob.num / statusJob.total) * 100,
          );
        } else {
          executionProgress = "";
        }
      } else {
        executionProgress = "";
      }

      const getDeviceNameByProbeAlias = (alias: string) => {
        if (isLoadingProbes || !probesData) {
          return null;
        }
        const probe = probesData.find((p) => p.probe_alias === alias);
        return probe?.devices?.[0]?.name ?? null;
      };

      const getMsisdnByIccid = (iccid: string) => {
        if (isLoadingSubscribers || !subscribers) {
          return null;
        }
        const subscriber = subscribers.find((p) => p.iccid === iccid);
        return subscriber?.msisdn ?? null;
      };

      const probes = isApproved
        ? (job as Status).options.probes.map((probe) => ({
            probe_name: probe.probe_alias,
            device_name: probe.device,
            msisdn: probe.msisdn,
            iccid: probe.filter_used?.desired_mapping?.iccid || "",
            identifier: probe.identifier,
            probe_type: probe.device_type,
          }))
        : (job as QueuedStatusJob).arguments[0].filters.map((filter) => ({
            probe_name: filter.probe_alias,
            device_name: getDeviceNameByProbeAlias(filter.probe_alias),
            msisdn: getMsisdnByIccid(filter?.desired_mapping?.iccid),
            iccid: filter?.desired_mapping?.iccid || "",
            identifier: "",
            probe_type: "",
          }));

      const regex = /@\d{3}_\d{3}/g;
      let jobTags;
      if (isApproved) {
        const statusJob = job as Status;
        const matchedWords = statusJob.options.cucumber_args?.match(regex);
        jobTags = matchedWords ? matchedWords.join(" , ") : "";
      } else {
        jobTags = "";
      }

      let statusDescription = "";
      let jailedAtFirst;
      if (isApproved) {
        const statusJob = job as Status;
        if (statusJob.status === "working") {
          statusDescription = `Working (${executionProgress}%)`;
        } else if (
          ["completed", "killed", "failed"].includes(statusJob.status)
        ) {
          statusDescription = statusJob.message || "";
        }
      } else {
        const queuedJob = job as QueuedStatusJob;
        const jailStatus = queuedJob.jail_status || {};
        const timesJailed = jailStatus.times_jailed || 0;
        jailedAtFirst = jailStatus?.jailed_at_first
          ? new Date(
              jailStatus.jailed_at_first
                .replace(" ", "T")
                .replace(" +0000", "Z"),
            )
              .toLocaleString("sv-SE")
              .replace("T", " ")
          : "";

        let reasons = "";

        if (Array.isArray(jailStatus.reason)) {
          reasons = jailStatus.reason
            .flat()
            .filter((reason) => reason)
            .map((reason) => `- ${reason}`)
            .join("\n");
        } else if (typeof jailStatus.reason === "string") {
          reasons = `- ${jailStatus.reason}`;
        }
        statusDescription = `Times jailed: ${timesJailed}\nFirst jailed at: ${jailedAtFirst}\nReasons:\n${reasons}`;
      }

      const developer_job = isApproved
        ? (job as Status).name === "SleepJob"
        : (job as QueuedStatusJob).klass === "SleepJob";

      const newStatus = {
        uuid: isApproved
          ? (job as Status).uuid
          : (job as QueuedStatusJob).approval_id,
        start: isApproved
          ? new Date((job as Status).time * 1000)
              .toLocaleString("sv-SE")
              .replace("T", " ")
              .slice(0, 19)
          : jailedAtFirst,
        type: isQueued ? "pending_job" : "approved_job",
        user_id: isQueued
          ? (job as QueuedStatusJob).arguments[0].user_id
          : (job as Status).options.user_id,
        status: isQueued ? "" : (job as Status).status,
        execution_progress: executionProgress,
        selenium_port: isApproved
          ? `:${(job as Status).options.selenium_grid_url.split(":")[1]}`
          : "",
        report: "https://reporting.its-telekom.eu",
        probes,
        jobTags,
        status_description: statusDescription,
        developer_job: developer_job,
      } as unknown as StatusData;

      return newStatus;
    });

    const pending = result.filter((job) => job.type === "pending_job");
    const approved = result.filter((job) => job.type === "approved_job");

    // Sort pending: empty times first, then by descending time
    const queued = pending.sort((a, b) => {
      if (!a.start && !b.start) return 0;
      if (!a.start) return -1;
      if (!b.start) return 1;
      return new Date(b.start).getTime() - new Date(a.start).getTime(); // Newest first
    });

    // Sort working: newest to oldest
    const working = approved
      .filter((job) => job.status === "working")
      .sort(
        (a, b) => new Date(b.start).getTime() - new Date(a.start).getTime(),
      );

    // Sort others: newest to oldest
    const others = approved
      .filter(
        (job) =>
          job.status === "completed" ||
          job.status === "killed" ||
          job.status === "failed",
      )
      .sort(
        (a, b) => new Date(b.start).getTime() - new Date(a.start).getTime(),
      );

    return [...queued, ...working, ...others];
  }, [
    QueuedStatuses,
    Statuses,
    isLoadingProbes,
    isLoadingSubscribers,
    probesData,
    subscribers,
  ]);

  return {
    data: statusData,
    isLoading: isLoadingStatuses || isLoadingQueuedStatuses || isLoadingProbes,
  };
};
