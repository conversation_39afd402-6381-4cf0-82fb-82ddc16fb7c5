import { Tab, Tabs } from "@tessa-portal/components/common/Tabs";
import JobsTable from "@tessa-portal/features/automationJobs/components/JobsTable";
import { StatusData } from "@tessa-portal/features/automationJobs/hooks/useGetAutomationJobsData";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { useMemo } from "react";
import useUser from "@tessa-portal/hooks/useUser";

type AutomationJobsTableProps = {
  data: StatusData[];
  onRowSelectionChange: (updaterOrValue: Updater<RowSelectionState>) => void;
  rowSelection?: RowSelectionState;
  showReservationButton?: boolean;
};

export const AutomationJobsTable = ({
  data,
  onRowSelectionChange,
  rowSelection,
}: AutomationJobsTableProps) => {
  const jobsData = useMemo(() => {
    return data.filter((job) => !job.developer_job);
  }, [data]);
  const developerJobsData = useMemo(() => {
    return data.filter((job) => job.developer_job);
  }, [data]);

  const { roles, isAdmin } = useUser();

  return (
    <div>
      <Tabs>
        <Tab label="Jobs">
          <JobsTable
            data={jobsData}
            onRowSelectionChange={onRowSelectionChange}
            rowSelection={rowSelection}
            showReservationButton={false}
          />
        </Tab>
        <Tab
          label="Developer Jobs"
          hidden={!roles.includes("expert-testing") && !isAdmin}
        >
          <JobsTable
            data={developerJobsData}
            onRowSelectionChange={onRowSelectionChange}
            rowSelection={rowSelection}
            showReservationButton={true}
          />
        </Tab>
      </Tabs>
    </div>
  );
};

export default AutomationJobsTable;
