import { ReactNode, useEffect, useRef, useState } from "react";
import Modal, { ModalRef } from "@tessa-portal/components/common/Modal";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { useCreateProbeAndEditLocation } from "@tessa-portal/hooks/services/probe";
import {
  SubmitHandler,
  useForm,
  FormProvider,
  useFormContext,
} from "react-hook-form";
import {
  ScaleLoadingSpinner,
  ScaleHelperText,
} from "@telekom/scale-components-react";
import { useGetProbeLocations } from "@tessa-portal/hooks/services/probe/location/useGetProbeLocations";
import { ReservationTimeData } from "@tessa-portal/types/services/reservations";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import EditProbe from "@tessa-portal/features/administration/probes/features/components/EditProbe";
import clsx from "clsx";

type AddProbeModalProps = {
  onClose: () => void;
};

export type AddProbeFormValues = {
  probes: string[];
  reservation: ReservationTimeData;
};

const AddProbeModal = ({ onClose: handleClose }: AddProbeModalProps) => {
  const {
    mutateAsync: createProbeAndEditLocation,
    error,
    isError,
  } = useCreateProbeAndEditLocation();
  const { data: locationData, isLoading } = useGetProbeLocations();

  const defaultValues = {};

  const form = useForm<AddProbeFormValues>({
    mode: "all",
    defaultValues,
  });

  const onSubmit: SubmitHandler<AddProbeFormValues> = async (data) => {
    console.log(`data ${data}`);
    console.log(`createProbeAndEditLocation ${createProbeAndEditLocation}`);
  };

  if (isLoading)
    return (
      <div className="tw-flex tw-flex-grow tw-justify-center">
        <ScaleLoadingSpinner />
      </div>
    );

  return (
    <FormProvider {...form}>
      <FormModal onClose={handleClose}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <EditProbe locations={locationData || []} />
          <div className={clsx(!isError && "tw-invisible")}>
            <ScaleHelperText variant="danger">{error?.message}</ScaleHelperText>
          </div>
          <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4">
            <Modal.CloseButton type="button" variant="secondary">
              Cancel
            </Modal.CloseButton>
            <StyledScaleButton variant="primary" type="submit">
              Reserve
            </StyledScaleButton>
          </div>
        </form>
      </FormModal>
    </FormProvider>
  );
};

export default AddProbeModal;

type FormModalProps = {
  children: ReactNode;
  onClose: () => void;
};

const FormModal = ({ children, onClose: handleClose }: FormModalProps) => {
  const modalRef = useRef<ModalRef>(null);

  const [confirmed, setConfirmed] = useState(false);

  const {
    formState: { isDirty },
  } = useFormContext();

  useEffect(() => {
    if (confirmed && modalRef.current) {
      modalRef.current?.closeModal();
    }
  }, [confirmed]);

  const showConfirm = useConfirm();

  const handleBeforeClose = (e: Event) => {
    if (isDirty && !confirmed) {
      e.preventDefault();

      showConfirm({
        title: "Close Reservation",
        message: "Are you sure you want to close the reservation?",
        onConfirm: () => {
          setConfirmed(true);
        },
      });
    }
  };

  return (
    <Modal
      disablePortal
      heading="New reservation"
      onScale-before-close={handleBeforeClose}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.4 * var(--scl-grid-max-width))));}"
      }
      ref={modalRef}
    >
      {children}
    </Modal>
  );
};
