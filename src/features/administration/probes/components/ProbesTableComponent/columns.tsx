import { createColumnHelper } from "@tanstack/react-table";
import { Probe } from "@tessa-portal/types/services/probe";

export interface ProbeAdminColumns extends Probe {
  country_name: string;
  device_status: string;
  type: string;
  device_model: string;
}

const columnHelper = createColumnHelper<ProbeAdminColumns>();

export const useColumns = () => {
  return [
    columnHelper.accessor("country_name", {
      header: "Location",
    }),
    columnHelper.accessor("status", {
      header: "Probe status",
    }),
    columnHelper.accessor("device_status", {
      header: "Device status",
    }),
    columnHelper.accessor("probe_alias", {
      header: "Alias",
    }),
    columnHelper.accessor("type", {
      header: "Type",
    }),
    columnHelper.accessor("device_model", {
      header: "Device Model",
    }),
  ];
};
