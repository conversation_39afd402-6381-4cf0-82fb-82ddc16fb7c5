import { useMemo, useState } from "react";
import Table from "@tessa-portal/components/common/Table";
import AddProbeModal from "@tessa-portal/features/administration/probes/components/AddProbeModal";
import { useColumns } from "./columns";
import useUserTableSettings from "@tessa-portal/hooks/userSettings/useUserTableSettings";
import { useProbeHelper } from "@tessa-portal/features/administration/probes/helpers";
import { ProbeData } from "../../hooks";
import { ScaleIconActionAdd } from "@telekom/scale-components-react";

export const ProbesTableComponent = ({ data }: { data: ProbeData[] }) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const userTableSettings = useUserTableSettings("admin-probes-table");
  const tableColumns = useColumns();
  const tableData = useMemo(() => {
    return data;
  }, [data]);
  const { handleRowSelectionChange } = useProbeHelper(tableData);

  return (
    <>
      <Table
        columns={tableColumns}
        data={tableData}
        selectableRows={true}
        getRowId={(row) => String(row.probe_name)}
        showSort
        enableSorting
        showSettingsMenu
        settingsMenuItems={["columns", "export"]}
        showTableActions
        tableActions={{
          customActions: [
            {
              label: "Probe",
              icon: <ScaleIconActionAdd />,
              onClick: () => setShowAddModal(true),
            },
          ],
        }}
        initialState={userTableSettings.initialState}
        onStateChange={userTableSettings.updateTableSettings}
        onRowSelectionChange={handleRowSelectionChange}
      />
      {showAddModal && <AddProbeModal onClose={() => setShowAddModal(false)} />}
    </>
  );
};

export default ProbesTableComponent;
