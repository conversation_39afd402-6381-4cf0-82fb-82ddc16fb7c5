import { useCallback, useContext } from "react";
import { RowSelectionState, Updater } from "@tanstack/react-table";
import { ProbesContext } from "@tessa-portal/features/administration/probes/context/ProbesContext";
import { Probe } from "@tessa-portal/types/services/probe";

type FieldType = {
  label: string;
  name: string;
  rules?: Record<string, string>;
  readonly?: boolean;
  inputModeType?: "numeric" | "tel" | "text" | "none" | undefined;
  type?:
    | "number"
    | "text"
    | "hidden"
    | "email"
    | "password"
    | "tel"
    | "date"
    | "month"
    | "week"
    | "time"
    | "datetime-local"
    | "url"
    | undefined;
};

export const fields: FieldType[] = [
  {
    label: "IP address *",
    name: "IP",
    rules: { required: "IP address is required" },
    type: "text",
  },
  {
    label: "Name *",
    name: "probe_name",
    rules: { required: "Name is required" },
    type: "text",
    readonly: true,
  },
  {
    label: "<PERSON><PERSON> *",
    name: "probe_alias",
    rules: { required: "<PERSON><PERSON> is required" },
    type: "text",
  },
];

// Find subscriber and assign as selected subscriber for editing
export const useProbeHelper = (data: Probe[]) => {
  const { setSelectedProbe } = useContext(ProbesContext);
  const handleRowSelectionChange = useCallback(
    (updaterOrValue: Updater<RowSelectionState>) => {
      const value =
        typeof updaterOrValue === "function"
          ? updaterOrValue({})
          : updaterOrValue;
      const selectedRows = Object.keys(value);
      const selectedRow = selectedRows[0];

      if (selectedRow) {
        const selectedProbe = data.find(
          (probe) => probe.probe_name === selectedRow,
        );

        if (selectedProbe) {
          setSelectedProbe(selectedProbe);
        }
      } else {
        setSelectedProbe(undefined);
      }
    },
    [data, setSelectedProbe],
  );

  return { handleRowSelectionChange };
};
