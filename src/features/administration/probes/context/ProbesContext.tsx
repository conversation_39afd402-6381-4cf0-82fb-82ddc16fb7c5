import { Probe } from "@tessa-portal/types/services/probe";
import { createContext, Di<PERSON>atch, SetStateAction } from "react";

export type ProbesContext = {
  selectedProbe?: Probe;
  setSelectedProbe: Dispatch<SetStateAction<Probe | undefined>>;
  newProbe: () => void;
  showModal: boolean;
};

const probesContext: ProbesContext = {
  selectedProbe: undefined,
  setSelectedProbe: () => {
    throw new Error("setSelectedProbe function is not initialized");
  },
  newProbe: () => {
    throw new Error("newProbe function is not initialized");
  },
  showModal: false,
};

export const ProbesContext = createContext<ProbesContext>(probesContext);
