import { useState, useContext } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { Text } from "@tessa-portal/components/common/Typography";

import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import { ProbesContext } from "../context/ProbesContext";
import { Probe } from "@tessa-portal/types/services/probe";

import EditProbe from "./components/EditProbe";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";

import { useGetProbeLocations } from "@tessa-portal/hooks/services/probe/location/useGetProbeLocations";

export const ProbeEditor = () => {
  const [state, forceUpdate] = useState(false);
  const { selectedProbe } = useContext(ProbesContext);
  const { data: locationData, isLoading } = useGetProbeLocations();

  const defaultValues = selectedProbe || {};
  const form = useForm<Probe>({
    mode: "all",
    defaultValues,
  });

  const onSubmit = (data: Probe) => {
    console.log(data);
  };

  if (!selectedProbe || isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <FormProvider {...form}>
      <Text className="tw-pb-6 tw-pt-2" variant="large">
        {`Edit probe ${selectedProbe.probe_name}`}
      </Text>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <EditProbe locations={locationData || []} />
        <div className="tw-flex tw-w-full tw-justify-end tw-gap-4 tw-pt-4">
          <StyledScaleButton
            type="button"
            variant="secondary"
            // disabled={!form.formState.isDirty}
            onClick={() => {
              forceUpdate(!state);
              return form.reset(defaultValues);
            }}
            innerAriaLabel="Discard"
          >
            Discard
          </StyledScaleButton>
          <StyledScaleButton
            type="submit"
            variant="primary"
            // disabled={!form.formState.isDirty || !form.formState.isValid}
            innerAriaLabel="Save"
          >
            Save
          </StyledScaleButton>
        </div>
      </form>
    </FormProvider>
  );
};
