import {
  ScaleCheckbox,
  ScaleDropdownSelect,
  ScaleDropdownSelectItem,
} from "@telekom/scale-components-react";
import { fields } from "../../helpers";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import { useFormContext } from "react-hook-form";
import { ProbeLocation } from "@tessa-portal/types/services/probe";

export const EditProbe = ({ locations }: { locations: ProbeLocation[] }) => {
  const form = useFormContext();

  return (
    <div>
      <div className="tw-grid tw-grid-cols-2 tw-gap-4">
        <ScaleDropdownSelect label="Location" {...form.register("country_iso")}>
          {locations?.map((loc) => (
            <ScaleDropdownSelectItem value={loc.country_iso}>
              {loc.country_name}
            </ScaleDropdownSelectItem>
          ))}
        </ScaleDropdownSelect>
        {fields.map((field) => {
          switch (field.type) {
            case "number":
            case "text": {
              return (
                <TextField
                  key={field.name}
                  rules={field.rules}
                  type={field.type}
                  {...field}
                />
              );
            }
          }
        })}
        <ScaleCheckbox
          {...form.register("VPN")}
          label={"VPN connected"}
          ariaLabelCheckbox={"vpn"}
          checked={!!form.watch("VPN")}
        />
        <ScaleCheckbox
          {...form.register("poe")}
          label={"PoE connected"}
          ariaLabelCheckbox={"poe"}
          checked={!!form.watch("poe")}
        />
      </div>
    </div>
  );
};

export default EditProbe;
