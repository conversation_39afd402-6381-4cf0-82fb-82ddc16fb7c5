import { ProbesTableComponent } from "@tessa-portal/features/administration/probes/components/ProbesTableComponent";
import { useGetProbesData } from "@tessa-portal/features/administration/probes/hooks";
import LoadingSpinner from "@tessa-portal/components/common/LoadingSpinner";

export const ProbesTable = () => {
  const { data: probeData, isLoading } = useGetProbesData();

  if (isLoading) return <LoadingSpinner />;

  return <ProbesTableComponent data={probeData} />;
};

export default ProbesTable;
