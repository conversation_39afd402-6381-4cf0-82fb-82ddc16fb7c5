import { useMemo } from "react";
import { useGetProbeLocations } from "@tessa-portal/hooks/services/probe/location/useGetProbeLocations";
import { useGetProbes } from "@tessa-portal/hooks/services/probe";
import { Probe } from "@tessa-portal/types/services/probe";

export interface ProbeData extends Probe {
  country_name: string;
  device_status: "online" | "offline";
  type: string;
  device_model: string;
}

export const useGetProbesData = () => {
  const { data: locations, isLoading: isLoadingLocations } =
    useGetProbeLocations();
  const { data: probes, isLoading: isLoadingProbes } = useGetProbes();

  const getDeviceStatus = (probe) => {
    if (!probe?.devices || probe.devices.length === 0) {
      return "";
    }
    if (probe.devices[0]?.type === "Android") {
      return probe.devices[0]?.status === "online" &&
        probe.devices[0]?.stf_status === "online" &&
        probe.status === "online"
        ? "online"
        : "offline";
    }
    return probe.devices[0]?.status === "online" && probe.status === "online"
      ? "online"
      : "offline";
  };

  const probeData = useMemo(() => {
    if (!probes || !locations) return [];

    return probes.map((probe) => {
      let country_name: string = "";
      country_name =
        locations?.find((item) => item.country_iso === probe.country_iso)
          ?.country_name || "";

      return {
        ...probe,
        country_name: country_name,
        device_status: getDeviceStatus(probe),
        type: probe?.devices?.[0]?.type,
        device_model: probe?.devices?.[0]?.name,
      } as ProbeData;
    });
  }, [probes, locations]);

  return {
    data: probeData,
    isLoading: isLoadingLocations || isLoadingProbes,
  };
};
