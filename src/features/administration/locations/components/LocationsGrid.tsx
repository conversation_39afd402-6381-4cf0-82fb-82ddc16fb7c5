import { useMemo, useState } from "react";
import {
  ScaleButton,
  ScaleChip,
  ScaleIconActionAdd,
  ScaleIconContentSimCard,
  ScaleIconDeviceMobilePhoneInsurance,
  ScaleIconUserFileBussinesUsers,
  ScaleTooltip,
} from "@telekom/scale-components-react";
import AddLocationModal from "./AddLocationModal/AddLocationModal";
import { Probe, ProbeLocation } from "@tessa-portal/types/services/probe";
import { Subscriber } from "@tessa-portal/types/services/simManager";
import { User } from "@tessa-portal/types/services/users";
import Card from "./Card";
import {
  calculateDeviceCounts,
  createLocationCards,
} from "../helpers/Calculations";
import { DebouncedSearchField } from "@tessa-portal/components/common/SearchField";

interface LocationProps {
  probes: Probe[];
  locations: ProbeLocation[];
  subscribers: Subscriber[];
  users: User[];
}

interface LocationCardData {
  iso: string;
  name: string;
  offlineDevicesAndroid: number;
  offlineDevicesNonAndroid: number;
  onlineDevicesAndroid: number;
  onlineDevicesNonAndroid: number;
  mobileProbes: number;
  ipPhonesProbes: number;
  modemProbes: number;
}

const LocationsGrid: React.FC<LocationProps> = ({
  probes,
  locations,
  subscribers,
  users,
}) => {
  const [showLocationModal, setShowLocationModal] = useState<boolean>(false);
  const [search, setSearch] = useState<string>("");

  // Memoized device counts using optimized single-pass calculation
  const deviceCounts = useMemo(() => calculateDeviceCounts(probes), [probes]);

  // Memoized location cards using optimized single-pass calculation
  const locationCards = useMemo<LocationCardData[]>(
    () => createLocationCards(locations, probes),
    [locations, probes],
  );

  // Memoized filtered and sorted cards
  const filteredCards = useMemo(() => {
    const searchLower = search.toLowerCase();
    return locationCards
      ?.sort((a, b) => a.name.localeCompare(b.name))
      .filter((card) => card.name.toLowerCase().includes(searchLower));
  }, [locationCards, search]);

  return (
    <div>
      <div className="tw-flex tw-items-center tw-justify-between">
        <ScaleButton
          size="small"
          variant="secondary"
          onClick={() => setShowLocationModal(true)}
        >
          <ScaleIconActionAdd />
          Location
        </ScaleButton>
        <div>
          <ScaleTooltip content="No. of probes">
            <ScaleChip type="dynamic">
              <div slot="chip-icon">
                <ScaleIconDeviceMobilePhoneInsurance />
              </div>
              {probes?.length ?? 0}
            </ScaleChip>
          </ScaleTooltip>
          <ScaleTooltip content="No. of users">
            <ScaleChip type="dynamic" className="tw-ml-3">
              <div slot="chip-icon">
                <ScaleIconUserFileBussinesUsers />
              </div>
              {users?.length ?? 0}
            </ScaleChip>
          </ScaleTooltip>
          <ScaleTooltip content="No. of subscribers">
            <ScaleChip type="dynamic" className="tw-ml-3">
              <div slot="chip-icon">
                <ScaleIconContentSimCard />
              </div>
              {subscribers?.length ?? 0}
            </ScaleChip>
          </ScaleTooltip>
        </div>
        <div>
          <DebouncedSearchField
            onChange={(value) => setSearch(value)}
            label="Search locations"
            debounce={200}
          />
        </div>
      </div>

      {showLocationModal && (
        <AddLocationModal onClose={() => setShowLocationModal(false)} />
      )}

      <div className="tw-grid tw-auto-rows-fr tw-grid-cols-4 tw-gap-6 tw-pb-6">
        <Card
          iso="all"
          name="All probes"
          offline={
            deviceCounts.android.offline + deviceCounts.nonAndroid.offline
          }
          online={deviceCounts.android.online + deviceCounts.nonAndroid.online}
          mobileProbes={deviceCounts.mobile}
          ipPhonesProbes={deviceCounts.ipPhones}
          modemProbes={deviceCounts.modem}
          probesLength={0}
        />
      </div>

      <div className="tw-grid tw-auto-rows-fr tw-grid-cols-4 tw-gap-6 tw-pb-6">
        {filteredCards.map(
          ({
            iso,
            name,
            offlineDevicesAndroid,
            offlineDevicesNonAndroid,
            onlineDevicesAndroid,
            onlineDevicesNonAndroid,
            mobileProbes,
            ipPhonesProbes,
            modemProbes,
          }) => (
            <Card
              key={iso}
              iso={iso}
              name={name}
              offline={offlineDevicesAndroid + offlineDevicesNonAndroid}
              online={onlineDevicesAndroid + onlineDevicesNonAndroid}
              mobileProbes={mobileProbes}
              ipPhonesProbes={ipPhonesProbes}
              modemProbes={modemProbes}
              probesLength={
                probes?.filter((probe) => probe.country_iso === iso).length ?? 0
              }
            />
          ),
        )}
      </div>
    </div>
  );
};

export default LocationsGrid;
