import { useState } from "react";
import {
  ScaleMenuFlyout,
  ScaleMenuFlyoutList,
  ScaleMenuFlyoutItem,
  ScaleIconActionMore,
  ScaleButton,
  ScaleTooltip,
  ScaleCard,
} from "@telekom/scale-components-react";
import { H5 } from "@tessa-portal/components/common/Typography";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { useDeleteProbeLocation } from "@tessa-portal/hooks/services/probe/location/useDeleteProbeLocation";
import { Link } from "react-router-dom";
import RenameLocationModal from "./RenameLocationModal";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";
import StyledScaleTag from "@tessa-portal/components/common/StyledScaleTag";

type CardProps = {
  iso: string;
  name: string;
  online: number;
  offline: number;
  mobileProbes: number;
  ipPhonesProbes: number;
  modemProbes: number;
  probesLength: number;
};

const Card = ({
  iso,
  name,
  online,
  offline,
  mobileProbes,
  ipPhonesProbes,
  modemProbes,
  probesLength,
}: CardProps) => {
  const showConfirm = useConfirm();

  const [showRenameLocationModal, setRenameLocationModal] = useState(false);

  const { mutateAsync: deleteProbeLocation } = useDeleteProbeLocation();

  const toast = useToastNotification();

  const handleDeleteLocation = () => {
    showConfirm({
      title: "Delete location",
      message: "Are you sure to delete location?",
      confirmButtonText: "Delete",
      onConfirm: () =>
        deleteProbeLocation(
          { country_iso: iso },
          {
            onSuccess: () => {
              toast.open({
                heading: "Success",
                text: "Location has been deleted",
                variant: "success",
              });
            },
            onError: () => {
              toast.open({
                heading: "Error",
                text: "Unable to delete location",
                variant: "danger",
              });
            },
          },
        ),
    });
  };

  return (
    <ScaleCard>
      <div className="tw-flex tw-justify-between">
        <Link to={{ pathname: "/administration/probes" }}>
          <H5 asChild className="tw-mb-8">
            <h2>{name}</h2>
          </H5>
        </Link>
        {iso !== "all" && (
          <ScaleMenuFlyout direction="bottom-left">
            <ScaleButton
              slot="trigger"
              variant="secondary"
              size="small"
              icon-only
            >
              <ScaleIconActionMore
                accessibility-title="more"
                selected
                style={{ transform: "rotate(90deg)" }}
              />
            </ScaleButton>
            <ScaleMenuFlyoutList>
              <ScaleMenuFlyoutItem
                onScale-select={() => {
                  setRenameLocationModal(true);
                }}
              >
                Rename
              </ScaleMenuFlyoutItem>
              {probesLength > 0 && (
                <ScaleTooltip content="The location has assigned probes.">
                  <ScaleMenuFlyoutItem
                    disabled
                    onScale-select={() => {
                      handleDeleteLocation();
                    }}
                  >
                    Delete
                  </ScaleMenuFlyoutItem>
                </ScaleTooltip>
              )}
              {probesLength === 0 && (
                <ScaleMenuFlyoutItem
                  onScale-select={() => {
                    handleDeleteLocation();
                  }}
                >
                  Delete
                </ScaleMenuFlyoutItem>
              )}
            </ScaleMenuFlyoutList>
          </ScaleMenuFlyout>
        )}
      </div>
      {showRenameLocationModal && (
        <RenameLocationModal
          onClose={() => setRenameLocationModal(false)}
          name={name}
          iso={iso}
        />
      )}
      <div className="tw-flex tw-justify-between">
        <div>
          {offline > 0 && (
            <ScaleTooltip content="No. of offline devices">
              <StyledScaleTag color="red" textColor="white" type="strong">
                {offline} offline
              </StyledScaleTag>
            </ScaleTooltip>
          )}
          {offline === 0 && online >= 0 && (
            <ScaleTooltip content="No. of online devices">
              <StyledScaleTag color="green" textColor="white" type="strong">
                {online} online
              </StyledScaleTag>
            </ScaleTooltip>
          )}
        </div>

        <div className="tw-flex tw-justify-end">
          <ScaleTooltip content="No. of mobile probes">
            <StyledScaleTag color="standard" type="standard">
              {mobileProbes}
            </StyledScaleTag>
          </ScaleTooltip>
          <ScaleTooltip content="No. of IP-Phone probes">
            <StyledScaleTag
              color="standard"
              type="standard"
              className="tw-ml-3"
            >
              {ipPhonesProbes}
            </StyledScaleTag>
          </ScaleTooltip>
          <ScaleTooltip content="No. of modem probes">
            <StyledScaleTag
              color="standard"
              type="standard"
              className="tw-ml-3"
            >
              {modemProbes}
            </StyledScaleTag>
          </ScaleTooltip>
        </div>
      </div>
    </ScaleCard>
  );
};

export default Card;
