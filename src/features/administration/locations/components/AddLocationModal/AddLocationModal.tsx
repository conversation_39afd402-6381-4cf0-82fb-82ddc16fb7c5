import { ReactNode, useEffect, useRef, useState } from "react";
import Modal, { ModalRef } from "@tessa-portal/components/common/Modal";
import { useConfirm } from "@tessa-portal/hooks/useConfirm";
import { useAddProbeLocation } from "@tessa-portal/hooks/services/probe/location/useAddProbeLocation";
import {
  SubmitHandler,
  useForm,
  FormProvider,
  useFormContext,
} from "react-hook-form";
import { ScaleHelperText } from "@telekom/scale-components-react";
import TextField from "@tessa-portal/helpers/react-hook-form/TextField";
import clsx from "clsx";
import { StyledScaleButton } from "@tessa-portal/components/common/StyledScaleButton";
import useToastNotification from "@tessa-portal/hooks/useToastNotification";

type AddLocationModalProps = {
  onClose: () => void;
};

export type AddLocationFormValues = {
  name: string;
  iso: string;
};

const AddLocationModal = ({ onClose: handleClose }: AddLocationModalProps) => {
  const { mutateAsync: addLocation, error, isError } = useAddProbeLocation();
  const toast = useToastNotification();

  const defaultValues: AddLocationFormValues = {
    name: "",
    iso: "",
  };

  const form = useForm<AddLocationFormValues>({
    mode: "all",
    defaultValues,
  });

  const onSubmit: SubmitHandler<AddLocationFormValues> = async (data) => {
    const { name, iso } = data;
    addLocation(
      {
        country_name: name,
        country_iso: iso,
      },
      {
        onSuccess: () => {
          handleClose();
          toast.open({
            heading: "Success",
            text: "Location added successfully",
            variant: "success",
          });
        },
        onError: () => {
          toast.open({
            heading: "Error",
            text: "Unable to add location",
            variant: "danger",
          });
        },
      },
    );
  };

  const isoRegex = /^[a-zA-Z]{3}$/;
  const specialCharRegex = /[!@#$%^&*(),.?":{}|<>]/;

  return (
    <FormProvider {...form}>
      <FormModal onClose={handleClose}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="tw-flex tw-justify-evenly tw-gap-4 tw-pb-4">
            <TextField
              className="tw-w-full"
              label="Name *"
              name="name"
              rules={{
                validate: (value) =>
                  !specialCharRegex.test(value) ||
                  "Special characters are not allowed",
                required: "Name is required",
              }}
              type="text"
            />
            <TextField
              label="ISO *"
              className="tw-w-full"
              name="iso"
              rules={{
                validate: (value) =>
                  isoRegex.test(value) || "Must be a three letter ISO code",
                required: "ISO is required",
              }}
              type="text"
            />
          </div>
          <div className={clsx(!isError && "tw-invisible")}>
            <ScaleHelperText variant="danger">{error?.message}</ScaleHelperText>
          </div>
          <div className="tw-flex tw-flex-none tw-justify-end tw-gap-4">
            <Modal.CloseButton type="button" variant="secondary">
              Cancel
            </Modal.CloseButton>
            <StyledScaleButton variant="primary" type="submit">
              Add
            </StyledScaleButton>
          </div>
        </form>
      </FormModal>
    </FormProvider>
  );
};

export default AddLocationModal;

type FormModalProps = {
  children: ReactNode;
  onClose: () => void;
};

const FormModal = ({ children, onClose: handleClose }: FormModalProps) => {
  const modalRef = useRef<ModalRef>(null);

  const [confirmed, setConfirmed] = useState(false);

  const {
    formState: { isDirty },
  } = useFormContext();

  useEffect(() => {
    if (confirmed && modalRef.current) {
      modalRef.current?.closeModal();
    }
  }, [confirmed]);

  const showConfirm = useConfirm();

  const handleBeforeClose = (e: Event) => {
    if (isDirty && !confirmed) {
      e.preventDefault();

      showConfirm({
        title: "Close Location",
        message: "Are you sure you want to close the location?",
        onConfirm: () => {
          setConfirmed(true);
        },
      });
    }
  };

  return (
    <Modal
      disablePortal
      heading="Add location"
      onScale-before-close={handleBeforeClose}
      onScale-close={handleClose}
      onScale-open={() => {
        const campaignNameInput: HTMLScaleTextFieldElement | null =
          document.querySelector("input[name='name']");

        campaignNameInput?.focus();
      }}
      opened
      styles={
        "div[role = dialog] { min-width: max(var(--telekom-size-generic-size-23), min(75vw, calc(0.3 * var(--scl-grid-max-width))));}"
      }
      ref={modalRef}
    >
      {children}
    </Modal>
  );
};
