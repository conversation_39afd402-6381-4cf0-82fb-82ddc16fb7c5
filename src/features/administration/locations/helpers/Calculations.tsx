import { Probe, ProbeLocation } from "@tessa-portal/types/services/probe";

interface DeviceCounts {
  android: {
    online: number;
    offline: number;
  };
  nonAndroid: {
    online: number;
    offline: number;
  };
  mobile: number;
  ipPhones: number;
  modem: number;
}

interface LocationCard {
  iso: string;
  name: string;
  offlineDevicesAndroid: number;
  offlineDevicesNonAndroid: number;
  onlineDevicesAndroid: number;
  onlineDevicesNonAndroid: number;
  mobileProbes: number;
  ipPhonesProbes: number;
  modemProbes: number;
}

const isDeviceOnline = (probe: Probe): boolean => {
  if (!probe.devices?.[0]) return false;

  const isAndroid = probe.devices[0].type === "Android";
  if (isAndroid) {
    return (
      probe.devices[0].status === "online" &&
      probe.devices[0].stf_status === "online" &&
      probe.status === "online"
    );
  }
  return probe.devices[0].status === "online" && probe.status === "online";
};

const getDeviceType = (probe: Probe) => {
  const deviceType = probe.devices?.[0]?.type;
  if (!deviceType) return "unknown";
  return deviceType;
};

export const calculateDeviceCounts = (
  probes: Probe[] | undefined,
): DeviceCounts => {
  if (!probes) {
    return {
      android: { online: 0, offline: 0 },
      nonAndroid: { online: 0, offline: 0 },
      mobile: 0,
      ipPhones: 0,
      modem: 0,
    };
  }

  return probes.reduce(
    (counts, probe) => {
      if (!probe.devices?.[0]) return counts;

      const deviceType = getDeviceType(probe);
      const isOnline = isDeviceOnline(probe);

      // Count Android/Non-Android devices
      if (deviceType === "Android") {
        if (isOnline) {
          counts.android.online++;
        } else {
          counts.android.offline++;
        }
      } else if (deviceType !== "unknown") {
        if (isOnline) {
          counts.nonAndroid.online++;
        } else {
          counts.nonAndroid.offline++;
        }
      }

      // Count by device category
      if (deviceType === "Android" || deviceType === "iOS") {
        counts.mobile++;
      } else if (deviceType === "IP_Phone") {
        counts.ipPhones++;
      } else if (deviceType === "Analogue_Modem") {
        counts.modem++;
      }

      return counts;
    },
    {
      android: { online: 0, offline: 0 },
      nonAndroid: { online: 0, offline: 0 },
      mobile: 0,
      ipPhones: 0,
      modem: 0,
    },
  );
};

export const createLocationCards = (
  locations: ProbeLocation[] | undefined,
  probes: Probe[] | undefined,
): LocationCard[] => {
  if (!locations || !probes) return [];

  const locationMap = new Map<string, LocationCard>();

  // Initialize location cards
  locations.forEach((location) => {
    locationMap.set(location.country_iso, {
      iso: location.country_iso,
      name: location.country_name,
      offlineDevicesAndroid: 0,
      offlineDevicesNonAndroid: 0,
      onlineDevicesAndroid: 0,
      onlineDevicesNonAndroid: 0,
      mobileProbes: 0,
      ipPhonesProbes: 0,
      modemProbes: 0,
    });
  });

  // Count devices for each location in a single pass
  probes.forEach((probe) => {
    const locationCard = locationMap.get(probe.country_iso || "");
    if (!locationCard || !probe.devices?.[0]) return;

    const deviceType = getDeviceType(probe);
    const isOnline = isDeviceOnline(probe);

    // Update Android/Non-Android counts
    if (deviceType === "Android") {
      if (isOnline) {
        locationCard.onlineDevicesAndroid++;
      } else {
        locationCard.offlineDevicesAndroid++;
      }
    } else if (deviceType !== "unknown") {
      if (isOnline) {
        locationCard.onlineDevicesNonAndroid++;
      } else {
        locationCard.offlineDevicesNonAndroid++;
      }
    }

    // Update device category counts
    if (deviceType === "Android" || deviceType === "iOS") {
      locationCard.mobileProbes++;
    } else if (deviceType === "IP_Phone") {
      locationCard.ipPhonesProbes++;
    } else if (deviceType === "Analogue_Modem") {
      locationCard.modemProbes++;
    }
  });

  return Array.from(locationMap.values());
};
