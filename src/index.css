@tailwind base;
@tailwind components;
@tailwind utilities;

/* SCALE OVERRIDES */

html {
  /* Tailwind sets an explicit line-height, but Scale assumes the default */
  line-height: inherit;
  hyphens: auto;
}

body {
  background-color: var(--telekom-color-background-canvas);
  color: var(--telekom-color-text-and-icon-standard);
}

scale-breadcrumb::part(current) {
  line-height: normal !important;
}

.scale-telekom-nav-list[variant="functions"]
  scale-telekom-mobile-menu-item::part(icon-right-container) {
  position: relative;
}

.scale-telekom-nav-list[variant="functions"]
  scale-telekom-mobile-menu-item
  scale-icon-navigation-external-link
  .scale-icon {
  width: 18px;
  height: 18px;
}

.tw-auto-rows-fr scale-card {
  display: flex;
}

.tw-auto-rows-fr scale-card::part(border) {
  display: flex;
  flex-grow: 1;
}

.tw-auto-rows scale-card::part(base) {
  display: flex;
  flex-grow: 1;
}

.table--sortable th:not([aria-sort]):hover {
  padding: var(--telekom-spacing-composition-space-00)
    var(--telekom-spacing-composition-space-04);
  padding-right: var(--telekom-spacing-composition-space-07);
}

.table--sortable th:not([aria-sort]) .scale-sort-indicator {
  display: none;
}

/* TAILWIND OVERRIDES */

scale-telekom-mobile-menu-item a {
  color: var(--_color);
}

.border-regular {
  border: 1px solid var(--telekom-color-ui-regular);
}
.border-error {
  border: 2px solid var(--telekom-color-text-and-icon-functional-danger);
}
