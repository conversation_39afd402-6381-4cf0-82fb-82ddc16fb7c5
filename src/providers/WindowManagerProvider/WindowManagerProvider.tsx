import {
  createContext,
  FunctionComponent,
  useCallback,
  useEffect,
  useReducer,
  useMemo,
} from "react";

type WindowState = "OPEN" | "CLOSED" | "CLOSING" | undefined;

interface WindowContainer {
  id: string;
  title: string;
  url: string;
  state: WindowState;
  size: { width: number; height: number };
  window: Window | null;
}

export interface IWindowsManagerContext {
  windows: WindowContainer[];
  openWindow: (
    win: Omit<WindowContainer, "window" | "state"> & {
      size: { width: number; height: number };
    },
  ) => void;
  closeWindow: (id: string) => void;
  focusWindow: (id: string) => void;
  isOpen: (id: string) => boolean;
  getState: (id: string) => WindowState;
}

interface WindowsManagerProps {
  children: React.ReactNode;
}

type Action =
  | { type: "OPEN_WINDOW"; payload: WindowContainer }
  | { type: "CLOSE_WINDOW"; payload: { id: string } }
  | { type: "START_DESTROY_WINDOW"; payload: { id: string } }
  | { type: "FINISH_DESTROY_WINDOW"; payload: { id: string } }
  | { type: "SET_WINDOW_STATE"; payload: { id: string; state: WindowState } };

const WindowsManagerContext = createContext<IWindowsManagerContext | undefined>(
  undefined,
);

const windowsReducer = (
  state: WindowContainer[],
  action: Action,
): WindowContainer[] => {
  switch (action.type) {
    case "OPEN_WINDOW":
      return [...state, action.payload];
    case "CLOSE_WINDOW":
      return state.filter((win) => win.id !== action.payload.id);
    case "START_DESTROY_WINDOW":
      return state.map((win) =>
        win.id === action.payload.id ? { ...win, state: "CLOSING" } : win,
      );
    case "FINISH_DESTROY_WINDOW":
      return state.filter((win) => win.id !== action.payload.id);
    case "SET_WINDOW_STATE":
      return state.map((win) =>
        win.id === action.payload.id
          ? { ...win, state: action.payload.state }
          : win,
      );
    default:
      return state;
  }
};

const WindowsManagerProvider: FunctionComponent<WindowsManagerProps> = ({
  children,
}) => {
  const [windows, dispatch] = useReducer(windowsReducer, []);

  const openWindow = useCallback(
    (
      win: Omit<WindowContainer, "window" | "state"> & {
        size: { width: number; height: number };
      },
    ) => {
      const newWindow = window.open(
        win.url,
        win.id,
        `height=${win.size.height},width=${win.size.width}`,
      );

      if (newWindow) {
        const windowContainer: WindowContainer = {
          ...win,
          state: "OPEN",
          window: newWindow,
        };

        // Handler for beforeunload event
        const handleBeforeUnload = (event: BeforeUnloadEvent) => {
          // Optionally, set state to "CLOSING" if you want to track the closing process
          dispatch({ type: "START_DESTROY_WINDOW", payload: { id: win.id } });

          event.preventDefault();
          const message = "Are you sure you want to close this window?";
          event.returnValue = message;
          return message;
        };

        // Handler for unload event
        const handleUnload = () => {
          // Set state to "CLOSED" or remove the window from the context
          dispatch({ type: "FINISH_DESTROY_WINDOW", payload: { id: win.id } });
        };

        // Attach event listeners to the child window
        newWindow.addEventListener("beforeunload", handleBeforeUnload);

        // Optionally, clean up event listeners when the window is closed programmatically
        const cleanup = () => {
          newWindow.removeEventListener("beforeunload", handleBeforeUnload);
          newWindow.removeEventListener("unload", handleUnload);
        };

        const interval = setInterval(() => {
          if (newWindow.closed) {
            clearInterval(interval);
            dispatch({
              type: "FINISH_DESTROY_WINDOW",
              payload: { id: win.id },
            });
          }
        }, 1000);

        newWindow.addEventListener("close", cleanup);
        dispatch({ type: "OPEN_WINDOW", payload: windowContainer });
      } else {
        console.error(`Failed to open window: ${win.id}`);
      }
    },
    [],
  );

  const closeWindow = useCallback(
    (id: string) => {
      const windowToKill = windows.find((win) => win.id === id);
      if (windowToKill?.window) {
        windowToKill.window.close();
      }
      dispatch({ type: "CLOSE_WINDOW", payload: { id } });
    },
    [windows],
  );

  const focusWindow = useCallback(
    (id: string) => {
      const win = windows.find((w) => w.id === id);
      win?.window?.focus();
    },
    [windows],
  );

  const isOpen = useCallback(
    (id: string): boolean => {
      const win = windows.find((w) => w.id === id);
      return !!(win && win.state === "OPEN");
    },
    [windows],
  );

  const getState = useCallback(
    (id: string): WindowState => {
      const win = windows.find((w) => w.id === id);
      return win?.state;
    },
    [windows],
  );

  // Handle unloading of the main window
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (windows.length > 0) {
        event.preventDefault();
        const message = "Other open windows will also be closed.";
        event.returnValue = message;
        return message;
      }
    };

    const handleUnload = () => {
      windows.forEach((win) => {
        if (win.window && !win.window.closed) {
          win.window.close();
        }
      });
    };

    if (windows.length > 0) {
      window.addEventListener("beforeunload", handleBeforeUnload);
      window.addEventListener("unload", handleUnload);
    }

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      window.removeEventListener("unload", handleUnload);
    };
  }, [windows]);

  const contextValue = useMemo(
    () => ({
      windows,
      openWindow,
      closeWindow,
      focusWindow,
      isOpen,
      getState,
    }),
    [windows, openWindow, closeWindow, focusWindow, isOpen, getState],
  );

  return (
    <WindowsManagerContext.Provider value={contextValue}>
      {children}
    </WindowsManagerContext.Provider>
  );
};

const WindowsManagerConsumer = WindowsManagerContext.Consumer;

export {
  WindowsManagerProvider,
  WindowsManagerConsumer,
  WindowsManagerContext,
};
