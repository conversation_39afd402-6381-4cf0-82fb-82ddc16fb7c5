import { createContext, ReactNode, useCallback, useReducer } from "react";
import { JSX } from "@telekom/scale-components";
import { ScaleNotification } from "@telekom/scale-components-react";
import { UnreachableCaseError } from "ts-essentials";
import Markdown from "react-markdown";
import { markdownRenderers } from "@tessa-portal/components/common/MarkdownRenderers";
import styled from "styled-components";

const StyledScaleNotification = styled(ScaleNotification)({
  "@media (max-width: 480px)": {
    "--width-toast": "18rem",
  },
});

type NewNotification = Omit<JSX.ScaleNotification, "type"> & {
  text?: ReactNode;
};

type Notification = NewNotification & {
  id: number;
};

interface ToastNotificationContext {
  notifications: Notification[];
  open: (notification: NewNotification) => void;
  close: (notificationId: Notification["id"]) => void;
  closeAll: () => void;
}

export const ToastNotificationContext = createContext<ToastNotificationContext>(
  {
    notifications: [],
    open: () => {},
    close: () => {},
    closeAll: () => {},
  },
);

type Action =
  | { type: "OPEN"; notification: NewNotification }
  | { type: "CLOSE"; notificationId: Notification["id"] }
  | { type: "CLOSE_ALL" };

const reducer = (state: Notification[], action: Action): Notification[] => {
  switch (action.type) {
    case "OPEN":
      return [{ id: Date.now(), ...action.notification }, ...state];
    case "CLOSE":
      return state.filter(
        (notification) => notification.id !== action.notificationId,
      );
    case "CLOSE_ALL":
      return [];
    default:
      throw new UnreachableCaseError(action);
  }
};

interface ToastNotificationProviderProps {
  children: ReactNode;
}

const ToastNotificationProvider = ({
  children,
}: ToastNotificationProviderProps) => {
  const [state, dispatch] = useReducer(reducer, []);

  const open = useCallback((notification: NewNotification) => {
    dispatch({ type: "OPEN", notification });
  }, []);

  const close = useCallback((notificationId: Notification["id"]) => {
    dispatch({ type: "CLOSE", notificationId });
  }, []);

  const closeAll = useCallback(() => {
    dispatch({ type: "CLOSE_ALL" });
  }, []);

  return (
    <ToastNotificationContext.Provider
      value={{ notifications: state, open, close, closeAll }}
    >
      {children}
      <div className="scl-toast-stack tw-fixed tw-mt-[108px]">
        {state.map(
          ({ id, text, "onScale-close": handleScaleClose, ...props }) => (
            <StyledScaleNotification
              dismissible
              key={id}
              type="toast"
              onScale-close={(event) => {
                handleScaleClose?.(event);
                close(id);
              }}
              opened
              {...props}
            >
              {text && (
                <span slot="text">
                  <Markdown components={markdownRenderers}>
                    {text.toString()}
                  </Markdown>
                </span>
              )}
            </StyledScaleNotification>
          ),
        )}
      </div>
    </ToastNotificationContext.Provider>
  );
};

export default ToastNotificationProvider;
