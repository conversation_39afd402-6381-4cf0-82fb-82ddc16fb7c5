import { createContext, ReactNode, useState, useCallback } from "react";
import Modal from "@tessa-portal/components/common/Modal";

export type ConfirmModalProps = {
  title: string;
  message: ReactNode;
  confirmButtonText?: string;
  cancelButtonText?: string;
  onClose?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
};

type ConfirmContextType = {
  showConfirmModal: (props: ConfirmModalProps) => void;
  isModalVisible: boolean;
};

export const ConfirmContext = createContext<ConfirmContextType | undefined>(
  undefined,
);

export const ConfirmProvider = ({ children }: { children: ReactNode }) => {
  const [modalProps, setModalProps] = useState<ConfirmModalProps | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showConfirmModal = useCallback((props: ConfirmModalProps) => {
    setModalProps(props);
    setIsModalVisible(true);
  }, []);

  const handleClose = useCallback(() => {
    if (modalProps?.onClose) {
      modalProps.onClose();
    }
    setIsModalVisible(false);
    setModalProps(null);
  }, [modalProps]);

  const handleConfirm = useCallback(() => {
    if (modalProps?.onConfirm) {
      modalProps.onConfirm();
    }
    setIsModalVisible(false);
    setModalProps(null);
  }, [modalProps]);

  const handleCancel = useCallback(() => {
    if (modalProps?.onCancel) {
      modalProps.onCancel();
    }
    setIsModalVisible(false);
    setModalProps(null);
  }, [modalProps]);

  return (
    <ConfirmContext.Provider value={{ showConfirmModal, isModalVisible }}>
      {children}
      <Modal
        heading={modalProps?.title}
        opened={isModalVisible}
        id="confirm-modal"
        onScale-close={handleClose}
        size="small"
      >
        <p>{modalProps?.message}</p>
        <Modal.CloseButton
          onClick={handleCancel}
          slot="action"
          type="button"
          variant="secondary"
        >
          {modalProps?.cancelButtonText || "Cancel"}
        </Modal.CloseButton>
        <Modal.CloseButton
          onClick={handleConfirm}
          slot="action"
          type="button"
          variant="primary"
        >
          {modalProps?.confirmButtonText || "Confirm"}
        </Modal.CloseButton>
      </Modal>
    </ConfirmContext.Provider>
  );
};

export default ConfirmProvider;
