/* eslint-disable react-hooks/exhaustive-deps */
import _ from "lodash";
import { v4 as uuid } from "uuid";
import {
  createContext,
  FunctionComponent,
  useCallback,
  useMemo,
  useState,
  useRef,
  useEffect,
  ReactNode,
} from "react";
import { useGetStfUser } from "@tessa-portal/hooks/services/stf";
import useUser from "@tessa-portal/hooks/useUser";

const { VITE_STF_WS } = import.meta.env;

// websocket packet types
enum PacketTypes {
  unsupported = -1,
  open = 0, // non-ws
  close = 1, // non-ws
  ping = 2,
  pong = 3,
  message = 4,
  upgrade = 5,
  noop = 6,
}

type Packet = {
  type: PacketTypes;
  data?: any;
  secondDigit?: number;
};

type OpenMsg = {
  sid: string;
  upgrades: any[];
  pingInterval: number;
  pingTimeout: number;
};

type Msg = {
  body?: any;
  data: any;
  progress?: number;
  seq: number;
  source: string;
  success?: boolean;
};

type MsgReceive = {
  type: string;
  id: string;
  msg: Msg;
};

type MsgSend = {
  id: string;
  msg: string;
};

type Subscriber = {
  id: string;
  callback: (msg: string) => any;
};

export interface ViewStartMsg {
  version: number;
  length: number;
  pid: number;
  realWidth: number;
  realHeight: number;
  virtualWidth: number;
  virtualHeight: number;
  orientation: number;
  quirks: { dumb: boolean; alwaysUpright: boolean; tear: boolean };
}

export interface ScreenSize {
  width: number;
  height: number;
}

export interface DeviceDisplay {
  url: string;
  name: string;
  image?: string;
  screenSize?: ScreenSize;
  info?: ViewStartMsg;
  webSocket: WebSocket;
}
export interface IDeviceDisplayContext {
  getDeviceDisplay: (name: string) => DeviceDisplay | undefined;
  openDeviceDisplay: (name: string, displayUrl: string) => void;
  closeDeviceDisplay: (name: string) => void;
  changeScreenResolution: (name: string, size: ScreenSize) => void;
  getControlSocket: () => WebSocket | null;
  sendWithCallback: (
    msg: string,
    channel: string,
    callback: (msg: string) => void,
    data?: string,
  ) => string;
}

interface DeviceDisplayProps {
  children: ReactNode;
}

const DeviceDisplayContext = createContext<IDeviceDisplayContext | undefined>(
  undefined,
);

const DeviceDisplayProvider: FunctionComponent<DeviceDisplayProps> = ({
  children,
}) => {
  const [deviceDisplays, setDeviceDisplays] = useState<DeviceDisplay[]>([]);
  const controlSocketRef = useRef<WebSocket | null>(null);
  const reconnectTime = 5000;

  const { data: user } = useGetStfUser();
  const { token } = useUser();

  const pingIntervalTimer = useRef<number | undefined>();
  const pingTimeoutTimer = useRef<number | undefined>();

  const [pingInterval, setPingInterval] = useState(10000);
  const [pingTimeout, setPingTimeout] = useState(5000);

  const [sendQue, setSendQue] = useState<MsgSend[]>([]);
  const [rcvQue, setRcvQue] = useState<MsgReceive[]>([]);

  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);

  const processImage = useCallback(
    (name: string, imageData: Blob) => {
      const imageUrl = URL.createObjectURL(imageData);
      setDeviceDisplays((displays) =>
        displays.map((display) => {
          if (display.name !== name) {
            return display;
          }
          if (display.image) {
            URL.revokeObjectURL(display.image);
          }
          return { ...display, image: imageUrl };
        }),
      );
    },
    [deviceDisplays],
  );

  const cleanupImage = useCallback(
    (name: string) => {
      setDeviceDisplays((displays) =>
        displays.map((display) => {
          if (display.name !== name) {
            return display;
          }
          if (display.image) {
            URL.revokeObjectURL(display.image);
          }
          return { ...display, image: undefined };
        }),
      );
    },
    [deviceDisplays],
  );

  const processStartInfo = useCallback(
    (name: string, startInfo: ViewStartMsg) => {
      setDeviceDisplays((displays) =>
        displays.map((display) => {
          if (display.name !== name) {
            return display;
          }
          return {
            ...display,
            info: startInfo,
          };
        }),
      );
    },
    [deviceDisplays],
  );

  const handleMessage = useCallback(
    (name: string, msg: MessageEvent) => {
      try {
        if (msg.data instanceof Blob) {
          processImage(name, msg.data);
        } else if (typeof msg.data === "string") {
          const startPrefix = "start ";
          if (msg.data.startsWith(startPrefix)) {
            const data = msg.data.slice(startPrefix.length);
            const startInfo: ViewStartMsg = JSON.parse(data);
            processStartInfo(name, startInfo);
          }
        } else {
          console.error("ws: unknown message:", msg.data);
        }
      } catch (error: any) {
        console.error("ws: error processing message:", error.message);
      }
    },
    [deviceDisplays, processImage, processStartInfo],
  );

  // init
  const initializeScreenWs = useCallback(
    (name: string, url: string) => {
      const ws = new WebSocket(url);
      ws.onopen = () => {
        ws.send("on");
        changeScreenResolution(name, { width: 319, height: 692 });
      };
      ws.onmessage = (e) => handleMessage(name, e);
      ws.onclose = (e) => {
        if (e.code !== 1000) {
          setTimeout(() => {
            cleanupImage(name);
            reconnectWs(name);
          }, reconnectTime);
        }
      };
      ws.onerror = (e) => {
        console.error("ws: error:", e);
        cleanupImage(name);
        ws.close();
      };
      setDeviceDisplays(
        deviceDisplays.map((device) =>
          device.name === name ? { ...device, webSocket: ws } : device,
        ),
      );

      return ws;
    },
    [handleMessage, cleanupImage],
  );

  // get device
  const getDeviceDisplay = useCallback(
    (name: string) => {
      return deviceDisplays.find((display) => display.name === name);
    },
    [deviceDisplays],
  );

  // open
  const openDeviceDisplay = useCallback(
    (name: string, displayUrl: string) => {
      const url = displayUrl.split("/d/")[1];
      const wssProcotol =
        window.location.origin.indexOf("https") < 0 ? "ws" : "wss";
      const wsUrl = `${wssProcotol}://${window.location.host}/stfDisplay/d/${url}`;
      const existingDevice = deviceDisplays.find(
        (display) => display.url === url,
      );
      if (!existingDevice) {
        const webSocket = initializeScreenWs(name, wsUrl);

        setDeviceDisplays([...deviceDisplays, { name, url, webSocket }]);
      }
    },
    [deviceDisplays, initializeScreenWs],
  );

  // close
  const closeDeviceDisplay = useCallback(
    (name: string) => {
      const device = deviceDisplays.find((device) => device.name === name);
      if (device) {
        device.webSocket.close();
        setDeviceDisplays(
          deviceDisplays.filter((device) => device.name !== name),
        );
      }
    },
    [deviceDisplays],
  );

  // update resolution
  const changeScreenResolution = useCallback(
    (name: string, size: ScreenSize) => {
      const device = deviceDisplays.find((device) => device.name === name);
      if (device) {
        try {
          device.webSocket.send(`size ${size.width}x${size.height}`);
        } catch (error: any) {
          console.error("ws: error sending message:", error.message);
        }
      }
    },
    [deviceDisplays],
  );

  // reconnect
  const reconnectWs = useCallback(
    (name: string) => {
      const device = deviceDisplays.find((device) => device.name === name);
      if (device) {
        const ws = initializeScreenWs(name, device.url);
        setDeviceDisplays(
          deviceDisplays.map((device) =>
            device.name === name ? { ...device, webSocket: ws } : device,
          ),
        );
      }
    },
    [deviceDisplays, initializeScreenWs],
  );

  //  Control socket

  const getControlSocket = useCallback(() => {
    return controlSocketRef.current;
  }, [controlSocketRef.current]);

  const connect = useCallback(() => {
    if (user) {
      const pat = /^https?:\/\//i;
      const wssProcotol =
        window.location.origin.indexOf("https") < 0 ? "ws" : "wss";
      let url = "";
      if (VITE_STF_WS && pat.test(VITE_STF_WS)) {
        url = VITE_STF_WS;
      } else {
        // relative URL
        url = `${wssProcotol}://${window.location.host}${VITE_STF_WS}`;
      }
      controlSocketRef.current = new WebSocket(
        `${url}/socket.io/?uip=${user && encodeURIComponent(user.ip)}&EIO=3&transport=websocket`,
      );

      controlSocketRef.current.onopen = () => {
        const msg = `42["tessa.token","${token}"]`;
        controlSocketRef.current?.send(msg);
      };
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      connect();
    }
  }, [user]);

  useEffect(() => {
    if (subscribers && subscribers.length > 0 && rcvQue && rcvQue.length > 0) {
      let idsToRemove: string[] = [];
      // todo: on tx.done merge all messages to one
      rcvQue
        .filter((n: MsgReceive, i: number) => rcvQue.indexOf(n) === i)
        .forEach((item) => {
          if (item.type !== "tx.progress") {
            subscribers.forEach((sub) => {
              if (sub.id === item.id) {
                idsToRemove = [...idsToRemove, sub.id];
                const message = rcvQue
                  .filter((msg: MsgReceive) => msg.id === sub.id)
                  .map((val: MsgReceive) =>
                    val.msg?.data ? val.msg?.data : "",
                  );
                sub.callback(JSON.stringify(message));
              }
            });
          }
        });

      // if idsToRemove is empty don't update state with same data as it will create infinite loop
      if (!_.isEmpty(idsToRemove)) {
        setSubscribers((prevState) =>
          prevState.filter((item) => !idsToRemove.includes(item.id)),
        );
        setRcvQue((prevState) =>
          prevState.filter((item) => !idsToRemove.includes(item.id)),
        );
      }
    }
  }, [subscribers, rcvQue]);

  useEffect(() => {
    // todo: is it safe? what happens when new item is queued?
    const que = [...sendQue];
    if (que && que.length > 0) {
      if (controlSocketRef.current) {
        setSendQue([]);
        que.map((item) => controlSocketRef.current?.send(item.msg));
      }
    }
  }, [sendQue, controlSocketRef.current]);

  const decodePacket = (packetData: string): Packet => {
    try {
      let data: string = packetData;
      // get packet type
      const packetType: PacketTypes = Number(data.charAt(0));
      data = data.slice(1);
      // get second digit if any (what the hell the second digit can mean??)
      let secondDigit = 0;
      if (packetType === PacketTypes.message) {
        secondDigit = Number(data.charAt(0));
        data = data.slice(1);
      }
      // assume that the number has only two digits...
      // if data follows
      if (secondDigit > 0) {
        return { type: packetType, secondDigit, data: JSON.parse(data) };
      }
      return { type: packetType, secondDigit };
    } catch (e: any) {
      console.log(
        "unable to parse incoming packet:",
        packetData,
        "error: ",
        e.message,
      );
    }
    return { type: PacketTypes.noop, secondDigit: 0 };
  };

  const sendPacket = useCallback(
    (packet: Packet) => {
      if (controlSocketRef.current && controlSocketRef.current?.readyState) {
        controlSocketRef.current.send(`${packet.type}`);
      }
    },
    [controlSocketRef.current],
  );
  // send a ping packet
  const ping = useCallback(() => {
    sendPacket({ type: PacketTypes.ping });
  }, [sendPacket]);

  // set ping timeout
  const onHeartbeat = useCallback(
    (timeout: number) => {
      clearTimeout(pingTimeoutTimer.current!);
      pingTimeoutTimer.current = window.setTimeout(
        () => {
          if (
            controlSocketRef.current &&
            controlSocketRef.current.readyState === 3
          )
            return;
          console.log("ws ping response timeout");
          if (controlSocketRef.current) controlSocketRef.current.close();
        },
        timeout || pingInterval + pingTimeout,
      );
    },
    [pingInterval, pingTimeout, controlSocketRef.current],
  );

  // pings server periodically and expects response
  const setPing = useCallback(() => {
    clearTimeout(pingTimeoutTimer.current);
    clearInterval(pingIntervalTimer.current!);
    pingIntervalTimer.current = window.setInterval(() => {
      ping();
      onHeartbeat(pingTimeout);
    }, pingInterval);
  }, [pingTimeout, pingInterval, onHeartbeat, ping]);

  useEffect(() => {
    if (controlSocketRef.current && controlSocketRef.current.OPEN) {
      setPing();
    }
  }, [controlSocketRef.current, pingTimeout, pingInterval, setPing]);

  useEffect(() => {
    if (controlSocketRef.current) {
      controlSocketRef.current.onopen = () => {
        const msg = `42["tessa.token","${token}"]`;
        controlSocketRef.current?.send(msg);
      };
      controlSocketRef.current.onmessage = (msg) => {
        const packet = decodePacket(msg.data);
        switch (packet.type) {
          case PacketTypes.pong:
            clearTimeout(pingTimeoutTimer.current!);
            break;
          case PacketTypes.open:
            if (packet.data) {
              const openMsg: OpenMsg = packet.data;
              if (openMsg) {
                setPingInterval(openMsg.pingInterval);
                setPingTimeout(openMsg.pingTimeout);
              }
            }
            break;
          case PacketTypes.message:
            if (packet.data) {
              const msgType = packet.data[0];
              if (typeof msgType === "string") {
                switch (msgType) {
                  case "socket.ip":
                    break;
                  case "device.change":
                    // handleUpdateDevices(packet.data[1].data);
                    break;
                  case "device.add":
                    console.log("add");
                    // handleUpdateDevices(packet.data[1].data);
                    break;
                  case "device.remove":
                    console.log("removed");
                    // set ready to false to indicate device removal
                    // handleUpdateDevices({
                    //   ...packet.data[1].data,
                    //   ready: false,
                    // });
                    break;
                  case "device.log":
                    break;
                  case "tx.done":
                    controlSocketRef.current?.send(
                      `42["tx.cleanup","${packet.data[1]}"]`,
                    );
                    if (
                      rcvQue.filter(
                        (rcv: MsgReceive) => rcv.id === packet.data[1],
                      ).length
                    ) {
                      setRcvQue((prevState) =>
                        prevState.map((item: MsgReceive) => {
                          if (item.id === packet.data[1]) {
                            return {
                              type: packet.data[0],
                              id: packet.data[1],
                              msg: item.msg,
                            };
                          }
                          return item;
                        }),
                      );
                    } else {
                      setRcvQue((prevState) => [
                        ...prevState,
                        {
                          type: packet.data[0],
                          id: packet.data[1],
                          msg: packet.data[2],
                        },
                      ]);
                    }
                    break;
                  case "tx.progress":
                    if (
                      rcvQue.filter(
                        (rcv: MsgReceive) => rcv.id === packet.data[1],
                      ).length
                    ) {
                      setRcvQue((prevState) => [
                        ...prevState.filter(
                          (item: MsgReceive) => item.id === packet.data[1],
                        ),
                        ...prevState.filter((item: MsgReceive) => [
                          {
                            ...item,
                            msg: {
                              ...item.msg,
                              data: `${item?.msg?.data} ${packet.data[2]?.msg?.data}`,
                            },
                          },
                        ]),
                      ]);
                    } else {
                      setRcvQue((prevState) => [
                        ...prevState,
                        {
                          type: packet.data[0],
                          id: packet.data[1],
                          msg: packet.data[2],
                        },
                      ]);
                    }
                    break;
                  case "tx.cancel":
                    break;
                  default:
                    break;
                }
              }
            }
            break;
          default:
            break;
        }
      };
      controlSocketRef.current.onclose = () => {
        // clear timers
        clearInterval(pingIntervalTimer.current!);
        clearTimeout(pingTimeoutTimer.current!);

        setTimeout(() => {
          connect();
        }, reconnectTime);
      };
      controlSocketRef.current.onerror = (err: Event) => {
        console.error("Socket encountered error: ", err, "Closing socket");
        if (controlSocketRef.current) controlSocketRef.current.close();
      };
    }
  }, [controlSocketRef.current, connect]);

  const sendWithCallback = (
    msg: string,
    channel: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    callback: (msg: any) => void,
    data?: string,
  ): string => {
    const sessionId = uuid();
    setSubscribers((prevState) => [...prevState, { id: sessionId, callback }]);
    // todo: escape data
    const dataToSend = data || null;

    setSendQue((prevState) => [
      ...prevState,
      {
        id: sessionId,
        msg: `42["${msg}","${channel}","${sessionId}",${dataToSend}]`,
      },
    ]);
    return sessionId;
  };

  const contextValue = useMemo(
    () => ({
      getDeviceDisplay,
      openDeviceDisplay,
      closeDeviceDisplay,
      changeScreenResolution,
      getControlSocket,
      sendWithCallback,
    }),
    [
      changeScreenResolution,
      getDeviceDisplay,
      openDeviceDisplay,
      closeDeviceDisplay,
      getControlSocket,
      sendWithCallback,
    ],
  );

  return (
    <DeviceDisplayContext.Provider value={contextValue}>
      {children}
    </DeviceDisplayContext.Provider>
  );
};

const DeviceDisplayConsumer = DeviceDisplayContext.Consumer;

export { DeviceDisplayProvider, DeviceDisplayConsumer, DeviceDisplayContext };
