import clsx from "clsx";
import { ScaleIconAlertWarning } from "@telekom/scale-components-react";
import { useInRouterContext, useNavigate } from "react-router-dom";
import { StyledScaleButton } from "../StyledScaleButton";

const ErrorBoundaryHero = () => {
  const inRouterContext = useInRouterContext();

  return (
    <div className="tw-relative tw-flex tw-h-[640px] tw-flex-col tw-items-center md:tw-h-[400px]">
      <div
        className={clsx(
          "tw-relative tw-z-10 tw-grid tw-h-full tw-max-w-3xl tw-flex-col tw-items-center",
        )}
      >
        <div>
          <ScaleIconAlertWarning
            className="tw-text-functional-danger tw-mr-4"
            size={44}
            decorative={true}
          />
          <h1 className="tw-mb-0 tw-text-style-heading-2">
            Something went wrong!
          </h1>
          <p className="tw-text-callout tw-mb-8 tw-text-style-body">
            Due to a technical issue, we couldn't complete this request.
          </p>
          {inRouterContext ? (
            <ErrorBoundaryHeroLink />
          ) : (
            <StyledScaleButton
              href="/"
              size="small"
              variant="secondary"
              innerAriaLabel="Go back"
            >
              "Go back"
            </StyledScaleButton>
          )}
        </div>
      </div>
    </div>
  );
};

const ErrorBoundaryHeroLink = () => {
  const navigate = useNavigate();

  return (
    <StyledScaleButton
      onClick={() => navigate("/")}
      size="small"
      variant="secondary"
      innerAriaLabel="Go back"
    >
      Go back
    </StyledScaleButton>
  );
};

export default ErrorBoundaryHero;
