import { ScaleIconActionRecordNb } from "@telekom/scale-components-react";

type SimStatusCellProps = {
  status: string;
};

export const SimStatusCell = ({ status }: SimStatusCellProps) => {
  let statusColor = "";

  switch (status) {
    case "Available": {
      statusColor = "green";
      break;
    }
    case "Inserting": {
      statusColor = "yellow";
      break;
    }
    case "In Use": {
      statusColor = "blue";
      break;
    }
  }

  return (
    <span className="tw-flex tw-grow-0 tw-items-center tw-justify-start">
      <ScaleIconActionRecordNb
        fill={`var(--telekom-color-additional-${statusColor}-300)`}
      />
      {status}
    </span>
  );
};

export default SimStatusCell;
