import {
  ComponentProps,
  ForwardedRef,
  forwardRef,
  MouseEvent,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { createPortal } from "react-dom";

import { ScaleModal } from "@telekom/scale-components-react";
import { StyledScaleButton } from "../StyledScaleButton";
import { useMutationObserver } from "@tessa-portal/helpers/react/mutationObserver";

type ModalProps = ComponentProps<typeof ScaleModal> & {
  children: ReactNode;
  disablePortal?: boolean;
  umountOnClose?: boolean;
};

export type ModalRef = {
  modal: null | HTMLScaleModalElement;
  closeModal: () => void;
};

const Modal = forwardRef(
  (
    {
      disablePortal,
      ["onScale-close"]: handleClose,
      ["onScale-open"]: handleOpen,
      opened: openedProp,
      umountOnClose = false,
      ...props
    }: ModalProps,
    ref: ForwardedRef<ModalRef>,
  ) => {
    const returnFocusElementRef = useRef<Element | null>(null);

    const modalRef = useRef<HTMLScaleModalElement>(null);
    const openedRef = useRef<boolean>();
    openedRef.current = openedProp;

    const [opened, setOpened] = useState(false);

    // return modal instance to parent component
    useImperativeHandle(
      ref,
      () => ({
        modal: modalRef.current,
        closeModal: () => modalRef.current?.dispatchEvent(keyboardEscapeEvent),
      }),
      [],
    );

    // trigger modal open when opened prop changes on new mounted modal
    useMutationObserver(
      modalRef,
      () => {
        if (modalRef.current?.classList.contains("hydrated")) {
          if (openedRef.current) {
            setOpened(true);
          }
        }
      },
      {
        attributes: true,
        attributeFilter: ["class"],
      },
    );

    // trigger modal close when opened prop changes
    useEffect(() => {
      if (openedProp && modalRef.current?.classList.contains("hydrated")) {
        setOpened(true);
      }

      if (!openedProp) {
        modalRef.current?.dispatchEvent(keyboardEscapeEvent);
      }
    }, [openedProp]);

    // remember focus element before modal opens
    useEffect(() => {
      if (openedProp && !opened) {
        returnFocusElementRef.current = document.activeElement;
      }
    }, [opened, openedProp]);

    // handle body overflow when modal closes with onmount (e.g. page change)
    const [bodyOverflow, setBodyOverflow] = useState<string | null>(null);
    useEffect(() => {
      if (openedProp && !opened) {
        setBodyOverflow(document.body.style.overflow);
      }

      return () => {
        if (opened) {
          document.body.style.setProperty("overflow", bodyOverflow);
        }
      };
    }, [bodyOverflow, opened, openedProp]);

    // handle closing of other elements inside modal and return focus
    const handleOnScaleClose: ComponentProps<
      typeof ScaleModal
    >["onScale-close"] = (event) => {
      if (event.target !== modalRef.current) {
        event.preventDefault();
      } else {
        setOpened(false);

        if (
          returnFocusElementRef.current &&
          isHTMLScaleModalElement(returnFocusElementRef.current)
        ) {
          const modalCloseButton: HTMLButtonElement | null | undefined =
            returnFocusElementRef.current.shadowRoot?.querySelector(
              ".modal__close-button",
            );

          if (
            modalCloseButton &&
            modalCloseButton instanceof HTMLButtonElement
          ) {
            modalCloseButton.focus();
          }
        }

        if (
          returnFocusElementRef.current &&
          isHTMLScaleButtonElement(returnFocusElementRef.current)
        ) {
          returnFocusElementRef.current.setFocus();
        }

        if (
          returnFocusElementRef.current &&
          returnFocusElementRef.current instanceof HTMLElement
        ) {
          returnFocusElementRef.current?.focus();
        }

        if (handleClose && typeof handleClose === "function") {
          handleClose(event);
        }
      }
    };

    const unmounted = umountOnClose && !openedProp && !opened;

    if (unmounted) {
      return null;
    }

    if (disablePortal) {
      return (
        <ScaleModal
          allowInjectingStyleToBody
          onScale-close={handleOnScaleClose}
          onScale-open={handleOpen}
          opened={opened}
          ref={modalRef}
          {...props}
        />
      );
    }

    return createPortal(
      <ScaleModal
        allowInjectingStyleToBody
        onScale-close={handleOnScaleClose}
        onScale-open={handleOpen}
        opened={opened}
        ref={modalRef}
        {...props}
      />,
      document.body,
    );
  },
);

const CloseButton = ({
  onClick,
  ...props
}: ComponentProps<typeof StyledScaleButton>) => (
  <StyledScaleButton
    onClick={(event: MouseEvent<HTMLScaleButtonElement>) => {
      if (isHTMLScaleButtonElement(event.target)) {
        const target: HTMLScaleButtonElement | null | undefined = event.target;

        if (onClick && typeof onClick === "function") {
          onClick(event);
        }

        if (target) {
          const modal: HTMLScaleModalElement | null | undefined =
            target.closest("scale-modal") as HTMLScaleModalElement;

          if (modal) {
            modal.dispatchEvent(keyboardEscapeEvent);
          }
        }
      }
    }}
    {...props}
  />
);

const keyboardEscapeEvent = new KeyboardEvent("keydown", { key: "Escape" });

const isHTMLScaleButtonElement = (
  element: Element | EventTarget,
): element is HTMLScaleButtonElement =>
  (element as HTMLScaleButtonElement).setFocus !== undefined;

const isHTMLScaleModalElement = (
  element: Element | EventTarget,
): element is HTMLScaleButtonElement =>
  (element as HTMLScaleButtonElement).nodeName === "SCALE-MODAL";

const ModalNamespace = Object.assign(Modal, {
  CloseButton,
});

export default ModalNamespace;
