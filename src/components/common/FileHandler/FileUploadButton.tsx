import React, { useRef } from "react";
import { ScaleButton } from "@telekom/scale-components-react";

interface FileUploadButtonProps {
  onFileSelected: (file: File) => void;
  accept?: string;
  buttonText?: string;
  variant?: "primary" | "secondary" | "tertiary";
  size?: "small" | "large";
}

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  onFileSelected,
  accept = ".csv, .json, .xlsx, .xls",
  buttonText = "Upload File",
  variant = "secondary",
  size = "large",
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      onFileSelected(selectedFile);
    }
  };

  return (
    <>
      <ScaleButton variant={variant} size={size} onClick={handleButtonClick}>
        {buttonText}
      </ScaleButton>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }} // Hide the actual file input
        accept={accept}
        onChange={handleFileChange}
      />
    </>
  );
};

export default FileUploadButton;
