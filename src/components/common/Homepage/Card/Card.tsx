import { ReactNode, createElement } from "react";
import { H4 } from "../../Typography";
import { ScaleCard } from "@telekom/scale-components-react";
import {
  ScaleIconDevicePhoneWithMobilePlan,
  ScaleIconActionLaunch,
  ScaleIconTProductMeasureInternetSpeed,
  ScaleIconActionSearch,
  ScaleIconContentSimCard,
  ScaleIconContentKey,
  ScaleIconProcessSepaTransaction,
  ScaleIconHomeLightBulb,
  ScaleIconServiceMaintanance,
  ScaleIconCommunicationChat,
} from "@telekom/scale-components-react";

const iconMap = {
  ScaleIconDevicePhoneWithMobilePlan,
  ScaleIconActionLaunch,
  ScaleIconTProductMeasureInternetSpeed,
  ScaleIconActionSearch,
  ScaleIconContentSimCard,
  ScaleIconContentKey,
  ScaleIconProcessSepaTransaction,
  ScaleIconHomeLightBulb,
  ScaleIconServiceMaintanance,
  ScaleIconCommunicationChat,
};

interface CardProps {
  iconName: string;
  heading: ReactNode;
  subHeading: ReactNode;
}

const Card = ({ iconName, heading, subHeading }: CardProps) => (
  <ScaleCard>
    <div className="tw-flex tw-flex-col tw-gap-6">
      <div className="tw-flex">
        <div className="tw-flex tw-w-1/5 tw-items-start">
          {createElement(iconMap[iconName], {
            size: 64,
            className: "tw-text-text-&-icon tw-mx-auto",
            "aria-hidden": true,
          })}
        </div>
        <div className="tw-w-4/5 tw-p-4">
          <H4>{heading}</H4>
          <p className="tw-mt-4">{subHeading}</p>
        </div>
      </div>
    </div>
  </ScaleCard>
);
export default Card;
