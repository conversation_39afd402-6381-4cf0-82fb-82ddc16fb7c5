import { ReactNode } from "react";
import { H1, H2, H3 } from "../../Typography"; // import all needed heading components

interface HeadingProps {
  heading: ReactNode;
  description: ReactNode;
  level?: number;
}

const Heading = ({ heading, description, level = 2 }: HeadingProps) => {
  const HeadingTag =
    {
      1: H1,
      2: H2,
      3: H3,
    }[level] || H2; // fallback to H2 if level is not found

  return (
    <div className="tw-p-4">
      <HeadingTag>{heading}</HeadingTag>
      <p className="tw-my-4">{description}</p>
    </div>
  );
};

export default Heading;
