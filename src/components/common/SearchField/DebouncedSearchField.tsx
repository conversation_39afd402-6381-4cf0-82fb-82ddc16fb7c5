import { useEffect, useState } from "react";
import styled from "styled-components";
import {
  ScaleIconActionSearch,
  ScaleTextField,
} from "@telekom/scale-components-react";
import { ComponentProps, Ref } from "react";

const SearchFieldStyled = styled(ScaleTextField)({
  "--height": "var(--telekom-spacing-composition-space-11)",
  "--spacing-control": "0.6rem 0.5rem 0.3rem 2.5rem",
  position: "relative",
  ["& label"]: {
    transform: "translate(2.5rem, 0.6rem)",
  },
  ["& .text-field--has-focus:not(.text-field--readonly) .text-field__label, .animated label.text-field__label"]:
    {
      transform: "translate(2.5rem, 0) !important",
      fontSize: "0.5rem",
    },
});

export const DebouncedSearchField = (
  props: ComponentProps<typeof ScaleTextField> & {
    ref?: Ref<HTMLScaleTextFieldElement>;
  } & {
    onChange: (value: string | number) => void;
    debounce: number;
  } & Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange">,
) => {
  const [value, setValue] = useState("");
  const { onChange, debounce } = props;

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value, onChange, debounce]);

  return (
    <SearchFieldStyled
      {...props}
      value={value}
      onScale-change={(e) => setValue(String(e.target.value))}
    >
      <ScaleIconActionSearch
        style={{ position: "absolute", marginLeft: "10px", marginTop: "6px" }}
        decorative={true}
      />
    </SearchFieldStyled>
  );
};
