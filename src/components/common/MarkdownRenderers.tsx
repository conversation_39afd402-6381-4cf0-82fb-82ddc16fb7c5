import { Components } from "react-markdown";
import { H4, H5, H6 } from "@tessa-portal/components/common/Typography";
import {
  ScaleLink,
  ScaleList,
  ScaleListItem,
} from "@telekom/scale-components-react";

export const markdownRenderers: Components = {
  h2: ({ children }) => <H4>{children}</H4>,
  h3: ({ children }) => <H5>{children}</H5>,
  h4: ({ children }) => <H6>{children}</H6>,
  a: ({ children, href }) => <ScaleLink href={href}>{children}</ScaleLink>,
  ol: ({ children }) => <ScaleList ordered>{children}</ScaleList>,
  ul: ({ children }) => <ScaleList>{children}</ScaleList>,
  li: ({ children }) => <ScaleListItem>{children}</ScaleListItem>,
};
