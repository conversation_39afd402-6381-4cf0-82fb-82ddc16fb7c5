import clsx from "clsx";
import { ReactNode } from "react";

import { Slot } from "@radix-ui/react-slot";

type TypograpyProps = {
  asChild?: boolean;
  className?: string;
  children: ReactNode;
  href?: string;
};

export const Heading = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h1";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-3", className)}
      {...props}
    />
  );
};

export const Heading2 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h2";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-4", className)}
      {...props}
    />
  );
};
export const Heading3 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h3";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-5", className)}
      {...props}
    />
  );
};

export const H1 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h1";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-1", className)}
      {...props}
    />
  );
};

export const H2 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h2";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-2", className)}
      {...props}
    />
  );
};
export const H3 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h3";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-3", className)}
      {...props}
    />
  );
};

export const H4 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h4";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-4", className)}
      {...props}
    />
  );
};

export const H5 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h5";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-5", className)}
      {...props}
    />
  );
};

export const H6 = ({ asChild, className, ...props }: TypograpyProps) => {
  const Comp = asChild ? Slot : "h6";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-6", className)}
      {...props}
    />
  );
};

export function Caption({ asChild, className, ...props }: TypograpyProps) {
  const Comp = asChild ? Slot : "caption";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-caption", className)}
      {...props}
    />
  );
}

export function Label({ asChild, className, ...props }: TypograpyProps) {
  const Comp = asChild ? Slot : "label";

  return (
    <Comp
      className={clsx("scl-font-variant-heading-label", className)}
      {...props}
    />
  );
}

export function Smaller({ asChild, className, ...props }: TypograpyProps) {
  const Comp = asChild ? Slot : "small";

  return (
    <Comp className={clsx("scl-font-variant-smaller", className)} {...props} />
  );
}

type TextProps = TypograpyProps & { variant?: "short" | "large" };

export function Text({ asChild, className, variant, ...props }: TextProps) {
  const Comp = asChild ? Slot : "p";

  return (
    <Comp
      className={clsx(
        !variant && "scl-font-variant-body",
        variant && `scl-font-variant-body-${variant}`,
        className,
      )}
      {...props}
    />
  );
}
