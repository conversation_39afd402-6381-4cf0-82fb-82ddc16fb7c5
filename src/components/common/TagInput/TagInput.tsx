import { useState, KeyboardEvent, useRef, useEffect } from "react";
import styled from "styled-components";
import {
  ScaleIconActionSearch,
  ScaleTag,
} from "@telekom/scale-components-react";

const InputContainer = styled.div`
  position: relative;
  width: 100%;
`;

const Label = styled.label<{
  isFocused?: boolean;
  isDisabled?: boolean;
  hasValue?: boolean;
}>`
  top: 0;
  left: 0;
  color: ${(props) =>
    props.isDisabled
      ? "var(--telekom-color-text-and-icon-disabled)"
      : "var(--telekom-color-text-and-icon-additional)"};
  display: flex;
  z-index: 10;
  position: absolute;
  transition: all var(--telekom-motion-duration-transition);
  pointer-events: none;
  font: var(--telekom-text-style-ui);
  transform: translate(40px, 0.6rem);

  ${(props) =>
    (props.isFocused || props.hasValue) &&
    `
    transform: translate(2.5rem, 0);
    font: var(--telekom-text-style-small);
    font-size: 0.5rem;
  `}
`;

const Control = styled.div<{
  isFocused?: boolean;
  isDisabled?: boolean;
  invalid?: boolean;
  hasValue?: boolean;
}>`
  min-height: var(--telekom-spacing-composition-space-11);
  background-color: var(--telekom-color-ui-state-fill-standard);
  border: var(--telekom-spacing-unit-x025) solid
    var(--telekom-color-ui-border-standard);
  border-radius: var(--telekom-radius-standard);
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  padding: 2px 8px;
  gap: var(--telekom-spacing-unit-x05);

  &:hover {
    background-color: var(--telekom-color-ui-state-fill-hovered);
    border-color: var(--telekom-color-ui-border-hovered);
  }

  ${(props) =>
    props.isFocused &&
    `
    border-color: var(--telekom-color-ui-border-hovered);
    box-shadow: 0 0 0 var(--telekom-line-weight-highlight) var(--telekom-color-functional-focus);
    outline: var(--telekom-line-weight-highlight) solid var(--telekom-color-functional-focus-standard);
    outline-offset: 1px;
  `}

  ${(props) =>
    props.invalid &&
    `
    border: var(--telekom-spacing-unit-x05) solid var(--telekom-color-functional-danger-standard);
  `}

  ${(props) =>
    props.isDisabled &&
    `
    background: var(--telekom-color-ui-fill-disabled);
    border-color: var(--telekom-color-ui-border-disabled);
    color: var(--telekom-color-text-and-icon-disabled);
    cursor: not-allowed;
  `}
`;

const Input = styled.input`
  background: transparent;
  border: 0;
  color: var(--telekom-color-text-and-icon-standard);
  display: inline-block;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  outline: 0;
  padding: 0;
  padding-left: 6px;
  padding-top: var(--telekom-spacing-unit-x2);
  flex: 1 1 0%;
  min-width: 2px;

  &::placeholder {
    color: var(--telekom-color-text-and-icon-disabled);
  }

  &:disabled {
    cursor: not-allowed;
  }
`;

const HelperText = styled.div`
  margin-top: var(--telekom-spacing-composition-space-03);
  color: var(--telekom-color-text-and-icon-functional-danger);
  font: var(--telekom-text-style-small);
`;

export type Tag = {
  id: string;
  label: string;
};

type TagInputProps = {
  value: Tag[];
  onChange: (tags: Tag[]) => void;
  disabled?: boolean;
  label?: string;
  helperText?: string;
  invalid?: boolean;
  onSearch?: (searchTerm: string) => void;
  debounce?: number;
};

export const TagInput = ({
  value,
  onChange,
  disabled = false,
  label = "Search",
  helperText,
  invalid,
  onSearch,
  debounce = 300,
}: TagInputProps) => {
  const [inputValue, setInputValue] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const skipSearchDebounce = useRef(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      const trimmedValue = inputValue.trim();

      if (trimmedValue && !value.some((tag) => tag.label === trimmedValue)) {
        const newTag: Tag = {
          id: `${Date.now()}-${trimmedValue}`,
          label: trimmedValue,
        };
        onChange([...value, newTag]);
        skipSearchDebounce.current = true;
        setInputValue("");
      }
    } else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      onChange(value.slice(0, -1));
    }
  };

  useEffect(() => {
    if (skipSearchDebounce.current) {
      skipSearchDebounce.current = false;
      return;
    }
    const timeout = setTimeout(() => {
      if (onSearch) {
        onSearch(inputValue);
      }
    }, debounce);
    return () => clearTimeout(timeout);
  }, [inputValue, debounce, onSearch]);

  const handleRemove = (tagId: string) => {
    onChange(value.filter((tag) => tag.id !== tagId));
    inputRef.current?.focus();
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  const hasValue = value.length > 0 || inputValue.length > 0;

  return (
    <InputContainer>
      {label && (
        <Label isFocused={isFocused} isDisabled={disabled} hasValue={hasValue}>
          {label}
        </Label>
      )}
      <Control
        isFocused={isFocused}
        isDisabled={disabled}
        invalid={invalid}
        hasValue={hasValue}
        onClick={handleContainerClick}
      >
        <ScaleIconActionSearch
          style={{
            marginBottom: "3px",
          }}
          decorative={true}
        />
        {value.map((tag) => (
          <ScaleTag
            key={tag.id}
            size="small"
            dismissable
            disabled={disabled}
            onScale-close={() => handleRemove(tag.id)}
            dismissText="Remove"
            tabIndex={-1}
          >
            {tag.label}
          </ScaleTag>
        ))}
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          placeholder=""
        />
      </Control>
      {helperText && <HelperText>{helperText}</HelperText>}
    </InputContainer>
  );
};
