import { <PERSON>, useMatches, useParams, useNavigate } from "react-router-dom";
import { ScaleBreadcrumb } from "@telekom/scale-components-react";

type Crumb = {
  name: string;
  to?: string;
};

const Breadcrumbs = () => {
  const params = useParams();
  const matches = useMatches();
  const navigate = useNavigate();

  const handleBreadCrumbClick = (
    e: React.MouseEvent<HTMLScaleBreadcrumbElement, MouseEvent>,
  ) => {
    e.preventDefault();
    const shadowPath = e.nativeEvent.composedPath();
    const anchor = shadowPath[0] as HTMLAnchorElement;
    if (anchor.href) {
      const url = new URL(anchor.href);
      navigate(url.pathname);
    }
  };
  const crumbs: Crumb[] = matches
    .filter(
      (match) =>
        typeof match.handle === "object" &&
        match.handle &&
        "crumb" in match.handle &&
        Boolean(match.handle.crumb),
    )
    .map(
      (match) =>
        typeof match.handle === "object" &&
        match.handle &&
        "crumb" in match.handle &&
        typeof match.handle.crumb === "function" &&
        match.handle.crumb(params),
    );

  return (
    <ScaleBreadcrumb
      separator=">"
      onClick={handleBreadCrumbClick}
      className={"tw-hidden sm:tw-block"}
    >
      {crumbs.map((crumb) =>
        crumb?.to ? (
          <Link key={crumb?.to} to={crumb?.to}>
            {crumb?.name}
          </Link>
        ) : (
          <span key={crumb?.name}>{crumb?.name}</span>
        ),
      )}
    </ScaleBreadcrumb>
  );
};

export default Breadcrumbs;
