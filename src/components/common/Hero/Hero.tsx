import clsx from "clsx";
import { ReactNode } from "react";
import { H1 } from "../Typography";

interface HeroProps {
  backgroundImageUrl?: string;
  heading: ReactNode;
  subHeading: ReactNode;
}

const Hero = ({ backgroundImageUrl, heading, subHeading }: HeroProps) => (
  <div className="tw-relative tw-flex tw-h-[640px] tw-flex-col tw-items-center md:tw-h-[400px]">
    {backgroundImageUrl ? (
      <div
        className="tw-rounded-large tw-absolute tw-inset-x-0 tw-inset-y-0 tw-z-[1] tw-bg-cover tw-bg-[position:0%_30%]"
        style={{ backgroundImage: `url(${backgroundImageUrl})` }}
      />
    ) : (
      <div className="tw-bg-ui-faint tw-absolute tw-inset-x-0 tw-inset-y-0" />
    )}
    <div
      className={clsx(
        backgroundImageUrl && "tw-text-ui-white",
        "tw-relative tw-z-10 tw-grid tw-h-full tw-max-w-3xl tw-flex-col tw-items-center tw-text-center",
      )}
    >
      <div>
        <H1 className="tw-mb-8">{heading}</H1>
        <p className="tw-text-callout">{subHeading}</p>
      </div>
    </div>
  </div>
);

export default Hero;
