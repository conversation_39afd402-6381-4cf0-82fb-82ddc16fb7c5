import {
  ScaleTabHeader,
  ScaleTabNav,
  ScaleTabPanel,
} from "@telekom/scale-components-react";
import { Fragment, ReactElement, useState } from "react";
import { TabProps } from "./Tab";
import styled from "styled-components";

type TabsProps = {
  // block navigation, sets every tab except the active one to disabled
  freeze?: boolean;
  children: ReactElement<TabProps>[];
  activeTab?: number;
  isArchived?: boolean;
};

const StyledScaleTabNav = styled(ScaleTabNav)`
  &::part(tab-nav) {
    padding-left: 3px;
    padding-top: 2px;
  }
`;

export const Tabs = ({
  freeze,
  children,
  activeTab,
  isArchived,
}: TabsProps) => {
  const [activeTabIndex, setActiveTabIndex] = useState(
    (!isArchived && activeTab) || 0,
  );

  return (
    <StyledScaleTabNav>
      {children.map((tab, index) => {
        return tab.props.hidden ? null : (
          <Fragment key={index}>
            <ScaleTabHeader
              slot="tab"
              onScale-select={() => {
                setActiveTabIndex(index);
                tab.props.onClick && tab.props.onClick();
              }}
              onKeyDown={(e) => {
                if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
                  e.stopPropagation();
                  setActiveTabIndex(
                    (index + (e.key === "ArrowLeft" ? -1 : 1)) %
                      children.length,
                  );
                }
              }}
              selected={activeTabIndex === index}
              disabled={activeTabIndex !== index && freeze}
            >
              <>{tab.props.label}</>
            </ScaleTabHeader>
            <ScaleTabPanel
              slot="panel"
              onScale-select={(e: CustomEvent) => e.stopPropagation()}
            >
              {tab.props.children}
            </ScaleTabPanel>
          </Fragment>
        );
      })}
    </StyledScaleTabNav>
  );
};
