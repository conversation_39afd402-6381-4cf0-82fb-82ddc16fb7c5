import styled from "styled-components";

export const StyledRow = styled.tr.withConfig({
  shouldForwardProp: (prop) =>
    !["selectable", "selected", "selectOnlyWithCheckbox"].includes(prop),
})<{
  selected: boolean;
  selectable?: boolean;
  selectOnlyWithCheckbox?: boolean;
}>(
  ({ selected }) =>
    selected && {
      "&": {
        "--background-tbody-tr-hover":
          "var(--telekom-color-background-surface-subtle)",
        backgroundColor: "var(--telekom-color-ui-faint)",
      },
    },
  ({ selectable, selectOnlyWithCheckbox }) =>
    selectable &&
    !selectOnlyWithCheckbox && {
      cursor: "pointer",
    },
);
