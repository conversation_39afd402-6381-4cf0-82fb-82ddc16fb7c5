import { StyledScaleButton } from "../StyledScaleButton";

export interface TableActionsTypes {
  size?: "small" | "large";
  customActions?: {
    label: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    className?: string;
    variant?: string;
    hidden?: boolean;
  }[];
}

export const TableRowActions = (actions: TableActionsTypes) => {
  const { size, customActions } = actions;
  return (
    <div className="tw-flex tw-gap-2">
      {customActions?.map(
        (action) =>
          !action.hidden && (
            <StyledScaleButton
              className={action.className}
              onClick={action.onClick}
              key={action.label}
              variant={action.variant || "secondary"}
              size={size}
            >
              {action.icon}
              {action.label}
            </StyledScaleButton>
          ),
      )}
    </div>
  );
};

export default TableRowActions;
