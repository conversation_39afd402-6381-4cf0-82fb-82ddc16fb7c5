import { Table as ReactTable } from "@tanstack/react-table";
import {
  ScaleMenuFlyout,
  ScaleMenuFlyoutList,
  ScaleMenuFlyoutItem,
  ScaleIconActionMore,
} from "@telekom/scale-components-react";
import { StyledScaleButton } from "../StyledScaleButton";
import { ReactNode } from "react";
export type CustomMenuItem = {
  label: string | ReactNode;
  onClick: () => void;
  closeOnSelect?: boolean;
  subItems?: {
    label: string;
    onClick: () => void;
    closeOnSelect?: boolean;
  }[];
};

interface TableSettingsMenuProps<TData> {
  table: ReactTable<TData>;
  items: ("export" | "columns")[];
  customItems?: CustomMenuItem[];
  exportHandler: (format: "csv" | "json" | "xlsx") => void;
  size?: "small" | "large";
}

export const TableSettingsMenu = <TData,>({
  table,
  items,
  customItems,
  size,
  exportHandler,
}: TableSettingsMenuProps<TData>) => {
  return (
    <div className="tw-m-auto tw-flex tw-justify-center">
      <ScaleMenuFlyout direction="bottom-left">
        <StyledScaleButton
          type="button"
          slot="trigger"
          variant="secondary"
          size={size}
        >
          <ScaleIconActionMore
            accessibility-title="Settings"
            className="tw-mx-auto"
          />
        </StyledScaleButton>
        <ScaleMenuFlyoutList>
          {/* Export data */}
          {items.includes("export") && (
            <ScaleMenuFlyoutItem>
              Export
              <ScaleMenuFlyoutList slot="sublist" closeOnSelect>
                <ScaleMenuFlyoutItem
                  onScale-select={() => exportHandler("xlsx")}
                >
                  excel
                </ScaleMenuFlyoutItem>
                <ScaleMenuFlyoutItem
                  onScale-select={() => exportHandler("json")}
                >
                  json
                </ScaleMenuFlyoutItem>
                <ScaleMenuFlyoutItem
                  onScale-select={() => exportHandler("csv")}
                >
                  csv
                </ScaleMenuFlyoutItem>
              </ScaleMenuFlyoutList>
            </ScaleMenuFlyoutItem>
          )}
          {/* Column selector */}
          {items.includes("columns") && (
            <ScaleMenuFlyoutItem>
              Columns
              <ScaleMenuFlyoutList slot="sublist">
                {table.getAllLeafColumns().map((column) => (
                  <ScaleMenuFlyoutItem
                    key={column.id}
                    checkable="checkbox"
                    checked={column.getIsVisible()}
                    onScale-select={(e) => {
                      e.stopPropagation();
                      column.toggleVisibility(!column.getIsVisible());
                    }}
                  >
                    {column.columnDef.header?.toString()}
                  </ScaleMenuFlyoutItem>
                ))}
              </ScaleMenuFlyoutList>
            </ScaleMenuFlyoutItem>
          )}
          {/* Custom menu items */}
          {customItems?.map((item, index) =>
            item.subItems ? (
              <ScaleMenuFlyoutItem key={index}>
                {item.label}

                <ScaleMenuFlyoutList slot="sublist" closeOnSelect>
                  {item.subItems.map((subItem, subIndex) => (
                    <ScaleMenuFlyoutItem
                      key={subIndex}
                      onScale-select={(e) => {
                        if (
                          subItem.closeOnSelect !== undefined &&
                          !subItem.closeOnSelect
                        ) {
                          e.stopPropagation();
                        }
                        subItem.onClick();
                      }}
                    >
                      {subItem.label}
                    </ScaleMenuFlyoutItem>
                  ))}
                </ScaleMenuFlyoutList>
              </ScaleMenuFlyoutItem>
            ) : (
              <ScaleMenuFlyoutItem
                key={index}
                onScale-select={(e) => {
                  if (item.closeOnSelect !== undefined && !item.closeOnSelect) {
                    e.stopPropagation();
                  }
                  item.onClick();
                }}
              >
                {item.label}
              </ScaleMenuFlyoutItem>
            ),
          )}
        </ScaleMenuFlyoutList>
      </ScaleMenuFlyout>
    </div>
  );
};
