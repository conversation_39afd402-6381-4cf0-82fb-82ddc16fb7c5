import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  useReactTable,
  TableOptions,
  getFilteredRowModel,
  RowSelectionState,
  Updater,
  Row,
} from "@tanstack/react-table";
import {
  ScaleTable,
  ScalePagination,
  ScaleCheckbox,
} from "@telekom/scale-components-react";
import { ComponentProps, ReactNode, useMemo, useState } from "react";
import clsx from "clsx";
import { DebouncedSearchField } from "../SearchField/DebouncedSearchField";
import { StyledRow } from "./StyledRow";
import { TableRowActions, TableActionsTypes } from "./TableRowActions";
import { TableSettingsMenu } from "./TableSettingsMenu";
import type { CustomMenuItem } from "./TableSettingsMenu";
import {
  exportToCSV,
  exportToExcel,
  exportToJSON,
} from "../../../helpers/parsers/fileExporter";
import { TagInput, Tag } from "../TagInput";

interface RowAction<TData> {
  render: (data: TData) => ReactNode;
  key: string;
}

interface RowActions<TData> {
  content?: ReactNode;
  items: RowAction<TData>[];
}

interface TableProps<TData>
  extends Omit<TableOptions<TData>, "getCoreRowModel" | "globalFilterFn">,
    ComponentProps<typeof ScaleTable> {
  rowActions?: RowActions<TData>;
  tableActions?: TableActionsTypes;
  selectableRows?: boolean;
  rowSelection?: RowSelectionState;
  onRowSelectionChange?: (updaterOrValue: Updater<RowSelectionState>) => void;
  enableMultiRowSelection?: boolean;
  showCheckbox?: boolean;
  showTableActions?: boolean;
  showSettingsMenu?: boolean;
  settingsMenuItems?: ("export" | "columns")[];
  customSettingsMenuItems?: CustomMenuItem[];
  searchlabel?: string;
  selectRowsOnlyWithCheckbox?: boolean;
  searchTags?: boolean;
  pageIndex?: number;
  pageSize?: number;
  onPageChange?: (newPageIndex: number) => void;
  onPageSizeChange?: (newPageSize: number) => void;
  rowCount?: number;
}

const Table = <TData extends object>({
  showSort,
  striped,
  styles,
  selectableRows,
  rowSelection: externalRowSelection,
  onRowSelectionChange,
  enableMultiRowSelection,
  showCheckbox,
  rowActions,
  tableActions,
  showTableActions,
  showSettingsMenu,
  settingsMenuItems = [],
  customSettingsMenuItems,
  searchlabel = "Search",
  columns,
  data,
  selectRowsOnlyWithCheckbox,
  searchTags = false,
  pageIndex: externalPageIndex,
  pageSize: externalPageSize = 15,
  onPageChange,
  onPageSizeChange,
  rowCount,
  ...props
}: TableProps<TData>) => {
  const [internalRowSelection, setInternalRowSelection] =
    useState<RowSelectionState>({});
  const [internalPageIndex, setInternalPageIndex] = useState(0);
  const [internalPageSize, setInternalPageSize] = useState(10);

  const isPaginationControlled = externalPageIndex !== undefined;
  const currentPage = externalPageIndex ?? internalPageIndex;
  const pageSize = externalPageSize ?? internalPageSize;
  const totalPages = Math.ceil((rowCount ?? data.length) / pageSize);

  const isControlled = externalRowSelection !== undefined;
  const rowSelection = isControlled
    ? externalRowSelection
    : internalRowSelection;
  const [globalFilter, setGlobalFilter] = useState({
    searchTerm: "",
    tags: [] as Tag[],
  });
  const { searchTerm, tags } = globalFilter;

  const handleTagChange = (newTags: Tag[]) => {
    setGlobalFilter((prev) => {
      const isAddingTag = newTags.length > prev.tags.length;
      return {
        tags: newTags,
        searchTerm: isAddingTag ? "" : prev.searchTerm,
      };
    });
  };

  const handleSearchChange = (value: string) => {
    setGlobalFilter((prev) => ({ ...prev, searchTerm: value }));
  };

  const customGlobalFilterFn = useMemo(() => {
    const fn = (
      row: Row<TData>,
      _columnId: string,
      filterValue: { searchTerm: string; tags: Tag[] },
    ): boolean => {
      const { searchTerm: currentSearchTerm, tags: currentTags } = filterValue;
      // Check if row matches any tag
      const tagMatch =
        currentTags.length === 0 ||
        currentTags.every((tag) => {
          const lowerTag = tag.label.toLowerCase().trim();
          if (lowerTag === "") return true;
          return row.getAllCells().some((cell) => {
            const cellValue = cell.getValue();
            const cellStringValue =
              cellValue === null || cellValue === undefined
                ? ""
                : String(cellValue).toLowerCase();
            return cellStringValue.includes(lowerTag);
          });
        });

      // Check if row matches search term
      const searchMatch =
        !currentSearchTerm ||
        currentSearchTerm.trim() === "" ||
        row.getAllCells().some((cell) => {
          const cellValue = cell.getValue();
          const cellStringValue =
            cellValue === null || cellValue === undefined
              ? ""
              : String(cellValue).toLowerCase();
          return cellStringValue.includes(
            currentSearchTerm.toLowerCase().trim(),
          );
        });
      return tagMatch && searchMatch;
    };
    return fn;
  }, []);

  const columnsWithSelection = useMemo(() => {
    let cols = columns;

    if (selectableRows && showCheckbox) {
      cols = [
        {
          id: "select",
          header: ({ table }) => (
            <ScaleCheckbox
              checked={table.getIsAllPageRowsSelected()}
              indeterminate={table.getIsSomePageRowsSelected()}
              onScale-change={table.getToggleAllPageRowsSelectedHandler()}
            />
          ),
          cell: ({ row }) => (
            <ScaleCheckbox
              checked={row.getIsSelected()}
              disabled={!row.getCanSelect()}
              indeterminate={row.getIsSomeSelected()}
              onScale-change={row.getToggleSelectedHandler()}
            />
          ),
        },
        ...cols,
      ];
    }

    if (rowActions) {
      cols = [
        ...cols,
        {
          id: "actions",
          header: () => <>{rowActions.content || "Actions"}</>,
          cell: ({ row }) => (
            <span className="tw-flex tw-gap-3">
              {rowActions.items.map((action) => (
                <span key={action.key}>{action.render(row.original)}</span>
              ))}
            </span>
          ),
        },
      ];
    }

    return cols;
  }, [columns, selectableRows, showCheckbox, rowActions]);

  const handleExport = (format: "json" | "csv" | "xlsx") => {
    const filteredData = table
      .getFilteredRowModel()
      .rows.map((row) => row.original);
    switch (format) {
      case "json":
        exportToJSON(filteredData, "TableData");
        break;
      case "csv":
        exportToCSV(filteredData, "TableData");
        break;
      case "xlsx":
        exportToExcel(filteredData, "TableData");
        break;
      default:
        console.error("Unsupported export format");
    }
  };

  const handleRowSelectionChange = (
    updaterOrValue: Updater<RowSelectionState>,
  ) => {
    const newState =
      typeof updaterOrValue === "function"
        ? updaterOrValue(rowSelection)
        : updaterOrValue;

    onRowSelectionChange?.(newState);

    if (!isControlled) {
      setInternalRowSelection(newState);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (isPaginationControlled) {
      onPageChange?.(newPage);
    } else {
      setInternalPageIndex(newPage);
    }
  };

  const handlePageSizeChange = (newSize: number) => {
    if (isPaginationControlled) {
      onPageSizeChange?.(newSize);
    } else {
      setInternalPageSize(newSize);
    }
  };

  // Slice the data for the current page
  const paginatedData = useMemo(() => {
    const start = currentPage * pageSize;
    const end = start + pageSize;
    return data.slice(start, end);
  }, [data, currentPage, pageSize]);

  const table = useReactTable({
    ...props,
    data: paginatedData,
    columns: columnsWithSelection,
    state: {
      rowSelection,
      globalFilter: globalFilter,
      columnVisibility: props.initialState?.columnVisibility,
      pagination: {
        pageIndex: currentPage,
        pageSize,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newState = updater({
          pageIndex: currentPage,
          pageSize,
        });
        handlePageChange(newState.pageIndex);
        handlePageSizeChange(newState.pageSize);
      } else {
        handlePageChange(updater.pageIndex);
        handlePageSizeChange(updater.pageSize);
      }
    },
    manualPagination: true,
    pageCount: totalPages,
    enableMultiRowSelection: !!enableMultiRowSelection,
    enableRowSelection: !!selectableRows,
    onRowSelectionChange: handleRowSelectionChange,
    globalFilterFn: customGlobalFilterFn,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  return (
    <>
      <div className="tw-flex tw-flex-none tw-justify-between tw-pb-4">
        <div className="tw-flex tw-gap-2">
          {showTableActions && (
            <TableRowActions
              {...tableActions}
              size={
                tableActions?.size === undefined ? "small" : tableActions.size
              }
            />
          )}
        </div>
        <div className="tw-flex tw-gap-2">
          {searchTags ? (
            <TagInput
              value={tags}
              onChange={handleTagChange}
              label={searchlabel}
              onSearch={handleSearchChange}
            />
          ) : (
            <DebouncedSearchField
              value={searchTerm}
              onChange={(value) => handleSearchChange(String(value))}
              label={searchlabel}
              debounce={300}
            />
          )}
          {showSettingsMenu && (
            <TableSettingsMenu
              items={settingsMenuItems}
              customItems={customSettingsMenuItems}
              table={table}
              exportHandler={handleExport}
              size={
                tableActions?.size === undefined ? "small" : tableActions?.size
              }
            />
          )}
        </div>
      </div>
      <ScaleTable showSort={showSort} striped={striped} styles={styles}>
        <table>
          <thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  let ariaSort: "ascending" | "descending" | "none" | undefined;

                  if (header.column.getCanSort()) {
                    if (header.column.getIsSorted() === "asc") {
                      ariaSort = "ascending";
                    } else if (header.column.getIsSorted() === "desc") {
                      ariaSort = "descending";
                    } else {
                      ariaSort = "none";
                    }
                  }

                  return (
                    <th
                      aria-sort={ariaSort}
                      key={header.id}
                      onClick={
                        header.column.getCanSort()
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                    >
                      {header.isPlaceholder ? null : (
                        <>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                        </>
                      )}
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row) => (
              <StyledRow
                selected={row.getIsSelected()}
                selectable={row.getCanSelect()}
                selectOnlyWithCheckbox={selectRowsOnlyWithCheckbox}
                key={row.id}
                onClick={(e) => {
                  e.stopPropagation();
                  if (!selectRowsOnlyWithCheckbox && row.getCanSelect()) {
                    row.toggleSelected();
                  }
                }}
                onKeyDown={(e) => {
                  if (
                    !selectRowsOnlyWithCheckbox &&
                    row.getCanSelect() &&
                    (e.key === "Enter" || e.key === " ")
                  ) {
                    e.preventDefault();
                    row.toggleSelected();
                  }
                }}
                tabIndex={0}
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className={clsx(
                      "tw-max-w-generic-size-20 tw-overflow-hidden",
                      !cell.column.columnDef.meta?.["disableEllipses"] &&
                        "tw-text-ellipsis tw-whitespace-nowrap",
                    )}
                    style={{
                      width: cell.column.columnDef.meta?.width,
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </StyledRow>
            ))}
          </tbody>
        </table>
      </ScaleTable>
      {table.getState().pagination && table.getRowCount() !== 0 && (
        <ScalePagination
          className="tw-mt-6 tw-flex tw-justify-center"
          pageSize={pageSize}
          startElement={currentPage * pageSize}
          totalElements={rowCount ?? data.length}
          onScale-pagination={(event) => {
            let newPage = currentPage;
            switch (event.detail.direction) {
              case "NEXT":
                newPage = Math.min(currentPage + 1, totalPages - 1);
                break;
              case "PREVIOUS":
                newPage = Math.max(currentPage - 1, 0);
                break;
              case "FIRST":
                newPage = 0;
                break;
              case "LAST":
                newPage = totalPages - 1;
                break;
            }
            handlePageChange(newPage);
          }}
          style={{
            marginTop: "var(--telekom-spacing-composition-space-06)",
            display: "flex",
            justifyContent: "center",
          }}
        />
      )}
    </>
  );
};

export default Table;
