import { ReactNode } from "react";
import { Heading } from "../Typography";
import { MaxGridWidth } from "@tessa-portal/components/common/Main/MaxGridWidth";
import Breadcrumbs from "@tessa-portal/components/common/Breadcrumbs";
import { ScaleIconUserFileUser } from "@telekom/scale-components-react";
import useUser from "@tessa-portal/hooks/useUser";

type MainProps = {
  actions?: ReactNode;
  children: ReactNode;
  heading?: ReactNode;
  breadcrumbs?: boolean;
};

const Main = ({
  actions,
  children,
  heading,
  breadcrumbs = true,
}: MainProps) => {
  const { name } = useUser();
  return (
    <MaxGridWidth>
      {(heading || actions || breadcrumbs) && (
        <div className="tw-my-4 -tw-ml-8 -tw-mr-8 2xl:-tw-ml-0 2xl:-tw-mr-0">
          <div className="tw-px-6 lg:tw-px-8 2xl:tw-px-0">
            {breadcrumbs && <Breadcrumbs />}
            <div className="tw-flex tw-flex-col tw-items-center tw-justify-between sm:tw-flex-row">
              <div className="tw-grow">
                {heading && (
                  <Heading className="tw-my-0 tw-mb-0">{heading}</Heading>
                )}
              </div>
              <div className="tw-mr-2 tw-flex tw-grow-0 tw-space-x-3">
                <ScaleIconUserFileUser />
                <div>{name}</div>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="tw-mb-8">{children}</div>
    </MaxGridWidth>
  );
};

export default Main;
