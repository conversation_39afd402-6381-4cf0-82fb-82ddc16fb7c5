import { ScaleButton } from "@telekom/scale-components-react";

interface IconButtonProps {
  icon: React.ReactNode;
  label?: string;
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  variant?: "primary" | "secondary";
  size?: "small" | "large";
}

export const IconButton = ({
  label,
  icon,
  onClick,
  disabled,
  className,
  variant = "secondary",
  size = "large",
}: IconButtonProps) => {
  return (
    <div className="tw-flex tw-flex-col tw-items-center tw-justify-center">
      <ScaleButton
        onClick={onClick}
        disabled={disabled}
        className={className}
        variant={variant}
        size={size}
        iconOnly
      >
        {icon}
      </ScaleButton>
      <span className="tw-text-center tw-text-xs">{label}</span>
    </div>
  );
};

export default IconButton;
