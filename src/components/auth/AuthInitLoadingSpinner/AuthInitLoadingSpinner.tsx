import { ReactNode } from "react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import { ScaleLoadingSpinner } from "@telekom/scale-components-react";
import styled from "styled-components";

const FullScreenLoadingSpinnerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: var(--telekom-color-background-canvas);
  z-index: 100;
`;

type FullScreenLoadingSpinnerProps = {
  text: string;
};

const FullScreenLoadingSpinner = ({ text }: FullScreenLoadingSpinnerProps) => (
  <FullScreenLoadingSpinnerContainer>
    <ScaleLoadingSpinner size="large" text={text} alignment="vertical" />
  </FullScreenLoadingSpinnerContainer>
);

type AuthInitLoadingSpinnerProps = {
  children: ReactNode;
};

const AuthInitLoadingSpinner = ({ children }: AuthInitLoadingSpinnerProps) => {
  const auth = useAuth();
  const isInIframe = window.top !== window.self;

  // Handle iframe context
  if (isInIframe) {
    if (auth.isLoading) {
      return <FullScreenLoadingSpinner text="Initializing authentication..." />;
    }
    return <>{children}</>;
  }

  // Handle standalone context
  if ("activeNavigator" in auth) {
    switch (auth.activeNavigator) {
      case "signinSilent":
        return <FullScreenLoadingSpinner text="Signing you in..." />;
      case "signoutRedirect":
        return <FullScreenLoadingSpinner text="Signing you out..." />;
    }

    if (!auth.activeNavigator && auth.isLoading) {
      return (
        <FullScreenLoadingSpinner text="Initializing Single Sign-On (SSO)..." />
      );
    }
  }

  return <>{children}</>;
};

export default AuthInitLoadingSpinner;
