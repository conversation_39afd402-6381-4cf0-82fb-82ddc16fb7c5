import { createContext, useState, useEffect, ReactNode } from "react";
import { User as OidcUser } from "oidc-client-ts";
import { RawUserProfile } from "@tessa-portal/types/userProfile";
import { jwtDecode } from "jwt-decode";

// Shared auth interface (mimics react-oidc-context)
interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  accessToken?: string;
  user?: Partial<OidcUser>;
  signinRedirect?: () => void;
  signoutRedirect?: () => void;
  removeUser: () => Promise<void>;
  events: {
    addAccessTokenExpired: (callback: () => void) => void;
    removeAccessTokenExpired: (callback: () => void) => void;
  };
}

// Token-based fallback (iframe)
export const TokenRelayAuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  removeUser: async () => {},
  events: {
    addAccessTokenExpired: () => {},
    removeAccessTokenExpired: () => {},
  },
});

const STORAGE_KEY = "tessa_iframe_auth";

interface DecodedToken {
  exp: number;
}

const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = jwtDecode<DecodedToken>(token);
    return decoded.exp * 1000 < Date.now();
  } catch (error) {
    console.error("Error decoding token:", error);
    return true;
  }
};

const clearAuthState = (
  setAccessToken: (token: string | undefined) => void,
  setIdToken: (token: string | undefined) => void,
  setUserProfile: (profile: RawUserProfile | undefined) => void,
  setIsAuthenticated: (isAuthenticated: boolean) => void,
) => {
  setAccessToken(undefined);
  setIdToken(undefined);
  setUserProfile(undefined);
  setIsAuthenticated(false);
  localStorage.removeItem(STORAGE_KEY);
};

export const TokenRelayAuthProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [accessToken, setAccessToken] = useState<string>();
  const [idToken, setIdToken] = useState<string>();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<RawUserProfile>();

  useEffect(() => {
    // Try to load tokens from localStorage first
    const storedAuth = localStorage.getItem(STORAGE_KEY);
    if (storedAuth) {
      try {
        const {
          accessToken: storedAccessToken,
          idToken: storedIdToken,
          profile: storedProfile,
        } = JSON.parse(storedAuth);

        // Check if tokens are expired
        if (storedAccessToken && isTokenExpired(storedAccessToken)) {
          console.log("Stored token is expired, clearing auth state");
          clearAuthState(
            setAccessToken,
            setIdToken,
            setUserProfile,
            setIsAuthenticated,
          );
          setIsLoading(false);
          return;
        }

        setAccessToken(storedAccessToken);
        setIdToken(storedIdToken);
        if (storedProfile) {
          setUserProfile(storedProfile);
        }
        setIsAuthenticated(true);
        setIsLoading(false);

        // Set up expiration timer
        if (storedAccessToken) {
          const decoded = jwtDecode<DecodedToken>(storedAccessToken);
          const expirationTime = decoded.exp * 1000;
          const timeUntilExpiration = expirationTime - Date.now();

          if (timeUntilExpiration > 0) {
            const timer = setTimeout(() => {
              console.log("Token expired, clearing auth state");
              clearAuthState(
                setAccessToken,
                setIdToken,
                setUserProfile,
                setIsAuthenticated,
              );
            }, timeUntilExpiration);

            return () => clearTimeout(timer);
          } else {
            // Token is already expired
            clearAuthState(
              setAccessToken,
              setIdToken,
              setUserProfile,
              setIsAuthenticated,
            );
          }
        }
      } catch (error) {
        console.error("Error parsing stored auth:", error);
        localStorage.removeItem(STORAGE_KEY);
        setIsLoading(false);
      }
    } else {
      // If no stored tokens, we're not loading anymore
      setIsLoading(false);
    }

    const onMessage = (event: MessageEvent) => {
      if (event.data?.type === "AUTH_SUCCESS") {
        const {
          accessToken: newAccessToken,
          idToken: newIdToken,
          profile: newProfile,
        } = event.data;

        // Check if new token is expired
        if (newAccessToken && isTokenExpired(newAccessToken)) {
          console.log("Received token is expired");
          return;
        }

        setAccessToken(newAccessToken);
        setIdToken(newIdToken);
        if (newProfile) {
          setUserProfile(newProfile);
        }
        setIsAuthenticated(true);
        setIsLoading(false);

        // Store tokens in localStorage
        localStorage.setItem(
          STORAGE_KEY,
          JSON.stringify({
            accessToken: newAccessToken,
            idToken: newIdToken,
            profile: newProfile,
          }),
        );

        // Set up expiration timer for new token
        if (newAccessToken) {
          const decoded = jwtDecode<DecodedToken>(newAccessToken);
          const expirationTime = decoded.exp * 1000;
          const timeUntilExpiration = expirationTime - Date.now();

          if (timeUntilExpiration > 0) {
            const timer = setTimeout(() => {
              console.log("Token expired, clearing auth state");
              clearAuthState(
                setAccessToken,
                setIdToken,
                setUserProfile,
                setIsAuthenticated,
              );
            }, timeUntilExpiration);

            return () => clearTimeout(timer);
          }
        }
      }
    };

    window.addEventListener("message", onMessage);

    return () => window.removeEventListener("message", onMessage);
  }, []);

  const handleSigninRedirect = () => {
    // Open login in a new window
    window.open("/login-redirect", "_blank");
  };

  const handleSignoutRedirect = () => {
    clearAuthState(
      setAccessToken,
      setIdToken,
      setUserProfile,
      setIsAuthenticated,
    );
    // You might want to notify the parent window about logout
    window.top?.postMessage({ type: "AUTH_LOGOUT" }, "*");
  };

  const removeUser = async () => {
    clearAuthState(
      setAccessToken,
      setIdToken,
      setUserProfile,
      setIsAuthenticated,
    );
    return Promise.resolve();
  };

  const tokenExpiredCallbacks: (() => void)[] = [];

  return (
    <TokenRelayAuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        accessToken,
        user: accessToken
          ? {
              access_token: accessToken,
              id_token: idToken,
              profile: userProfile as any,
            }
          : undefined,
        signinRedirect: handleSigninRedirect,
        signoutRedirect: handleSignoutRedirect,
        removeUser,
        events: {
          addAccessTokenExpired: (callback) => {
            tokenExpiredCallbacks.push(callback);
          },
          removeAccessTokenExpired: (callback) => {
            const index = tokenExpiredCallbacks.indexOf(callback);
            if (index > -1) {
              tokenExpiredCallbacks.splice(index, 1);
            }
          },
        },
      }}
    >
      {children}
    </TokenRelayAuthContext.Provider>
  );
};
