import { ReactNode } from "react";
import { AuthProvider as OIDCAuthProvider } from "react-oidc-context";
import { oidcConfig } from "../../../oidcConfig";
import { TokenRelayAuthProvider } from "./UniversalAuthContext";

const isInIframe = window.self !== window.top;

export const UniversalAuthProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  return isInIframe ? (
    <TokenRelayAuthProvider>{children}</TokenRelayAuthProvider>
  ) : (
    <OIDCAuthProvider {...oidcConfig}>{children}</OIDCAuthProvider>
  );
};
