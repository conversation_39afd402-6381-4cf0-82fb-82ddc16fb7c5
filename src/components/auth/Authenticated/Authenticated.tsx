import { ReactNode, useEffect, useState } from "react";
import { useAuth } from "@tessa-portal/components/auth/UniversalAuthProvider/useUniversalAuth";
import useUser from "@tessa-portal/hooks/useUser";
import styled from "styled-components";
import { ScaleLoadingSpinner } from "@telekom/scale-components-react";
import IframeLogin from "@tessa-portal/pages/Redirect/IframeLogin";
import { withAuthenticationRequired } from "react-oidc-context";

type LoadingSpinnerProps = {
  text: string;
};

const CenteredLoadingSpinnerContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
`;

const CenteredLoadingSpinner = ({ text }: LoadingSpinnerProps) => (
  <CenteredLoadingSpinnerContainer>
    <ScaleLoadingSpinner size="large" text={text} alignment="vertical" />
  </CenteredLoadingSpinnerContainer>
);

type AuthenticatedProps = {
  children: ReactNode;
  roles?: string[];
  users?: string[];
};

const Authenticated = ({
  children,
  roles = [],
  users = [],
}: AuthenticatedProps) => {
  const { roles: currentUserRoles, preferred_username: currentUser } =
    useUser();
  const auth = useAuth();
  const isInIframe = window.top !== window.self;
  const [isFrameAuthenticated, setIsFrameAuthenticated] = useState(false);

  useEffect(() => {
    const handleLoginMessage = (event: MessageEvent) => {
      if (event.data?.type === "AUTH_SUCCESS") {
        if (event.data.accessToken && event.data.idToken) {
          setIsFrameAuthenticated(true);
        }
      }
    };

    window.addEventListener("message", handleLoginMessage);

    if (auth.isAuthenticated) {
      setIsFrameAuthenticated(true);
    }

    return () => {
      window.removeEventListener("message", handleLoginMessage);
    };
  }, [auth.isAuthenticated]);

  // Show loading spinner during initial load
  if (auth.isLoading) {
    return <CenteredLoadingSpinner text="Loading..." />;
  }

  if (isInIframe) {
    if (!isFrameAuthenticated && !auth.isAuthenticated) {
      return <IframeLogin />;
    }
  }

  if (!currentUser) {
    return <CenteredLoadingSpinner text="Loading user data..." />;
  }

  const isRoleRestricted = roles.length > 0;
  const userHasRole = roles.some((r) => currentUserRoles.includes(r));
  const isUserRestricted = users.length > 0;
  const isAllowedUser = users.includes(currentUser);

  if (
    (isRoleRestricted && !userHasRole) ||
    (isUserRestricted && !isAllowedUser)
  ) {
    return null;
  }

  return <>{children}</>;
};

// Only wrap with withAuthenticationRequired when not in an iframe
const isInIframe = window.top !== window.self;
const EnhancedAuthenticated = isInIframe
  ? Authenticated
  : withAuthenticationRequired(Authenticated, {
      OnRedirecting: () => <CenteredLoadingSpinner text="Loading..." />,
      signinRedirectArgs: {
        redirect_uri: window.location.origin + window.location.pathname,
      },
    });

export default EnhancedAuthenticated;
