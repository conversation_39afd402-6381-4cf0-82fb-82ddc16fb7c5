import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";
import path from "path";

export default defineConfig({
  plugins: [svgr(), react()],
  resolve: {
    alias: {
      "@tessa-portal": path.resolve(__dirname, "src"),
      "@tessa-portal/*": path.resolve(__dirname, "src/*"),
    },
  },
  optimizeDeps: {
    include: ["@telekom/scale-components-react", "@telekom/scale-components"],
  },
  server: {
    port: 5001,
    hmr: {
      host: "localhost",
      protocol: "ws",
    },
  },
  preview: {
    port: 5001,
  },
});
