# Stage 1: Build the application
FROM docker01.its/cache/library/node:21-alpine3.20 AS build-stage

ARG ARTIFACTORY_TOKEN

WORKDIR /tessa-portal

# Debug versions
RUN echo "Node.js version:" && node -v
RUN echo "npm version:" && npm -v

# Copy dependencies first so this layer only gets rebuilt when necessary
COPY package*.json ./
COPY .npmrc ./

# Install dependencies
RUN npm ci

# Debug installed dependencies
RUN echo "Installed npm packages:" && npm list --depth=0

# Copy the rest of the source code
COPY . .

RUN npm run build

FROM docker01.its/cache/library/nginx:stable-alpine3.20 AS production-stage

# Delete the default Nginx configuration file
RUN rm -rf /etc/nginx/conf.d/default.conf

# Copy the build artifacts from the build stage
COPY --from=build-stage /tessa-portal/dist /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Start Nginx and serve the application
CMD ["nginx",  "-g",  "daemon off;"]
