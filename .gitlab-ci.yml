stages:
  - prepare
  - test
  - build
  - push
  - release
  - deploy

version:
  stage: prepare
  tags:
    - docker
  image:
    name: docker01.its/cache/orhunp/git-cliff:2.7.0
    entrypoint: [""]
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  script:
    - |
      # Get versions using git-cliff
      LAST_RELEASE_VERSION=$(git-cliff --bumped-version --latest)
      echo "LAST_RELEASE_VERSION=$LAST_RELEASE_VERSION" >> version.env
      NEXT_RELEASE_VERSION=$(git-cliff --bumped-version)
      echo "NEXT_RELEASE_VERSION=$NEXT_RELEASE_VERSION" >> version.env

      if [ "$CI_COMMIT_REF_NAME" == "$CI_DEFAULT_BRANCH" ]; then
        VERSION="$NEXT_RELEASE_VERSION"
      elif [ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]; then
        VERSION="$NEXT_RELEASE_VERSION-mr.${CI_MERGE_REQUEST_IID}.${CI_MERGE_REQUEST_DIFF_ID}"
      else
        VERSION="$NEXT_RELEASE_VERSION-branch.${CI_COMMIT_SHORT_SHA}"
      fi

      echo "VERSION=$VERSION" >> version.env
      echo "Computed pipeline version: $VERSION"
      echo "last release version $LAST_RELEASE_VERSION"
      echo "next release version $NEXT_RELEASE_VERSION"
  artifacts:
    reports:
      dotenv: version.env
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

conventional_commits:
  stage: prepare
  tags:
    - docker
  image: docker01.its/cache/library/alpine:3.20
  needs:
    - version
  script:
    - echo "Checking conventional commits..."
    - echo "LAST_RELEASE_VERSION=$LAST_RELEASE_VERSION"
    - echo "NEXT_RELEASE_VERSION=$NEXT_RELEASE_VERSION"
    - |
      if [ "$NEXT_RELEASE_VERSION" == "$LAST_RELEASE_VERSION" ]; then
        echo "Error: Merge request does not bump version"
        echo "PLEASE MAKE SURE YOUR COMMIT MESSAGES ADHERE TO THE CONVENTIONAL COMMIT STANDARD"
        echo "Valid commit types that trigger version bumps:"
        echo "- feat: Minor version bump"
        echo "- fix: Patch version bump"
        echo "- perf: Patch version bump"
        echo "- BREAKING CHANGE: Major version bump"
        exit 1
      fi
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'

lint:
  stage: test
  tags:
    - docker
  image: docker01.its/cache/library/node:21-alpine3.20
  script:
    - node -v
    - npm ci
    - npm run lint
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

build:
  stage: build
  tags:
    - docker
  needs:
  - job: version
    artifacts: true
  - lint
  image: docker01.its/cache/library/node:21-alpine3.20
  script:
    - echo "VERSION is $VERSION"
    - npm ci
    - npm run build
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

docker-build-and-push:
  stage: build
  tags:
    - shell
  needs:
    - job: version
      artifacts: true
    - build
  before_script:
    - echo ${REGISTRY_PASSWORD} | docker login -u ${REGISTRY_USER} --password-stdin ${REGISTRY}
    - echo "${COSIGN_PRIVATE_KEY}" > cosign.key
    - chmod 600 cosign.key
  script:
    - |
      echo "Building & pushing Docker image with tag: ${VERSION}"
    # Build the image
    - docker build
        --tag $REGISTRY/$ARTIFACT_NAME:${VERSION}
        --tag $REGISTRY/$ARTIFACT_NAME:latest
        --build-arg ARTIFACTORY_TOKEN="${ARTIFACTORY_TOKEN}"
        --file Dockerfile .
    # Push the versioned image
    - docker push ${REGISTRY}/${ARTIFACT_NAME}:latest
    # Extract and sign the image digest
    - IMAGE_DIGEST=$(docker image push ${REGISTRY}/${ARTIFACT_NAME}:${VERSION} | awk '/sha256/ {print $3}')
    - cosign sign --key cosign.key "${REGISTRY}/${ARTIFACT_NAME}@${IMAGE_DIGEST}"

  after_script:
    - docker logout ${REGISTRY}
    - rm -v cosign.key
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

changelog:
  tags:
    - docker
  image:
    name: docker01.its/cache/orhunp/git-cliff:2.7.0
    entrypoint: [""]
  variables:
    GIT_STRATEGY: clone
    GIT_DEPTH: 0
  stage: release
  needs:
    - job: version
      artifacts: true
    - docker-build-and-push
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
  script:
    - echo "Generating changelog from $LAST_RELEASE_VERSION to HEAD..."
    - git-cliff --tag "$VERSION" "${LAST_RELEASE_VERSION}..HEAD" > changelog.md
  artifacts:
    paths:
      - changelog.md

release:
  tags:
    - docker
  image: registry.gitlab.com/gitlab-org/release-cli:v0.20.0
  stage: release
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - job: version
      artifacts: true
    - changelog
    - docker-build-and-push
  script:
    - echo "Creating release for version $VERSION"
  release:
    tag_name: $VERSION
    description: |
      # Release $VERSION

      $(cat changelog.md)
    ref: $CI_COMMIT_SHA

deploy:
  stage: deploy
  tags:
    - tessa
  needs:
    - docker-build-and-push
    - job: version
      artifacts: true
  before_script:
    - echo "${REGISTRY_PASSWORD}" | docker login -u "${REGISTRY_USER}" --password-stdin "${REGISTRY}"
  script:
    - |
      echo "Deploying Docker image with tag: $VERSION to environment."
    - cd /opt/tessa/portal
    - echo "Pulling and starting the new version of the portal ${VERSION}"
    - export PORTAL_TAG="${VERSION}"
    - docker compose pull tessa-portal
    - docker compose up -d
  after_script:
    - docker logout "${REGISTRY}"
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
  when: manual
