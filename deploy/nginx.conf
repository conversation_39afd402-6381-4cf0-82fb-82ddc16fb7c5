
server {
  listen 5005;
  server_name localhost;

  # Set default headers for all responses
  add_header X-Content-Type-Options "nosniff" always;

  location / {
    proxy_pass http://host.docker.internal:5001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    add_header Cache-Control "no-cache, no-store, must-revalidate" always;
  }

  # Proxy API requests
  location /wwtcApi/ {
    proxy_pass https://wwtc.test/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header X-Forwarded-Host $http_host;
    proxy_ssl_server_name on;
    proxy_redirect http:// https://;
  }

  # BFF API proxy (STF Rest, yealink)
  location /api/ {
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_pass http://tessa-portal-bff:8000/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
  }

  # STF websocket proxy
  location /stfWs/ {
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host;
    proxy_pass http://tessa-portal-bff:8001/stfWs/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
}

  # STF device screen view proxy
  location /stfDisplay/ {
    proxy_pass http://**************/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
  }

  # ios proxy
  location /iosproxy/ {
    proxy_pass http://ios.test/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
  }

  # Handle WebSocket connections for Vite HMR
  location /sockjs-node/ {
    proxy_pass http://host.docker.internal:5001/sockjs-node/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
  }
}
