name: tessa-portal
services:
  tessa-portal:
    container_name: tessa-portal
    image: docker01.its/cache/library/nginx:stable-alpine3.20
    networks:
      - tessa-portal-network
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - 5005:5005

  tessa-portal-bff:
    image: docker01.its/tessa/tessa-portal-bff:0.0.5
    init: true
    networks:
      - tessa-portal-network
    environment:
      STF_URL: stf.test
      REST_PORT: 8000
      WS_PORT: 8001
      TOKEN_CHECK_URL: https://auth.its-telekom.eu/realms/wwtc/protocol/openid-connect/userinfo
      STF_LOGIN_CRED_1: tessa
      STF_LOGIN_CRED_2: <EMAIL>
      YEALINK_USERNAME: admin
      YEALINK_PASSWORD: admin

networks:
  tessa-portal-network:
    name: tessa-portal-network
